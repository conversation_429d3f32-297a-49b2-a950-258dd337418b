/**
 * Accessibility utilities and hooks for better UX
 */

import { useEffect, useState, useRef } from 'react';

// Screen reader announcements
export function announceToScreenReader(message: string, priority: 'polite' | 'assertive' = 'polite') {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.setAttribute('class', 'sr-only');
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

// Focus management
export function useFocusManagement() {
  const focusRef = useRef<HTMLElement | null>(null);
  
  const setFocus = (element: HTMLElement | null) => {
    focusRef.current = element;
    if (element) {
      element.focus();
    }
  };
  
  const restoreFocus = () => {
    if (focusRef.current) {
      focusRef.current.focus();
    }
  };
  
  return { setFocus, restoreFocus };
}

// Keyboard navigation
export function useKeyboardNavigation(
  items: any[],
  onSelect: (item: any, index: number) => void,
  options: {
    vertical?: boolean;
    loop?: boolean;
    disabled?: boolean;
  } = {}
) {
  const [activeIndex, setActiveIndex] = useState(-1);
  const { vertical = true, loop = true, disabled = false } = options;
  
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (disabled || items.length === 0) return;
    
    const { key } = event;
    let newIndex = activeIndex;
    
    if (vertical) {
      if (key === 'ArrowDown') {
        newIndex = activeIndex < items.length - 1 ? activeIndex + 1 : loop ? 0 : activeIndex;
        event.preventDefault();
      } else if (key === 'ArrowUp') {
        newIndex = activeIndex > 0 ? activeIndex - 1 : loop ? items.length - 1 : activeIndex;
        event.preventDefault();
      }
    } else {
      if (key === 'ArrowRight') {
        newIndex = activeIndex < items.length - 1 ? activeIndex + 1 : loop ? 0 : activeIndex;
        event.preventDefault();
      } else if (key === 'ArrowLeft') {
        newIndex = activeIndex > 0 ? activeIndex - 1 : loop ? items.length - 1 : activeIndex;
        event.preventDefault();
      }
    }
    
    if (key === 'Enter' || key === ' ') {
      if (activeIndex >= 0 && activeIndex < items.length) {
        onSelect(items[activeIndex], activeIndex);
        event.preventDefault();
      }
    }
    
    if (key === 'Home') {
      newIndex = 0;
      event.preventDefault();
    } else if (key === 'End') {
      newIndex = items.length - 1;
      event.preventDefault();
    }
    
    if (newIndex !== activeIndex) {
      setActiveIndex(newIndex);
    }
  };
  
  return {
    activeIndex,
    setActiveIndex,
    handleKeyDown,
    getItemProps: (index: number) => ({
      'aria-selected': index === activeIndex,
      'tabIndex': index === activeIndex ? 0 : -1,
      'data-active': index === activeIndex
    })
  };
}

// ARIA state management
export function useAriaState() {
  const [expanded, setExpanded] = useState(false);
  const [selected, setSelected] = useState(false);
  const [pressed, setPressed] = useState(false);
  
  return {
    expanded,
    setExpanded,
    selected,
    setSelected,
    pressed,
    setPressed,
    ariaProps: {
      'aria-expanded': expanded,
      'aria-selected': selected,
      'aria-pressed': pressed
    }
  };
}

// Error announcements
export function useErrorAnnouncements() {
  const announceError = (error: string) => {
    announceToScreenReader(`Error: ${error}`, 'assertive');
  };
  
  const announceSuccess = (message: string) => {
    announceToScreenReader(`Success: ${message}`, 'polite');
  };
  
  const announceInfo = (message: string) => {
    announceToScreenReader(message, 'polite');
  };
  
  return {
    announceError,
    announceSuccess,
    announceInfo
  };
}

// Skip links component data
export interface SkipLink {
  href: string;
  label: string;
}

export const defaultSkipLinks: SkipLink[] = [
  { href: '#main-content', label: 'Skip to main content' },
  { href: '#mission-list', label: 'Skip to mission list' },
  { href: '#chat-input', label: 'Skip to chat input' }
];

// Color contrast utilities
export function getContrastRatio(color1: string, color2: string): number {
  // Simplified contrast ratio calculation
  // In a real implementation, you'd convert colors to RGB and calculate properly
  return 4.5; // Placeholder - should meet WCAG AA standards
}

export function meetsContrastRequirements(
  foreground: string, 
  background: string, 
  level: 'AA' | 'AAA' = 'AA'
): boolean {
  const ratio = getContrastRatio(foreground, background);
  return level === 'AA' ? ratio >= 4.5 : ratio >= 7;
}

// Motion preferences
export function useReducedMotion(): boolean {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);
    
    const handler = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };
    
    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, []);
  
  return prefersReducedMotion;
}

// High contrast mode detection
export function useHighContrast(): boolean {
  const [highContrast, setHighContrast] = useState(false);
  
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setHighContrast(mediaQuery.matches);
    
    const handler = (event: MediaQueryListEvent) => {
      setHighContrast(event.matches);
    };
    
    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, []);
  
  return highContrast;
}

// Generate unique IDs for form fields and labels
export function useUniqueId(prefix: string = 'id'): string {
  const [id] = useState(() => `${prefix}-${Math.random().toString(36).substr(2, 9)}`);
  return id;
}