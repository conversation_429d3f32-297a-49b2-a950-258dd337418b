import { BaseTool } from './base';
import { ToolResult, ExecutionContext } from '../types';
import axios from 'axios';
import * as cheerio from 'cheerio';
import { DeepSeekClient } from '../deepseek';
import db, { schema } from '../../db';

export interface ConfluenceConfig {
  baseUrl?: string;
  username?: string;
  apiToken?: string;
}

export class ConfluenceLoaderTool extends BaseTool {
  name = 'confluence_loader';
  description = 'Load and index content from Confluence pages using advanced document processing';
  parameters = {
    url: { type: 'string', required: true, description: 'Confluence page URL' },
    includeAttachments: { type: 'boolean', required: false, description: 'Whether to include attachments' },
  };

  private deepseek: DeepSeekClient;
  private config: ConfluenceConfig;
  private documentProcessor: any; // Will be imported DocumentProcessor

  constructor(deepseek: DeepSeekClient, config: ConfluenceConfig = {}) {
    super();
    this.deepseek = deepseek;
    this.config = config;
    
    // Lazy import to avoid circular dependencies
    this.initializeDocumentProcessor();
  }

  private async initializeDocumentProcessor() {
    const { createDocumentProcessor } = await import('@/lib/llamaindex');
    this.documentProcessor = createDocumentProcessor(this.deepseek);
  }

  async execute(params: Record<string, any>, context?: ExecutionContext): Promise<ToolResult> {
    try {
      this.validateParams(params, ['url']);
      
      const { url, includeAttachments = false } = params;
      
      context?.logger.info(`Loading Confluence page with LlamaIndex: ${url}`);

      // Extract content from the page
      const content = await this.extractPageContent(url);
      
      // Ensure document processor is initialized
      if (!this.documentProcessor) {
        await this.initializeDocumentProcessor();
      }
      
      // Process document with LlamaIndex integration
      const processedDoc = await this.documentProcessor.processDocument(
        content.text,
        {
          title: content.title,
          sourceUrl: url,
          sourceType: 'confluence',
          tags: content.tags,
          lastIndexed: new Date(),
        }
      );

      context?.logger.info(`Successfully processed Confluence page: ${content.title} (${processedDoc.chunks.length} chunks)`);

      return this.createResult(true, {
        id: processedDoc.id,
        title: content.title,
        wordCount: content.text.split(' ').length,
        chunkCount: processedDoc.chunks.length,
        concepts: await this.documentProcessor.extractConcepts(content.text),
        url,
      });

    } catch (error) {
      context?.logger.error(`Failed to load Confluence page: ${error}`);
      return this.createResult(false, null, (error as Error).message);
    }
  }

  private async extractPageContent(url: string): Promise<{
    title: string;
    text: string;
    summary: string;
    tags: string[];
  }> {
    try {
      // For demo purposes, we'll use web scraping
      // In production, you'd use the Confluence REST API
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; ServiceAgent/1.0)',
        },
        timeout: 10000,
      });

      const $ = cheerio.load(response.data);
      
      // Extract title
      const title = $('title').text().trim() || $('h1').first().text().trim() || 'Untitled';
      
      // Extract main content
      let text = '';
      
      // Try common Confluence content selectors
      const contentSelectors = [
        '#main-content',
        '.wiki-content',
        '.page-content',
        'main',
        '.content',
        'article',
      ];

      for (const selector of contentSelectors) {
        const content = $(selector);
        if (content.length > 0) {
          // Remove script and style elements
          content.find('script, style, nav, header, footer').remove();
          text = content.text().trim();
          break;
        }
      }

      // Fallback to body content if no specific content area found
      if (!text) {
        $('script, style, nav, header, footer').remove();
        text = $('body').text().trim();
      }

      // Clean up the text
      text = text.replace(/\s+/g, ' ').trim();

      // Generate summary (first 200 words)
      const words = text.split(' ');
      const summary = words.slice(0, 200).join(' ') + (words.length > 200 ? '...' : '');

      // Extract potential tags from headings and meta tags
      const tags: string[] = [];
      
      // From headings
      $('h1, h2, h3').each((_, el) => {
        const heading = $(el).text().trim();
        if (heading && heading.length < 50) {
          tags.push(heading.toLowerCase());
        }
      });

      // From meta keywords
      const metaKeywords = $('meta[name="keywords"]').attr('content');
      if (metaKeywords) {
        tags.push(...metaKeywords.split(',').map(tag => tag.trim().toLowerCase()));
      }

      // Remove duplicates and limit to 10 tags
      const uniqueTags = [...new Set(tags)].slice(0, 10);

      return {
        title,
        text,
        summary,
        tags: uniqueTags,
      };

    } catch (error) {
      throw new Error(`Failed to extract content from ${url}: ${(error as Error).message}`);
    }
  }
}

export class ConfluenceSearchTool extends BaseTool {
  name = 'confluence_search';
  description = 'Search indexed Confluence content using advanced natural language processing';
  parameters = {
    query: { type: 'string', required: true, description: 'Natural language search query' },
    limit: { type: 'number', required: false, description: 'Maximum number of results to return' },
    useChunks: { type: 'boolean', required: false, description: 'Search at chunk level for more precise results' },
    threshold: { type: 'number', required: false, description: 'Minimum similarity threshold (0-1)' },
  };

  private deepseek: DeepSeekClient;
  private documentProcessor: any; // Will be imported DocumentProcessor

  constructor(deepseek: DeepSeekClient) {
    super();
    this.deepseek = deepseek;
    this.initializeDocumentProcessor();
  }

  private async initializeDocumentProcessor() {
    const { createDocumentProcessor } = await import('@/lib/llamaindex');
    this.documentProcessor = createDocumentProcessor(this.deepseek);
  }

  async execute(params: Record<string, any>, context?: ExecutionContext): Promise<ToolResult> {
    try {
      this.validateParams(params, ['query']);
      
      const { 
        query, 
        limit = 5, 
        useChunks = true, 
        threshold = 0.3 
      } = params;
      
      context?.logger.info(`Searching Confluence content with enhanced processing: ${query}`);

      // Ensure document processor is initialized
      if (!this.documentProcessor) {
        await this.initializeDocumentProcessor();
      }

      // Use the advanced search from DocumentProcessor
      const searchResults = await this.documentProcessor.searchDocuments(query, {
        limit,
        sourceType: 'confluence',
        threshold,
        useChunks,
      });

      // Format results for the agent
      const formattedResults = searchResults.map(result => ({
        id: result.document.id,
        title: result.document.metadata.title,
        summary: result.document.metadata.title,
        url: result.document.metadata.sourceUrl,
        score: Math.round(result.score * 100) / 100,
        snippet: result.snippet,
        chunkInfo: useChunks ? {
          chunkId: result.chunk.id,
          startIndex: result.chunk.startIndex,
          endIndex: result.chunk.endIndex,
        } : null,
        tags: result.document.metadata.tags,
        concepts: result.document.chunks?.[0]?.metadata?.concepts || [],
      }));

      context?.logger.info(`Found ${formattedResults.length} relevant results with scores above ${threshold}`);

      // If no results found with chunks, try document-level search
      let fallbackResults = [];
      if (formattedResults.length === 0 && useChunks) {
        context?.logger.info('No chunk-level results found, trying document-level search...');
        
        const docLevelResults = await this.documentProcessor.searchDocuments(query, {
          limit,
          sourceType: 'confluence',
          threshold: Math.max(0.2, threshold - 0.1), // Lower threshold for fallback
          useChunks: false,
        });

        fallbackResults = docLevelResults.map(result => ({
          id: result.document.id,
          title: result.document.metadata.title,
          summary: result.document.metadata.title,
          url: result.document.metadata.sourceUrl,
          score: Math.round(result.score * 100) / 100,
          snippet: result.snippet,
          chunkInfo: null,
          tags: result.document.metadata.tags,
          isFallback: true,
        }));
      }

      const finalResults = formattedResults.length > 0 ? formattedResults : fallbackResults;

      return this.createResult(true, {
        query,
        results: finalResults,
        totalFound: finalResults.length,
        searchType: useChunks ? 'chunk-level' : 'document-level',
        threshold,
        usedFallback: formattedResults.length === 0 && fallbackResults.length > 0,
      });

    } catch (error) {
      context?.logger.error(`Failed to search Confluence content: ${error}`);
      return this.createResult(false, null, (error as Error).message);
    }
  }
}

// Import eq from drizzle-orm
import { eq } from 'drizzle-orm';
