import { sql } from 'drizzle-orm';
import { DrizzleD1Database } from 'drizzle-orm/d1';
import { BetterSQLite3Database } from 'drizzle-orm/better-sqlite3';
import db from './index';

/**
 * Database performance optimization with indexes and query optimization
 */
export class DatabaseOptimizer {
  private db: BetterSQLite3Database<any>;

  constructor(database: BetterSQLite3Database<any>) {
    this.db = database;
  }

  /**
   * Create performance indexes for all tables
   */
  async createIndexes(): Promise<void> {
    console.log('🚀 Creating database performance indexes...');

    const indexes = [
      // Missions table indexes
      {
        name: 'idx_missions_status',
        sql: 'CREATE INDEX IF NOT EXISTS idx_missions_status ON missions(status)'
      },
      {
        name: 'idx_missions_priority',
        sql: 'CREATE INDEX IF NOT EXISTS idx_missions_priority ON missions(priority)'
      },
      {
        name: 'idx_missions_created_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_missions_created_at ON missions(created_at)'
      },
      {
        name: 'idx_missions_updated_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_missions_updated_at ON missions(updated_at)'
      },
      {
        name: 'idx_missions_status_priority',
        sql: 'CREATE INDEX IF NOT EXISTS idx_missions_status_priority ON missions(status, priority)'
      },

      // Plans table indexes
      {
        name: 'idx_plans_mission_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_plans_mission_id ON plans(mission_id)'
      },
      {
        name: 'idx_plans_status',
        sql: 'CREATE INDEX IF NOT EXISTS idx_plans_status ON plans(status)'
      },
      {
        name: 'idx_plans_version',
        sql: 'CREATE INDEX IF NOT EXISTS idx_plans_version ON plans(version)'
      },
      {
        name: 'idx_plans_mission_version',
        sql: 'CREATE INDEX IF NOT EXISTS idx_plans_mission_version ON plans(mission_id, version DESC)'
      },
      {
        name: 'idx_plans_created_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_plans_created_at ON plans(created_at)'
      },

      // Tasks table indexes
      {
        name: 'idx_tasks_plan_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tasks_plan_id ON tasks(plan_id)'
      },
      {
        name: 'idx_tasks_status',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)'
      },
      {
        name: 'idx_tasks_priority',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority)'
      },
      {
        name: 'idx_tasks_parent_task_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tasks_parent_task_id ON tasks(parent_task_id)'
      },
      {
        name: 'idx_tasks_tool_name',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tasks_tool_name ON tasks(tool_name)'
      },
      {
        name: 'idx_tasks_status_priority',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tasks_status_priority ON tasks(status, priority)'
      },
      {
        name: 'idx_tasks_plan_status',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tasks_plan_status ON tasks(plan_id, status)'
      },
      {
        name: 'idx_tasks_started_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tasks_started_at ON tasks(started_at)'
      },
      {
        name: 'idx_tasks_completed_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tasks_completed_at ON tasks(completed_at)'
      },

      // Execution logs indexes
      {
        name: 'idx_execution_logs_mission_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_execution_logs_mission_id ON execution_logs(mission_id)'
      },
      {
        name: 'idx_execution_logs_plan_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_execution_logs_plan_id ON execution_logs(plan_id)'
      },
      {
        name: 'idx_execution_logs_task_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_execution_logs_task_id ON execution_logs(task_id)'
      },
      {
        name: 'idx_execution_logs_level',
        sql: 'CREATE INDEX IF NOT EXISTS idx_execution_logs_level ON execution_logs(level)'
      },
      {
        name: 'idx_execution_logs_timestamp',
        sql: 'CREATE INDEX IF NOT EXISTS idx_execution_logs_timestamp ON execution_logs(timestamp DESC)'
      },
      {
        name: 'idx_execution_logs_mission_timestamp',
        sql: 'CREATE INDEX IF NOT EXISTS idx_execution_logs_mission_timestamp ON execution_logs(mission_id, timestamp DESC)'
      },
      {
        name: 'idx_execution_logs_level_timestamp',
        sql: 'CREATE INDEX IF NOT EXISTS idx_execution_logs_level_timestamp ON execution_logs(level, timestamp DESC)'
      },

      // Reflections indexes
      {
        name: 'idx_reflections_mission_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_reflections_mission_id ON reflections(mission_id)'
      },
      {
        name: 'idx_reflections_plan_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_reflections_plan_id ON reflections(plan_id)'
      },
      {
        name: 'idx_reflections_type',
        sql: 'CREATE INDEX IF NOT EXISTS idx_reflections_type ON reflections(type)'
      },
      {
        name: 'idx_reflections_timestamp',
        sql: 'CREATE INDEX IF NOT EXISTS idx_reflections_timestamp ON reflections(timestamp DESC)'
      },

      // Tool usage indexes
      {
        name: 'idx_tool_usage_task_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tool_usage_task_id ON tool_usage(task_id)'
      },
      {
        name: 'idx_tool_usage_tool_name',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tool_usage_tool_name ON tool_usage(tool_name)'
      },
      {
        name: 'idx_tool_usage_status',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tool_usage_status ON tool_usage(status)'
      },
      {
        name: 'idx_tool_usage_started_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tool_usage_started_at ON tool_usage(started_at DESC)'
      },
      {
        name: 'idx_tool_usage_tool_status',
        sql: 'CREATE INDEX IF NOT EXISTS idx_tool_usage_tool_status ON tool_usage(tool_name, status)'
      },

      // Knowledge base indexes
      {
        name: 'idx_knowledge_base_source_type',
        sql: 'CREATE INDEX IF NOT EXISTS idx_knowledge_base_source_type ON knowledge_base(source_type)'
      },
      {
        name: 'idx_knowledge_base_source_url',
        sql: 'CREATE INDEX IF NOT EXISTS idx_knowledge_base_source_url ON knowledge_base(source_url)'
      },
      {
        name: 'idx_knowledge_base_last_indexed',
        sql: 'CREATE INDEX IF NOT EXISTS idx_knowledge_base_last_indexed ON knowledge_base(last_indexed DESC)'
      },
      {
        name: 'idx_knowledge_base_created_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_knowledge_base_created_at ON knowledge_base(created_at DESC)'
      },
      {
        name: 'idx_knowledge_base_source_type_indexed',
        sql: 'CREATE INDEX IF NOT EXISTS idx_knowledge_base_source_type_indexed ON knowledge_base(source_type, last_indexed DESC)'
      },

      // Full-text search indexes for content
      {
        name: 'idx_knowledge_base_title_fts',
        sql: 'CREATE VIRTUAL TABLE IF NOT EXISTS knowledge_base_fts USING fts5(id UNINDEXED, title, content, summary, tokenize = "unicode61 remove_diacritics 1")'
      },
      {
        name: 'idx_missions_title_fts',
        sql: 'CREATE VIRTUAL TABLE IF NOT EXISTS missions_fts USING fts5(id UNINDEXED, title, description, tokenize = "unicode61 remove_diacritics 1")'
      }
    ];

    for (const index of indexes) {
      try {
        await this.db.run(sql.raw(index.sql));
        console.log(`✅ Created index: ${index.name}`);
      } catch (error) {
        // Index might already exist, which is fine
        if (!error.message.includes('already exists')) {
          console.warn(`⚠️  Failed to create index ${index.name}:`, error.message);
        }
      }
    }

    // Populate FTS tables
    await this.populateFTSTables();

    console.log('✅ Database indexes created successfully');
  }

  /**
   * Populate full-text search tables
   */
  private async populateFTSTables(): Promise<void> {
    try {
      // Populate knowledge base FTS
      await this.db.run(sql.raw(`
        INSERT OR REPLACE INTO knowledge_base_fts(id, title, content, summary)
        SELECT id, title, content, summary FROM knowledge_base
      `));

      // Populate missions FTS
      await this.db.run(sql.raw(`
        INSERT OR REPLACE INTO missions_fts(id, title, description)
        SELECT id, title, description FROM missions
      `));

      console.log('✅ FTS tables populated');
    } catch (error) {
      console.warn('⚠️  Failed to populate FTS tables:', error);
    }
  }

  /**
   * Analyze database performance and suggest optimizations
   */
  async analyzePerformance(): Promise<PerformanceAnalysis> {
    const analysis: PerformanceAnalysis = {
      tableStats: new Map(),
      indexUsage: new Map(),
      slowQueries: [],
      recommendations: []
    };

    try {
      // Get table statistics
      const tables = ['missions', 'plans', 'tasks', 'execution_logs', 'reflections', 'tool_usage', 'knowledge_base'];
      
      for (const table of tables) {
        const countResult = await this.db.get(sql.raw(`SELECT COUNT(*) as count FROM ${table}`));
        const count = countResult?.count || 0;
        
        analysis.tableStats.set(table, {
          rowCount: count,
          estimatedSize: count * 1024, // Rough estimate
        });
      }

      // Check for missing indexes (this would require query log analysis in production)
      analysis.recommendations = this.generateOptimizationRecommendations(analysis.tableStats);

    } catch (error) {
      console.error('Failed to analyze database performance:', error);
    }

    return analysis;
  }

  /**
   * Optimize database settings for better performance
   */
  async optimizeSettings(): Promise<void> {
    const optimizations = [
      // Enable WAL mode for better concurrency
      'PRAGMA journal_mode = WAL',
      
      // Optimize cache size
      'PRAGMA cache_size = -64000', // 64MB cache
      
      // Enable foreign key constraints
      'PRAGMA foreign_keys = ON',
      
      // Optimize synchronous mode for development (use FULL for production)
      'PRAGMA synchronous = NORMAL',
      
      // Optimize temp store
      'PRAGMA temp_store = MEMORY',
      
      // Optimize page size
      'PRAGMA page_size = 4096',
      
      // Auto vacuum for space management
      'PRAGMA auto_vacuum = INCREMENTAL'
    ];

    for (const pragma of optimizations) {
      try {
        await this.db.run(sql.raw(pragma));
        console.log(`✅ Applied: ${pragma}`);
      } catch (error) {
        console.warn(`⚠️  Failed to apply: ${pragma}`, error.message);
      }
    }
  }

  /**
   * Vacuum and optimize database
   */
  async vacuum(): Promise<void> {
    try {
      console.log('🧹 Vacuuming database...');
      await this.db.run(sql.raw('VACUUM'));
      await this.db.run(sql.raw('PRAGMA optimize'));
      console.log('✅ Database vacuumed and optimized');
    } catch (error) {
      console.error('Failed to vacuum database:', error);
    }
  }

  /**
   * Generate optimization recommendations
   */
  private generateOptimizationRecommendations(tableStats: Map<string, any>): string[] {
    const recommendations: string[] = [];

    for (const [table, stats] of tableStats.entries()) {
      if (stats.rowCount > 10000) {
        recommendations.push(`Table ${table} has ${stats.rowCount} rows - consider partitioning or archiving old data`);
      }
      
      if (stats.rowCount > 1000 && table === 'execution_logs') {
        recommendations.push('Consider implementing log rotation for execution_logs table');
      }
    }

    if (tableStats.get('knowledge_base')?.rowCount > 1000) {
      recommendations.push('Consider implementing vector index for knowledge_base embeddings');
    }

    return recommendations;
  }
}

// Performance analysis types
interface PerformanceAnalysis {
  tableStats: Map<string, {
    rowCount: number;
    estimatedSize: number;
  }>;
  indexUsage: Map<string, {
    timesUsed: number;
    lastUsed: Date;
  }>;
  slowQueries: {
    sql: string;
    executionTime: number;
    count: number;
  }[];
  recommendations: string[];
}

// Create and export singleton instance
export const databaseOptimizer = new DatabaseOptimizer(db);

// Export optimization functions
export async function initializeDatabaseOptimizations(): Promise<void> {
  await databaseOptimizer.createIndexes();
  await databaseOptimizer.optimizeSettings();
}

export async function analyzePerformance(): Promise<PerformanceAnalysis> {
  return databaseOptimizer.analyzePerformance();
}

export async function optimizeDatabase(): Promise<void> {
  await databaseOptimizer.vacuum();
}