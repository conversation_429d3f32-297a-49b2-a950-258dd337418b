# WebSocket to Server-Sent Events (SSE) Migration Guide

## Executive Summary

This guide evaluates and outlines the migration from WebSocket to Server-Sent Events (SSE) for real-time updates in your Next.js application. **SSE is recommended for your use case** due to simplified infrastructure, better HTTP/2 compatibility, and sufficient functionality for unidirectional updates.

## Analysis of Current Implementation

### Current WebSocket Architecture
Your application currently uses:
- **Separate Fastify server** on port 8080 for WebSocket connections
- **Bidirectional communication** with subscription management
- **4 types of real-time updates**: mission, task, log, and reflection updates
- **Ping/pong keepalive** mechanism for connection health
- **Client-side reconnection logic** with exponential backoff

### Update Patterns Analysis
- **Update frequency**: Moderate (progress updates, task completions, logs)
- **Data volume**: Small JSON payloads (typically 100-800 bytes)
- **Direction**: Primarily server-to-client (95% of communication)
- **Latency requirements**: Not critical (human-readable updates)

## WebSocket vs SSE Comparison

### ✅ SSE Advantages for Your Use Case

1. **Simplified Infrastructure**
   - No separate server needed (uses Next.js API routes)
   - Eliminates Fastify server maintenance
   - Reduces deployment complexity
   - Fewer potential failure points

2. **HTTP/2 Compatibility**
   - Better performance with connection multiplexing
   - Leverages existing HTTP infrastructure
   - Improved caching and compression

3. **Built-in Reconnection**
   - EventSource API handles reconnection automatically
   - No need for custom reconnection logic
   - More reliable connection recovery

4. **Firewall/Corporate Network Friendly**
   - Uses standard HTTP/HTTPS ports
   - Better compatibility with proxies and firewalls
   - No WebSocket upgrade negotiation issues

5. **Reduced Operational Overhead**
   - One less server to monitor and maintain
   - Unified logging and error handling
   - Simplified SSL/TLS configuration

### ⚠️ SSE Limitations

1. **Unidirectional Communication**
   - Client cannot send messages to server
   - No ping/pong mechanism (though not needed)
   - Limited to server-to-client updates

2. **Browser Connection Limits**
   - Limited to 6 concurrent connections per domain
   - May need connection pooling for multiple tabs

3. **HTTP/2 Server Push Alternative**
   - Less efficient than WebSocket for high-frequency updates
   - Additional HTTP overhead per message

### 🔄 WebSocket Advantages (Why you might stay)

1. **Bidirectional Communication**
   - Client can send subscription requests
   - Ping/pong heartbeat mechanism
   - Mission status requests

2. **Lower Latency**
   - No HTTP overhead per message
   - Better for real-time applications

3. **More Efficient for High-Frequency Updates**
   - Less protocol overhead
   - Better for gaming or trading applications

## Migration Recommendation: ✅ Proceed with SSE

**Recommendation**: Migrate to SSE because:
- Your bidirectional features are minimal (subscription can be handled via HTTP)
- Update frequency is moderate (not high-frequency trading)
- Infrastructure simplification outweighs the benefits of bidirectional communication
- Better long-term maintainability

## Migration Strategy

### Phase 1: SSE Infrastructure (✅ Completed)

Already implemented:
- ✅ **SSE API Route**: `/api/missions/[id]/stream`
- ✅ **SSE Client**: `src/lib/sse/client.ts`
- ✅ **Hybrid Hook**: `src/hooks/useMissionUpdatesSSE.ts`

### Phase 2: Component Migration

#### Example: MissionWorkspace Component

**Before (WebSocket only):**
```typescript
// components/MissionWorkspace.tsx
import { useMissionUpdates } from '@/hooks/useMissionUpdates';

const MissionWorkspace: React.FC = () => {
  const {
    isConnected: wsConnected,
    connectionError,
    subscribeMission,
    unsubscribeMission,
    getMission: getWsMission,
    missions: wsMissions,
    clearMission
  } = useMissionUpdates({
    autoConnect: true,
    maxLogs: 50,
  });

  useEffect(() => {
    if (selectedMissionId && wsConnected) {
      subscribeMission(selectedMissionId.toString());
      return () => unsubscribeMission(selectedMissionId.toString());
    }
  }, [selectedMissionId, wsConnected, subscribeMission, unsubscribeMission]);

  // ... rest of component
};
```

**After (Hybrid with SSE preference):**
```typescript
// components/MissionWorkspace.tsx
import { useMissionUpdatesSSE } from '@/hooks/useMissionUpdatesSSE';

const MissionWorkspace: React.FC = () => {
  const {
    missionState,
    isConnected,
    connectionError,
    activeConnection,
    connect,
    disconnect,
    getTasks,
    getLogs,
    getReflections,
  } = useMissionUpdatesSSE(selectedMissionId?.toString() || '', {
    connectionType: 'hybrid', // Try WebSocket first, fallback to SSE
    preferredConnection: 'sse', // Prefer SSE in hybrid mode
    fallbackToSSE: true,
    autoConnect: true,
    maxLogs: 50,
  });

  // No need for manual subscription management
  // Connection is automatically managed based on missionId

  // ... rest of component
};
```

### Phase 3: Migration Patterns

#### 1. Drop-in Replacement (Recommended)

Replace the WebSocket hook with the hybrid hook:

```typescript
// Before
import { useMissionUpdates } from '@/hooks/useMissionUpdates';

// After
import { useMissionUpdatesSSE } from '@/hooks/useMissionUpdatesSSE';

// The hybrid hook provides backward compatibility
const {
  missions, // Map<string, MissionState>
  subscribeMission, // (id: string) => void
  unsubscribeMission, // (id: string) => void
  getMission, // (id: string) => MissionState | undefined
} = useMissionUpdatesSSE(missionId, {
  connectionType: 'hybrid',
  preferredConnection: 'sse',
});
```

#### 2. SSE-Only Migration

For new features, use SSE directly:

```typescript
import { useSSE } from '@/lib/sse/client';

const MyComponent = () => {
  const { client, isConnected, error } = useSSE(missionId);

  useEffect(() => {
    if (!client) return;

    const handleUpdate = (update: MissionUpdate) => {
      // Handle mission update
    };

    client.on('mission.update', handleUpdate);
    return () => client.off('mission.update', handleUpdate);
  }, [client]);

  // ... rest of component
};
```

### Phase 4: Connection Type Selection

Choose the appropriate connection type based on your needs:

```typescript
// Option 1: SSE only (recommended for new features)
const updates = useMissionUpdatesSSE(missionId, {
  connectionType: 'sse',
  autoConnect: true,
});

// Option 2: Hybrid with SSE preference (recommended for migration)
const updates = useMissionUpdatesSSE(missionId, {
  connectionType: 'hybrid',
  preferredConnection: 'sse',
  fallbackToSSE: true,
});

// Option 3: WebSocket only (maintain current behavior)
const updates = useMissionUpdatesSSE(missionId, {
  connectionType: 'websocket',
  wsUrl: 'ws://localhost:8080/ws',
});
```

### Phase 5: Gradual Rollout

Implement feature flags for gradual migration:

```typescript
// lib/config/features.ts
export const FEATURE_FLAGS = {
  USE_SSE_FOR_MISSION_UPDATES: process.env.USE_SSE === 'true',
  SSE_FALLBACK_ENABLED: process.env.SSE_FALLBACK !== 'false',
};

// components/MissionWorkspace.tsx
const connectionType = FEATURE_FLAGS.USE_SSE_FOR_MISSION_UPDATES 
  ? 'sse' 
  : 'websocket';

const updates = useMissionUpdatesSSE(missionId, {
  connectionType,
  fallbackToSSE: FEATURE_FLAGS.SSE_FALLBACK_ENABLED,
});
```

## Implementation Details

### SSE API Route Features

The SSE endpoint (`/api/missions/[id]/stream`) provides:

```typescript
// Automatic connection management
data: {"type":"connection.established","payload":{"missionId":"123","clientId":"sse-..."},"timestamp":"..."}

// Real-time updates
data: {"type":"mission.update","payload":{"missionId":"123","status":"executing","progress":0.5},"timestamp":"..."}

// Keep-alive pings (every 30 seconds)
data: {"type":"ping","payload":{"timestamp":"..."},"timestamp":"..."}

// Error handling
data: {"type":"error","payload":{"message":"Server error","code":"ERROR_CODE"},"timestamp":"..."}
```

### Client Features

The SSE client provides:

```typescript
// Connection management
await client.connect();
client.disconnect();

// Event handling
client.on('mission.update', (update) => {});
client.on('connected', () => {});
client.on('disconnected', () => {});
client.on('error', (error) => {});

// Status monitoring
const status = client.getStatus();
const isConnected = client.isConnected();
```

## Testing Strategy

### Unit Tests

```typescript
// __tests__/sse-client.test.ts
import { SSEClient } from '@/lib/sse/client';

describe('SSEClient', () => {
  it('should connect and receive messages', async () => {
    const client = new SSEClient('test-mission');
    await client.connect();
    
    expect(client.isConnected()).toBe(true);
    expect(client.getMissionId()).toBe('test-mission');
  });

  it('should handle connection errors gracefully', async () => {
    const client = new SSEClient('invalid-mission');
    const errorPromise = new Promise((resolve) => {
      client.on('error', resolve);
    });

    await expect(client.connect()).rejects.toThrow();
    await expect(errorPromise).resolves.toMatchObject({
      message: expect.stringContaining('connection failed'),
    });
  });
});
```

### Integration Tests

```typescript
// __tests__/hybrid-hook.test.ts
import { useMissionUpdatesSSE } from '@/hooks/useMissionUpdatesSSE';
import { renderHook, act } from '@testing-library/react';

describe('useMissionUpdatesSSE', () => {
  it('should fallback to SSE when WebSocket fails', async () => {
    const { result } = renderHook(() => 
      useMissionUpdatesSSE('test-mission', {
        connectionType: 'hybrid',
        fallbackToSSE: true,
      })
    );

    await act(async () => {
      await result.current.connect();
    });

    expect(result.current.activeConnection).toBe('sse');
    expect(result.current.isConnected).toBe(true);
  });
});
```

### End-to-End Tests

```typescript
// e2e/mission-updates.e2e.ts
describe('Mission Updates', () => {
  it('should receive real-time updates via SSE', async () => {
    const page = await browser.newPage();
    await page.goto('/missions/123');

    // Wait for SSE connection
    await page.waitForSelector('[data-testid="connection-status"][data-connected="true"]');

    // Simulate server-side mission update
    await simulateMissionUpdate('123', { status: 'executing', progress: 0.5 });

    // Verify update received
    await expect(page.locator('[data-testid="mission-status"]')).toContainText('executing');
    await expect(page.locator('[data-testid="mission-progress"]')).toContainText('50%');
  });
});
```

## Performance Considerations

### Connection Management

```typescript
// Optimize connection usage
const SSE_CONNECTION_POOL = new Map<string, SSEClient>();

export function getOptimizedSSEClient(missionId: string): SSEClient {
  if (!SSE_CONNECTION_POOL.has(missionId)) {
    SSE_CONNECTION_POOL.set(missionId, new SSEClient(missionId));
  }
  return SSE_CONNECTION_POOL.get(missionId)!;
}

// Cleanup inactive connections
export function cleanupSSEConnections() {
  SSE_CONNECTION_POOL.forEach((client, missionId) => {
    if (!client.isConnected()) {
      client.disconnect();
      SSE_CONNECTION_POOL.delete(missionId);
    }
  });
}
```

### Memory Management

```typescript
// Prevent memory leaks in React components
useEffect(() => {
  const client = getSSEClient(missionId);
  
  // Event handlers
  const handleUpdate = (update: MissionUpdate) => {
    setMissionState(prev => ({ ...prev, ...update }));
  };
  
  client.on('mission.update', handleUpdate);
  
  return () => {
    client.off('mission.update', handleUpdate);
    // Only disconnect if this is the last subscriber
    if (client.listenerCount('mission.update') === 0) {
      client.disconnect();
    }
  };
}, [missionId]);
```

## Monitoring and Observability

### Connection Metrics

```typescript
// Track connection type usage
const trackConnectionMetrics = (type: 'websocket' | 'sse', event: string) => {
  if (typeof window !== 'undefined') {
    // Analytics tracking
    gtag('event', event, {
      event_category: 'realtime_connection',
      event_label: type,
      custom_map: {
        connection_type: type,
      },
    });
  }
};

// Usage in hook
useEffect(() => {
  if (isConnected && activeConnection) {
    trackConnectionMetrics(activeConnection, 'connection_established');
  }
}, [isConnected, activeConnection]);
```

### Error Monitoring

```typescript
// Enhanced error tracking
const trackSSEError = (error: Error, context: any) => {
  if (typeof window !== 'undefined') {
    // Sentry or similar error tracking
    Sentry.captureException(error, {
      tags: {
        component: 'sse_client',
        mission_id: context.missionId,
      },
      extra: {
        connection_type: context.activeConnection,
        error_code: context.errorCode,
        user_agent: navigator.userAgent,
      },
    });
  }
};
```

## Rollback Strategy

### Graceful Degradation

```typescript
// Fallback to polling if both WebSocket and SSE fail
const useMissionUpdatesWithPolling = (missionId: string) => {
  const sseResult = useMissionUpdatesSSE(missionId, {
    connectionType: 'hybrid',
    fallbackToSSE: true,
  });

  const [pollingData, setPollingData] = useState(null);
  const [isPolling, setIsPolling] = useState(false);

  useEffect(() => {
    if (!sseResult.isConnected && !isPolling) {
      console.warn('Real-time connections failed, falling back to polling');
      setIsPolling(true);
      
      const interval = setInterval(async () => {
        try {
          const data = await fetch(`/api/missions/${missionId}`).then(r => r.json());
          setPollingData(data);
        } catch (error) {
          console.error('Polling failed:', error);
        }
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [sseResult.isConnected, isPolling, missionId]);

  return sseResult.isConnected ? sseResult : { 
    ...sseResult, 
    missionState: pollingData || sseResult.missionState,
    isConnected: !!pollingData,
    activeConnection: 'polling' as const,
  };
};
```

### Quick Rollback to WebSocket

```typescript
// Environment variable to quickly disable SSE
const DISABLE_SSE = process.env.DISABLE_SSE === 'true';

const connectionType = DISABLE_SSE ? 'websocket' : 'hybrid';

const updates = useMissionUpdatesSSE(missionId, {
  connectionType,
  preferredConnection: DISABLE_SSE ? 'websocket' : 'sse',
});
```

## Migration Checklist

### Pre-Migration
- [ ] Audit current WebSocket usage patterns
- [ ] Identify bidirectional communication requirements
- [ ] Set up feature flags for gradual rollout
- [ ] Prepare rollback strategy
- [ ] Update monitoring and alerting

### Migration Steps
- [ ] Deploy SSE infrastructure (already done)
- [ ] Update components to use hybrid hook
- [ ] Test in development environment
- [ ] Deploy with feature flags disabled
- [ ] Enable SSE for subset of users
- [ ] Monitor connection success rates
- [ ] Gradually increase SSE usage
- [ ] Deprecate WebSocket server
- [ ] Remove WebSocket dependencies

### Post-Migration
- [ ] Monitor connection metrics
- [ ] Optimize connection pooling
- [ ] Update documentation
- [ ] Train team on SSE debugging
- [ ] Plan for future real-time features

## Conclusion

SSE provides a simpler, more maintainable solution for your real-time update needs. The hybrid approach allows for a gradual migration with built-in fallback mechanisms, reducing risk while improving infrastructure simplicity.

**Next Steps:**
1. Start with hybrid mode in development
2. Test thoroughly with real mission scenarios
3. Deploy with feature flags for controlled rollout
4. Monitor metrics and adjust as needed
5. Fully migrate to SSE once stable

The migration will result in:
- **Reduced operational overhead** (no separate WebSocket server)
- **Improved reliability** (better reconnection handling)
- **Better scalability** (HTTP/2 multiplexing)
- **Simplified deployment** (one less server to manage) 