import { EventEmitter } from 'events';
import { cacheManager } from '@/lib/cache/cache-manager';
import db from '@/lib/db';
import * as schema from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export interface MissionState {
  id: string;
  status: 'pending' | 'planning' | 'executing' | 'paused' | 'cancelled' | 'completed' | 'failed';
  currentTask?: string;
  currentTaskId?: string;
  progress: {
    percentage: number;
    completedTasks: number;
    totalTasks: number;
    currentPhase: string;
  };
  cancellationRequested: boolean;
  pauseRequested: boolean;
  resumeRequested: boolean;
  lastUpdate: Date;
  executionContext?: any;
  checkpoints: MissionCheckpoint[];
}

export interface MissionCheckpoint {
  id: string;
  timestamp: Date;
  taskId: string;
  taskTitle: string;
  status: 'completed' | 'failed' | 'skipped';
  result?: any;
  error?: string;
  context?: any;
}

export interface MissionLifecycleEvents {
  'status-changed': (missionId: string, oldStatus: string, newStatus: string) => void;
  'progress-updated': (missionId: string, progress: any) => void;
  'task-started': (missionId: string, taskId: string, taskTitle: string) => void;
  'task-completed': (missionId: string, taskId: string, result: any) => void;
  'task-failed': (missionId: string, taskId: string, error: string) => void;
  'checkpoint-created': (missionId: string, checkpoint: MissionCheckpoint) => void;
  'cancellation-requested': (missionId: string) => void;
  'pause-requested': (missionId: string) => void;
  'resume-requested': (missionId: string) => void;
}

/**
 * Enhanced mission lifecycle manager with cancellation, pause/resume, and checkpoint support
 * Provides comprehensive control over mission execution state and recovery capabilities
 */
export class MissionLifecycleManager extends EventEmitter {
  private static instance: MissionLifecycleManager | null = null;
  private missions: Map<string, MissionState> = new Map();
  private executionIntervals: Map<string, NodeJS.Timeout> = new Map();
  private isInitialized = false;

  private constructor() {
    super();
    this.setupCleanupInterval();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): MissionLifecycleManager {
    if (!MissionLifecycleManager.instance) {
      MissionLifecycleManager.instance = new MissionLifecycleManager();
    }
    return MissionLifecycleManager.instance;
  }

  /**
   * Initialize the lifecycle manager
   */
  initialize(): void {
    if (this.isInitialized) return;

    console.log('🚀 Mission Lifecycle Manager initialized');
    this.isInitialized = true;

    // Load active missions from database
    this.loadActiveMissions();

    // Setup event listeners
    this.setupEventListeners();
  }

  /**
   * Start tracking a mission
   */
  async startMission(missionId: string, totalTasks: number = 0): Promise<void> {
    const state: MissionState = {
      id: missionId,
      status: 'pending',
      progress: {
        percentage: 0,
        completedTasks: 0,
        totalTasks,
        currentPhase: 'initialized',
      },
      cancellationRequested: false,
      pauseRequested: false,
      resumeRequested: false,
      lastUpdate: new Date(),
      checkpoints: [],
    };

    this.missions.set(missionId, state);
    cacheManager.setSessionData(missionId, 'mission_state', state);

    console.log(`📋 Started tracking mission: ${missionId}`);
    this.emit('status-changed', missionId, 'unknown', 'pending');
  }

  /**
   * Update mission status
   */
  async updateMissionStatus(
    missionId: string, 
    status: MissionState['status'],
    context?: any
  ): Promise<void> {
    const state = this.getMissionState(missionId);
    if (!state) {
      console.warn(`Mission not found: ${missionId}`);
      return;
    }

    const oldStatus = state.status;
    state.status = status;
    state.lastUpdate = new Date();
    
    if (context) {
      state.executionContext = context;
    }

    // Update database
    await db.update(schema.missions)
      .set({ status })
      .where(eq(schema.missions.id, missionId));

    // Cache update
    cacheManager.setSessionData(missionId, 'mission_state', state);

    console.log(`📊 Mission ${missionId} status: ${oldStatus} → ${status}`);
    this.emit('status-changed', missionId, oldStatus, status);

    // Handle status-specific logic
    switch (status) {
      case 'paused':
        this.handleMissionPaused(missionId);
        break;
      case 'cancelled':
        this.handleMissionCancelled(missionId);
        break;
      case 'completed':
      case 'failed':
        this.handleMissionFinished(missionId);
        break;
    }
  }

  /**
   * Update mission progress
   */
  async updateProgress(
    missionId: string,
    progress: Partial<MissionState['progress']>,
    currentTask?: string,
    currentTaskId?: string
  ): Promise<void> {
    const state = this.getMissionState(missionId);
    if (!state) return;

    // Update progress
    state.progress = { ...state.progress, ...progress };
    state.currentTask = currentTask || state.currentTask;
    state.currentTaskId = currentTaskId || state.currentTaskId;
    state.lastUpdate = new Date();

    // Cache update
    cacheManager.setSessionData(missionId, 'mission_state', state);

    this.emit('progress-updated', missionId, state.progress);

    // Broadcast via WebSocket if available
    try {
      const { getWebSocketServer } = await import('@/lib/websocket/server');
      const wsServer = getWebSocketServer();
      if (wsServer) {
        wsServer.broadcastMissionUpdate({
          missionId,
          status: state.status,
          progress: state.progress,
          currentTask: state.currentTask,
          message: `${progress.currentPhase || 'Processing'}: ${currentTask || 'Working...'}`,
        });
      }
    } catch (error) {
      // WebSocket not available, continue silently
    }
  }

  /**
   * Create a checkpoint for mission recovery
   */
  async createCheckpoint(
    missionId: string,
    taskId: string,
    taskTitle: string,
    status: 'completed' | 'failed' | 'skipped',
    result?: any,
    error?: string
  ): Promise<void> {
    const state = this.getMissionState(missionId);
    if (!state) return;

    const checkpoint: MissionCheckpoint = {
      id: `${missionId}-${taskId}-${Date.now()}`,
      timestamp: new Date(),
      taskId,
      taskTitle,
      status,
      result,
      error,
      context: state.executionContext,
    };

    state.checkpoints.push(checkpoint);
    state.lastUpdate = new Date();

    // Keep only the last 50 checkpoints to manage memory
    if (state.checkpoints.length > 50) {
      state.checkpoints = state.checkpoints.slice(-50);
    }

    // Cache update
    cacheManager.setSessionData(missionId, 'mission_state', state);
    cacheManager.setSessionData(missionId, `checkpoint_${checkpoint.id}`, checkpoint);

    console.log(`🚩 Checkpoint created for mission ${missionId}: ${taskTitle} (${status})`);
    this.emit('checkpoint-created', missionId, checkpoint);

    // Emit task-specific events
    switch (status) {
      case 'completed':
        this.emit('task-completed', missionId, taskId, result);
        break;
      case 'failed':
        this.emit('task-failed', missionId, taskId, error || 'Unknown error');
        break;
    }
  }

  /**
   * Request mission cancellation
   */
  async requestCancellation(missionId: string): Promise<boolean> {
    const state = this.getMissionState(missionId);
    if (!state) {
      console.warn(`Cannot cancel mission: ${missionId} not found`);
      return false;
    }

    if (['completed', 'failed', 'cancelled'].includes(state.status)) {
      console.warn(`Cannot cancel mission: ${missionId} is already ${state.status}`);
      return false;
    }

    state.cancellationRequested = true;
    state.lastUpdate = new Date();

    // Cache update
    cacheManager.setSessionData(missionId, 'mission_state', state);

    console.log(`🛑 Cancellation requested for mission: ${missionId}`);
    this.emit('cancellation-requested', missionId);

    // If not actively executing, mark as cancelled immediately
    if (state.status === 'pending' || state.status === 'paused') {
      await this.updateMissionStatus(missionId, 'cancelled');
    }

    return true;
  }

  /**
   * Request mission pause
   */
  async requestPause(missionId: string): Promise<boolean> {
    const state = this.getMissionState(missionId);
    if (!state) {
      console.warn(`Cannot pause mission: ${missionId} not found`);
      return false;
    }

    if (state.status !== 'executing') {
      console.warn(`Cannot pause mission: ${missionId} is not executing (status: ${state.status})`);
      return false;
    }

    state.pauseRequested = true;
    state.lastUpdate = new Date();

    // Cache update
    cacheManager.setSessionData(missionId, 'mission_state', state);

    console.log(`⏸️ Pause requested for mission: ${missionId}`);
    this.emit('pause-requested', missionId);

    return true;
  }

  /**
   * Request mission resume
   */
  async requestResume(missionId: string): Promise<boolean> {
    const state = this.getMissionState(missionId);
    if (!state) {
      console.warn(`Cannot resume mission: ${missionId} not found`);
      return false;
    }

    if (state.status !== 'paused') {
      console.warn(`Cannot resume mission: ${missionId} is not paused (status: ${state.status})`);
      return false;
    }

    state.resumeRequested = true;
    state.pauseRequested = false;
    state.lastUpdate = new Date();

    // Cache update
    cacheManager.setSessionData(missionId, 'mission_state', state);

    console.log(`▶️ Resume requested for mission: ${missionId}`);
    this.emit('resume-requested', missionId);

    return true;
  }

  /**
   * Check if mission should be cancelled
   */
  shouldCancel(missionId: string): boolean {
    const state = this.getMissionState(missionId);
    return state ? state.cancellationRequested : false;
  }

  /**
   * Check if mission should be paused
   */
  shouldPause(missionId: string): boolean {
    const state = this.getMissionState(missionId);
    return state ? state.pauseRequested : false;
  }

  /**
   * Check if mission should resume
   */
  shouldResume(missionId: string): boolean {
    const state = this.getMissionState(missionId);
    return state ? state.resumeRequested : false;
  }

  /**
   * Get mission state
   */
  getMissionState(missionId: string): MissionState | null {
    let state = this.missions.get(missionId);
    
    if (!state) {
      // Try to load from cache
      state = cacheManager.getSessionData(missionId, 'mission_state');
      if (state) {
        this.missions.set(missionId, state);
      }
    }

    return state || null;
  }

  /**
   * Get all active missions
   */
  getActiveMissions(): MissionState[] {
    return Array.from(this.missions.values())
      .filter(state => !['completed', 'failed', 'cancelled'].includes(state.status));
  }

  /**
   * Get mission checkpoints
   */
  getMissionCheckpoints(missionId: string): MissionCheckpoint[] {
    const state = this.getMissionState(missionId);
    return state ? state.checkpoints : [];
  }

  /**
   * Recover mission from last checkpoint
   */
  async recoverFromLastCheckpoint(missionId: string): Promise<MissionCheckpoint | null> {
    const checkpoints = this.getMissionCheckpoints(missionId);
    if (checkpoints.length === 0) return null;

    const lastCheckpoint = checkpoints[checkpoints.length - 1];
    console.log(`🔄 Recovering mission ${missionId} from checkpoint: ${lastCheckpoint.taskTitle}`);
    
    return lastCheckpoint;
  }

  /**
   * Clean up finished mission
   */
  async cleanupMission(missionId: string): Promise<void> {
    this.missions.delete(missionId);
    
    // Clear execution interval if exists
    const interval = this.executionIntervals.get(missionId);
    if (interval) {
      clearInterval(interval);
      this.executionIntervals.delete(missionId);
    }

    // Clear most cached data (keep checkpoints for recovery)
    cacheManager.deleteSessionData(missionId, 'mission_state');
    
    console.log(`🧹 Cleaned up mission: ${missionId}`);
  }

  /**
   * Get lifecycle statistics
   */
  getStatistics(): {
    activeMissions: number;
    pausedMissions: number;
    totalMissions: number;
    checkpoints: number;
    memoryUsage: string;
  } {
    const states = Array.from(this.missions.values());
    const checkpointCount = states.reduce((sum, state) => sum + state.checkpoints.length, 0);

    return {
      activeMissions: states.filter(s => ['executing', 'planning'].includes(s.status)).length,
      pausedMissions: states.filter(s => s.status === 'paused').length,
      totalMissions: states.length,
      checkpoints: checkpointCount,
      memoryUsage: `${this.missions.size} states in memory`,
    };
  }

  /**
   * Health check for lifecycle manager
   */
  healthCheck(): {
    healthy: boolean;
    issues: string[];
    stats: any;
  } {
    const issues: string[] = [];
    const stats = this.getStatistics();

    // Check for potential issues
    if (stats.activeMissions > 50) {
      issues.push('High number of active missions');
    }

    if (stats.checkpoints > 1000) {
      issues.push('Large number of checkpoints, consider cleanup');
    }

    // Check for stuck missions
    const now = new Date();
    const stuckMissions = Array.from(this.missions.values())
      .filter(state => {
        const timeDiff = now.getTime() - state.lastUpdate.getTime();
        return state.status === 'executing' && timeDiff > 30 * 60 * 1000; // 30 minutes
      });

    if (stuckMissions.length > 0) {
      issues.push(`${stuckMissions.length} missions appear to be stuck`);
    }

    return {
      healthy: issues.length === 0,
      issues,
      stats,
    };
  }

  /**
   * Handle mission paused
   */
  private async handleMissionPaused(missionId: string): Promise<void> {
    console.log(`⏸️ Mission paused: ${missionId}`);
    
    // Create a pause checkpoint
    await this.createCheckpoint(
      missionId,
      'pause',
      'Mission Paused',
      'skipped',
      { reason: 'User requested pause' }
    );
  }

  /**
   * Handle mission cancelled
   */
  private async handleMissionCancelled(missionId: string): Promise<void> {
    console.log(`🛑 Mission cancelled: ${missionId}`);
    
    // Create a cancellation checkpoint
    await this.createCheckpoint(
      missionId,
      'cancel',
      'Mission Cancelled',
      'failed',
      undefined,
      'User requested cancellation'
    );

    // Schedule cleanup
    setTimeout(() => this.cleanupMission(missionId), 60000); // 1 minute delay
  }

  /**
   * Handle mission finished
   */
  private async handleMissionFinished(missionId: string): Promise<void> {
    const state = this.getMissionState(missionId);
    if (!state) return;

    console.log(`🏁 Mission finished: ${missionId} (${state.status})`);
    
    // Create a completion checkpoint
    await this.createCheckpoint(
      missionId,
      'finish',
      'Mission Finished',
      state.status === 'completed' ? 'completed' : 'failed',
      { finalStatus: state.status, totalTasks: state.progress.totalTasks, completedTasks: state.progress.completedTasks }
    );

    // Schedule cleanup
    setTimeout(() => this.cleanupMission(missionId), 5 * 60000); // 5 minute delay
  }

  /**
   * Load active missions from database on startup
   */
  private async loadActiveMissions(): Promise<void> {
    try {
      const activeMissions = await db.select()
        .from(schema.missions)
        .where(eq(schema.missions.status, 'executing'));

      for (const mission of activeMissions) {
        await this.startMission(mission.id, 0);
        // Set status without triggering events (already in database)
        const state = this.getMissionState(mission.id);
        if (state) {
          state.status = mission.status as any;
        }
      }

      console.log(`📋 Loaded ${activeMissions.length} active missions`);
    } catch (error) {
      console.error('Error loading active missions:', error);
    }
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    this.on('status-changed', (missionId, oldStatus, newStatus) => {
      console.log(`🔄 Mission ${missionId}: ${oldStatus} → ${newStatus}`);
    });

    this.on('task-started', (missionId, taskId, taskTitle) => {
      console.log(`▶️ Task started in mission ${missionId}: ${taskTitle}`);
    });

    this.on('task-completed', (missionId, taskId, result) => {
      console.log(`✅ Task completed in mission ${missionId}`);
    });

    this.on('task-failed', (missionId, taskId, error) => {
      console.log(`❌ Task failed in mission ${missionId}: ${error}`);
    });
  }

  /**
   * Setup cleanup interval for old data
   */
  private setupCleanupInterval(): void {
    // Clean up old mission data every hour
    setInterval(() => {
      this.cleanupOldMissions();
    }, 60 * 60 * 1000); // 1 hour
  }

  /**
   * Clean up old finished missions
   */
  private async cleanupOldMissions(): Promise<void> {
    const now = new Date();
    const missionsToCleanup: string[] = [];

    for (const [missionId, state] of this.missions) {
      if (['completed', 'failed', 'cancelled'].includes(state.status)) {
        const timeDiff = now.getTime() - state.lastUpdate.getTime();
        if (timeDiff > 24 * 60 * 60 * 1000) { // 24 hours
          missionsToCleanup.push(missionId);
        }
      }
    }

    for (const missionId of missionsToCleanup) {
      await this.cleanupMission(missionId);
    }

    if (missionsToCleanup.length > 0) {
      console.log(`🧹 Cleaned up ${missionsToCleanup.length} old missions`);
    }
  }
}

// Export singleton instance
export const missionLifecycle = MissionLifecycleManager.getInstance();