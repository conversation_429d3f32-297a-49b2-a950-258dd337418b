"use client"

import { useRef } from "react"
import { Bot, Plus, Library, PanelLeft, Search, X, FileText } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Mission, RealtimeStatus } from "./types"
import { MissionItem } from "./MissionItem"

interface MissionSidebarProps {
  isOpen: boolean;
  missions: Mission[];
  searchQuery: string;
  selectedMissionId: number | null;
  currentView: "chat" | "knowledge";
  editingMissionId: number | null;
  editingValue: string;
  getRealtimeMissionStatus: (missionId: number) => RealtimeStatus | null;
  onToggleSidebar: () => void;
  onStartNewMission: () => void;
  onSelectKnowledgeBase: () => void;
  onSelectMission: (missionId: number) => void;
  onSearchChange: (query: string) => void;
  onClearSearch: () => void;
  onStartEditingMission: (mission: Mission) => void;
  onEditValueChange: (value: string) => void;
  onSaveEditingMission: () => void;
  onCancelEditingMission: () => void;
  onDeleteMission: (mission: Mission) => void;
}

export function MissionSidebar({
  isOpen,
  missions,
  searchQuery,
  selectedMissionId,
  currentView,
  editingMissionId,
  editingValue,
  getRealtimeMissionStatus,
  onToggleSidebar,
  onStartNewMission,
  onSelectKnowledgeBase,
  onSelectMission,
  onSearchChange,
  onClearSearch,
  onStartEditingMission,
  onEditValueChange,
  onSaveEditingMission,
  onCancelEditingMission,
  onDeleteMission
}: MissionSidebarProps) {
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter missions based on search query
  const filteredMissions = missions.filter((mission) =>
    mission.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const clearSearch = () => {
    onClearSearch();
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  return (
    <div className={cn(
      "flex flex-col bg-muted/30 border-r border-border transition-all duration-300",
      isOpen ? "w-80" : "w-14"
    )}>
      <div className={cn(
        "p-4 border-b border-border",
        !isOpen && "p-2"
      )}>
        {isOpen ? (
          <>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Bot size={20} className="text-primary" />
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={onToggleSidebar}
                title="Collapse sidebar"
              >
                <PanelLeft className="h-4 w-4" />
              </Button>
            </div>
            <div className="space-y-2">
              <Button
                onClick={onStartNewMission}
                className={cn(
                  "w-full gap-2",
                  currentView === "chat" 
                    ? "" 
                    : "hover:bg-muted hover:text-muted-foreground"
                )}
                variant={currentView === "chat" ? "default" : "ghost"}
              >
                <Plus className="h-4 w-4" />
                New Mission
              </Button>
              <Button
                variant={currentView === "knowledge" ? "default" : "ghost"}
                className={cn(
                  "w-full gap-2",
                  currentView === "knowledge" 
                    ? "" 
                    : "hover:bg-muted hover:text-muted-foreground"
                )}
                onClick={onSelectKnowledgeBase}
              >
                <Library className="h-4 w-4" />
                Knowledge Base
              </Button>
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center space-y-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleSidebar}
              title="Show sidebar"
              className="w-10 h-10 hover:bg-muted hover:text-muted-foreground"
            >
              <PanelLeft className="h-4 w-4" />
            </Button>
            <Button
              onClick={onStartNewMission}
              size="icon"
              variant={currentView === "chat" ? "default" : "ghost"}
              title="New Mission"
              className={cn(
                "w-10 h-10",
                currentView === "chat" 
                  ? "" 
                  : "hover:bg-muted hover:text-muted-foreground [&:hover]:bg-muted [&:hover]:text-muted-foreground"
              )}
            >
              <Plus className="h-4 w-4" />
            </Button>
            <Button
              variant={currentView === "knowledge" ? "default" : "ghost"}
              size="icon"
              onClick={onSelectKnowledgeBase}
              title="Knowledge Base"
              className={cn(
                "w-10 h-10",
                currentView === "knowledge" 
                  ? "" 
                  : "hover:bg-muted hover:text-muted-foreground [&:hover]:bg-muted [&:hover]:text-muted-foreground"
              )}
            >
              <Library className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {isOpen && (
        <div className="flex-1 overflow-y-auto p-4">
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                placeholder="Search missions..."
                className="w-full pl-10 pr-10 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring transition-all"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={clearSearch}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 hover:bg-muted/50"
                  title="Clear search"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-sm font-medium text-muted-foreground">
                {searchQuery ? `Search Results` : "Recent Missions"}
              </h3>
              {searchQuery && (
                <span className="text-xs text-muted-foreground">
                  {filteredMissions.length} result{filteredMissions.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>
            
            {filteredMissions.length === 0 ? (
              <div className="text-center py-8">
                {searchQuery ? (
                  <div>
                    <Search className="h-8 w-8 text-muted-foreground/50 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground mb-1">No missions found</p>
                    <p className="text-xs text-muted-foreground/70">
                      Try adjusting your search terms
                    </p>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={clearSearch}
                      className="mt-2 h-auto p-0 text-xs"
                    >
                      Clear search
                    </Button>
                  </div>
                ) : (
                  <div>
                    <FileText className="h-8 w-8 text-muted-foreground/50 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No missions yet</p>
                    <p className="text-xs text-muted-foreground/70">Start a new mission to get going</p>
                  </div>
                )}
              </div>
            ) : (
              filteredMissions.map((mission) => (
                <MissionItem
                  key={mission.id}
                  mission={mission}
                  isSelected={selectedMissionId === mission.id && currentView === "chat"}
                  isEditing={editingMissionId === mission.id}
                  editingValue={editingValue}
                  realtimeStatus={getRealtimeMissionStatus(mission.id)}
                  onSelect={onSelectMission}
                  onStartEdit={onStartEditingMission}
                  onEditValueChange={onEditValueChange}
                  onSaveEdit={onSaveEditingMission}
                  onCancelEdit={onCancelEditingMission}
                  onDelete={onDeleteMission}
                />
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}