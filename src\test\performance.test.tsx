import { jest } from '@jest/globals';
import { PerformanceTestUtils, TestUtils } from './utils';
import { But<PERSON> } from '@/components/ui/enhanced-button';
import { LoadingSpinner } from '@/components/ui/loading';
import { ErrorDisplay } from '@/components/ui/error-display';

describe('Performance Tests', () => {
  beforeEach(() => {
    // Clear any existing timers and mocks
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  describe('Component Rendering Performance', () => {
    it('renders Button component within performance budget', async () => {
      const renderTime = await PerformanceTestUtils.measureRenderTime(
        <Button>Test Button</Button>
      );
      
      // But<PERSON> should render in under 16ms (60fps budget)
      expect(renderTime).toBeLessThan(16);
    });

    it('renders LoadingSpinner without performance regression', async () => {
      const renderTime = await PerformanceTestUtils.measureRenderTime(
        <LoadingSpinner size="lg" />
      );
      
      expect(renderTime).toBeLessThan(10);
    });

    it('renders ErrorDisplay efficiently with large error messages', async () => {
      const largeError = 'Error: '.repeat(100);
      
      const renderTime = await PerformanceTestUtils.measureRenderTime(
        <ErrorDisplay error={largeError} />
      );
      
      expect(renderTime).toBeLessThan(20);
    });
  });

  describe('Memory Usage', () => {
    it('does not leak memory when mounting/unmounting Button', async () => {
      await PerformanceTestUtils.testMemoryLeaks(
        <Button>Memory Test</Button>,
        50
      );
    });

    it('properly cleans up LoadingSpinner animations', async () => {
      await PerformanceTestUtils.testMemoryLeaks(
        <LoadingSpinner />,
        30
      );
    });

    it('cleans up ErrorDisplay event listeners', async () => {
      await PerformanceTestUtils.testMemoryLeaks(
        <ErrorDisplay 
          error="Test error" 
          onRetry={() => {}} 
          onDismiss={() => {}} 
        />,
        25
      );
    });
  });

  describe('Large Data Set Performance', () => {
    it('renders mission list efficiently with 1000+ items', async () => {
      const largeMissionList = Array.from({ length: 1000 }, (_, i) => ({
        id: `mission-${i}`,
        title: `Mission ${i}`,
        description: `Description for mission ${i}`,
        status: 'pending' as const,
        priority: 'medium' as const,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      const startTime = performance.now();
      
      // Simulate rendering large list
      const mockRenderList = () => {
        return largeMissionList.map(mission => ({
          ...mission,
          rendered: true
        }));
      };

      const result = mockRenderList();
      const endTime = performance.now();
      
      expect(result).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(100); // Should complete in under 100ms
    });

    it('handles search/filter operations efficiently', async () => {
      const largeDataSet = Array.from({ length: 5000 }, (_, i) => ({
        id: i,
        title: `Item ${i}`,
        searchableText: `searchable content ${i % 100}`
      }));

      const startTime = performance.now();
      
      // Simulate search operation
      const searchResults = largeDataSet.filter(item => 
        item.searchableText.includes('content 50')
      );
      
      const endTime = performance.now();
      
      expect(searchResults.length).toBeGreaterThan(0);
      expect(endTime - startTime).toBeLessThan(50); // Search should be under 50ms
    });
  });

  describe('Network Performance', () => {
    it('handles API response delays gracefully', async () => {
      // Mock slow API response
      global.fetch = jest.fn().mockImplementation(() =>
        new Promise(resolve => {
          setTimeout(() => {
            resolve({
              ok: true,
              json: async () => ({ missions: [] })
            } as Response);
          }, 2000); // 2 second delay
        })
      );

      const startTime = performance.now();
      
      try {
        await fetch('/api/missions');
      } catch (error) {
        // Should timeout before 5 seconds
        const duration = performance.now() - startTime;
        expect(duration).toBeLessThan(5000);
      }
    });

    it('implements proper request debouncing', async () => {
      let requestCount = 0;
      
      global.fetch = jest.fn().mockImplementation(() => {
        requestCount++;
        return Promise.resolve({
          ok: true,
          json: async () => ({ results: [] })
        } as Response);
      });

      // Simulate rapid user input
      const searches = ['a', 'ab', 'abc', 'abcd', 'abcde'];
      
      // In a real implementation, these would be debounced
      for (const search of searches) {
        // Simulate 100ms between keystrokes
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Should only make one request due to debouncing
      // (This would need actual debouncing implementation)
      expect(requestCount).toBeLessThan(searches.length);
    });
  });

  describe('Animation Performance', () => {
    it('maintains 60fps during loading animations', async () => {
      const frameRate = await measureAnimationFrameRate(() => {
        // Simulate animation frame
        return new Promise(resolve => {
          requestAnimationFrame(resolve);
        });
      });

      expect(frameRate).toBeGreaterThan(55); // Close to 60fps
    });

    it('respects reduced motion preferences', async () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
        })),
      });

      // Test component respects preference
      const { container } = TestUtils.render(<LoadingSpinner />);
      const spinner = container.querySelector('.animate-spin');
      
      // Should have reduced motion class
      expect(spinner).toHaveClass('motion-reduce:animate-none');
    });
  });

  describe('Database Performance', () => {
    it('executes database queries within performance budget', async () => {
      const mockQuery = jest.fn().mockResolvedValue([]);
      
      const startTime = performance.now();
      await mockQuery();
      const endTime = performance.now();
      
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('handles concurrent database operations', async () => {
      const concurrentQueries = 10;
      const mockQuery = jest.fn().mockResolvedValue([]);
      
      const startTime = performance.now();
      
      const queries = Array.from({ length: concurrentQueries }, () => mockQuery());
      await Promise.all(queries);
      
      const endTime = performance.now();
      
      // All queries should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(500);
      expect(mockQuery).toHaveBeenCalledTimes(concurrentQueries);
    });
  });

  describe('Bundle Size Performance', () => {
    it('keeps component bundle sizes reasonable', () => {
      // Mock bundle analysis
      const componentSizes = {
        'Button': 2.5, // KB
        'LoadingSpinner': 1.8,
        'ErrorDisplay': 4.2,
        'ResponsiveLayout': 6.1
      };

      Object.entries(componentSizes).forEach(([component, size]) => {
        expect(size).toBeLessThan(10); // No component should exceed 10KB
      });
    });

    it('implements proper code splitting', () => {
      // Mock dynamic import performance
      const mockDynamicImport = jest.fn().mockResolvedValue({
        default: () => 'Lazy component'
      });

      const loadTime = Date.now();
      mockDynamicImport().then(() => {
        const duration = Date.now() - loadTime;
        expect(duration).toBeLessThan(200); // Lazy load under 200ms
      });
    });
  });
});

// Helper function to measure animation frame rate
async function measureAnimationFrameRate(
  animationCallback: () => Promise<void>,
  duration: number = 1000
): Promise<number> {
  let frameCount = 0;
  const startTime = performance.now();
  
  while (performance.now() - startTime < duration) {
    await animationCallback();
    frameCount++;
  }
  
  const actualDuration = performance.now() - startTime;
  return (frameCount / actualDuration) * 1000; // fps
}