import axios, { AxiosInstance } from 'axios';
import { cacheManager } from '@/lib/cache/cache-manager';
import { AgentConfig } from './types';

export interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface DeepSeekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: DeepSeekMessage;
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface EmbeddingResponse {
  object: string;
  data: Array<{
    object: string;
    index: number;
    embedding: number[];
  }>;
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

export class DeepSeekClient {
  private client: AxiosInstance;
  private config: AgentConfig;

  constructor(config: AgentConfig) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.deepseekBaseUrl || 'https://api.deepseek.com/v1',
      headers: {
        'Authorization': `Bearer ${config.deepseekApiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: config.timeout || 30000,
    });
  }

  async chat(
    messages: DeepSeekMessage[],
    options: {
      model?: string;
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
      useCache?: boolean;
    } = {}
  ): Promise<DeepSeekResponse> {
    const model = options.model || 'deepseek-reasoner';
    const useCache = options.useCache !== false; // Default to true
    
    // Create cache key from messages and key options
    const cacheKey = JSON.stringify({
      messages,
      model,
      temperature: options.temperature || 0.7,
      maxTokens: options.maxTokens || 2000,
    });

    // Check cache first if enabled
    if (useCache) {
      const cachedResponse = cacheManager.getCompletion<DeepSeekResponse>(cacheKey, model);
      if (cachedResponse) {
        console.log('📦 Using cached completion');
        return cachedResponse;
      }
    }

    // Use error handler with retry logic and circuit breaker
    const { errorHandler } = await import('../error');
    
    const completion = await errorHandler.withRetry(
      async () => {
        console.log('🔄 Generating new completion');
        const response = await this.client.post('/chat/completions', {
          model,
          messages,
          temperature: options.temperature || 0.7,
          max_tokens: options.maxTokens || 2000,
          stream: options.stream || false,
        });

        return response.data as DeepSeekResponse;
      },
      'deepseek-chat',
      {
        maxAttempts: 3,
        retryCondition: (error: Error) => {
          // Retry on rate limits, timeouts, and server errors
          const message = error.message.toLowerCase();
          return message.includes('rate') || 
                 message.includes('timeout') || 
                 message.includes('503') || 
                 message.includes('502') ||
                 message.includes('504');
        }
      }
    );

    // Cache the completion if enabled and not streaming
    if (useCache && !options.stream) {
      cacheManager.setCompletion(cacheKey, model, completion);
    }

    return completion;
  }

  async generateEmbedding(text: string, model: string = 'deepseek-embedding'): Promise<number[]> {
    // Check cache first
    const cachedEmbedding = cacheManager.getEmbedding(text, model);
    if (cachedEmbedding) {
      console.log('📦 Using cached embedding');
      return cachedEmbedding;
    }

    // Use error handler with retry logic
    const { errorHandler } = await import('../error');
    
    const embeddingResponse = await errorHandler.withRetry(
      async () => {
        console.log('🔄 Generating new embedding');
        const response = await this.client.post('/embeddings', {
          model,
          input: text,
        });

        return response.data as EmbeddingResponse;
      },
      'deepseek-embedding',
      {
        maxAttempts: 2, // Fewer retries for embeddings
        retryCondition: (error: Error) => {
          const message = error.message.toLowerCase();
          return message.includes('rate') || 
                 message.includes('timeout') || 
                 message.includes('503');
        }
      }
    );

    const embedding = embeddingResponse.data[0].embedding;
    
    // Cache the embedding for future use
    cacheManager.setEmbedding(text, embedding, model);
    
    return embedding;
  }

  async generatePlan(mission: string, context?: string): Promise<string> {
    const messages: DeepSeekMessage[] = [
      {
        role: 'system',
        content: `You are an expert service management AI agent with advanced planning capabilities. Your task is to create detailed, executable plans for missions.

Key principles:
1. Break down complex missions into clear, actionable tasks
2. Consider dependencies between tasks
3. Estimate realistic timeframes
4. Identify required tools and resources
5. Plan for potential risks and contingencies
6. Ensure each task has clear success criteria

Return your response as a structured JSON plan with the following format:
{
  "title": "Plan title",
  "description": "Plan description",
  "estimatedDuration": 120,
  "tasks": [
    {
      "title": "Task title",
      "description": "Task description",
      "priority": 0,
      "toolName": "tool_name",
      "toolParams": {},
      "dependencies": [],
      "estimatedDuration": 30
    }
  ],
  "reasoning": "Explanation of the planning approach",
  "confidence": 0.85
}`
      },
      {
        role: 'user',
        content: `Mission: ${mission}${context ? `\n\nContext: ${context}` : ''}`
      }
    ];

    const response = await this.chat(messages, { 
      temperature: 0.3,
      useCache: true // Cache plans for similar missions
    });
    
    return response.choices[0].message.content;
  }

  async reflect(
    type: 'progress_assessment' | 'plan_optimization' | 'error_analysis' | 'success_analysis',
    data: any
  ): Promise<string> {
    const systemPrompts = {
      progress_assessment: 'You are analyzing the progress of a mission execution. Assess what has been completed, what remains, and any issues that need attention.',
      plan_optimization: 'You are optimizing an execution plan based on current progress and learnings. Suggest improvements to increase efficiency and success probability.',
      error_analysis: 'You are analyzing a failure or error that occurred during mission execution. Identify root causes and suggest corrective actions.',
      success_analysis: 'You are analyzing a successful task or mission completion. Extract insights and best practices for future use.'
    };

    const messages: DeepSeekMessage[] = [
      {
        role: 'system',
        content: `${systemPrompts[type]}

Return your analysis as a structured JSON response:
{
  "insights": ["insight 1", "insight 2"],
  "recommendations": ["recommendation 1", "recommendation 2"],
  "confidence": 0.8,
  "reasoning": "Detailed explanation of your analysis"
}`
      },
      {
        role: 'user',
        content: JSON.stringify(data, null, 2)
      }
    ];

    const response = await this.chat(messages, { 
      temperature: 0.4,
      useCache: false // Don't cache reflections as they're context-specific
    });
    
    return response.choices[0].message.content;
  }

  async selectTool(task: any, availableTools: string[]): Promise<string> {
    const messages: DeepSeekMessage[] = [
      {
        role: 'system',
        content: `You are selecting the most appropriate tool for a given task. Consider the task requirements and available tools.

Available tools: ${availableTools.join(', ')}

Return your response as JSON:
{
  "selectedTool": "tool_name",
  "reasoning": "Why this tool is most appropriate",
  "confidence": 0.9
}`
      },
      {
        role: 'user',
        content: `Task: ${JSON.stringify(task, null, 2)}`
      }
    ];

    const response = await this.chat(messages, { 
      temperature: 0.2,
      useCache: true // Cache tool selections for similar tasks
    });
    
    return response.choices[0].message.content;
  }

  async assessCompletion(mission: any, result: any): Promise<string> {
    const messages: DeepSeekMessage[] = [
      {
        role: 'system',
        content: `You are assessing whether a mission has been successfully completed based on the original goals and the execution results.

Return your assessment as JSON:
{
  "completed": true/false,
  "completionPercentage": 0.95,
  "reasoning": "Detailed explanation",
  "remainingWork": ["item 1", "item 2"],
  "confidence": 0.9
}`
      },
      {
        role: 'user',
        content: `Mission: ${JSON.stringify(mission, null, 2)}\n\nResult: ${JSON.stringify(result, null, 2)}`
      }
    ];

    const response = await this.chat(messages, {
      temperature: 0.3,
      useCache: false // Don't cache completion assessments
    });

    return response.choices[0].message.content;
  }

  /**
   * Generate response based ONLY on retrieved knowledge base data
   * CRITICAL: This method enforces the knowledge-base-first principle
   */
  async generateKnowledgeBasedResponse(
    query: string,
    knowledgeData: any[],
    context?: string
  ): Promise<string> {
    if (!knowledgeData || knowledgeData.length === 0) {
      return "I don't have enough information in the knowledge base to answer this question. No relevant data was found.";
    }

    // Prepare knowledge context
    const knowledgeContext = knowledgeData.map((item, index) => {
      return `[Source ${index + 1}] ${item.title || 'Document'}: ${item.content || item.snippet || 'No content available'}`;
    }).join('\n\n');

    const messages: DeepSeekMessage[] = [
      {
        role: 'system',
        content: `You are an AI assistant that MUST base all responses SOLELY on the provided knowledge base data.

CRITICAL RULES:
1. You can ONLY use information from the provided knowledge base sources
2. You CANNOT make assumptions or use external knowledge
3. If the knowledge base doesn't contain enough information, you MUST say "I don't have enough information in the knowledge base"
4. Always cite your sources when providing information
5. Be precise and factual based only on the retrieved data

The user's query is: "${query}"

Available Knowledge Base Sources:
${knowledgeContext}

${context ? `Additional Context: ${context}` : ''}

Provide a comprehensive answer based ONLY on the knowledge base sources above.`
      },
      {
        role: 'user',
        content: query
      }
    ];

    const response = await this.chat(messages, {
      temperature: 0.2, // Low temperature for factual responses
      useCache: false,
      maxTokens: 1000
    });

    return response.choices[0].message.content;
  }

  /**
   * Analyze incidents based ONLY on retrieved incident data
   */
  async analyzeIncidents(
    query: string,
    incidentData: any[],
    filters?: Record<string, any>
  ): Promise<string> {
    if (!incidentData || incidentData.length === 0) {
      return "No incidents found in the knowledge base matching the specified criteria.";
    }

    // Prepare incident context
    const incidentContext = incidentData.map((incident, index) => {
      return `[Incident ${index + 1}]
Title: ${incident.title || 'Unknown'}
Severity: ${incident.severity || 'Unknown'}
Status: ${incident.status || 'Unknown'}
Service: ${incident.service || 'Unknown'}
Description: ${incident.description || 'No description available'}`;
    }).join('\n\n');

    const filterContext = filters ? `Filters applied: ${JSON.stringify(filters)}` : '';

    const messages: DeepSeekMessage[] = [
      {
        role: 'system',
        content: `You are analyzing incident data from the knowledge base. Provide analysis based ONLY on the incident data provided.

CRITICAL RULES:
1. Base your analysis ONLY on the provided incident data
2. Provide specific counts, severity breakdowns, and patterns
3. Do not make assumptions about incidents not in the data
4. If asked about time periods not covered in the data, state this clearly

Query: "${query}"
${filterContext}

Incident Data:
${incidentContext}

Provide a comprehensive incident analysis with:
1. Total incident count
2. Severity breakdown (critical, high, medium, low)
3. Status summary
4. Service impact analysis
5. Key patterns or trends (only if evident in the data)`
      },
      {
        role: 'user',
        content: query
      }
    ];

    const response = await this.chat(messages, {
      temperature: 0.1, // Very low temperature for factual incident analysis
      useCache: false,
      maxTokens: 800
    });

    return response.choices[0].message.content;
  }
}
