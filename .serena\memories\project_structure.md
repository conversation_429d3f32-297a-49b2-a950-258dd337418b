# Project Structure

This Next.js application is a service management assistant with the following key components:

## Main Components
- `src/app/page.tsx` - Main page that renders MissionWorkspace
- `src/components/MissionWorkspace.tsx` - Main workspace interface with chat, mission management, and integrated progress display
- `src/components/CreateMission.tsx` - Mission creation interface
- `src/components/KnowledgeBase.tsx` - Knowledge management interface with integrated Confluence functionality
- `src/components/TestComponent.tsx` - Test component for development

## Key Features
```
my-next-assistant/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes
│   │   │   ├── confluence/    # Confluence integration endpoints
│   │   │   └── missions/      # Mission management endpoints
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx          # Home page (renders MissionWorkspace)
│   ├── components/            # React components
│   │   ├── ui/               # Base UI components (Shadcn/ui)
│   │   ├── CreateMission.tsx
│   │   ├── KnowledgeBase.tsx
│   │   ├── MissionWorkspace.tsx
│   │   └── TestComponent.tsx
│   └── lib/                   # Core logic
│       ├── agent/            # AI agent implementation
│       │   ├── core.ts       # Main agent classes
│       │   ├── deepseek.ts   # DeepSeek client
│       │   ├── types.ts      # Type definitions
│       │   └── tools/        # Agent tools
│       ├── db/               # Database operations
│       │   ├── index.ts      # Database initialization
│       │   └── schema.ts     # Drizzle schema
│       └── utils.ts          # Utility functions
├── data/                      # SQLite database files
├── doc/                       # Documentation
├── drizzle/                   # Database migrations
├── public/                    # Static assets
├── scripts/                   # Utility scripts
├── package.json              # Dependencies and scripts
├── tsconfig.json             # TypeScript configuration
├── drizzle.config.ts         # Database configuration
└── next.config.ts            # Next.js configuration
```

## Key Files
- **Entry Point**: `src/app/page.tsx` renders `MissionWorkspace`
- **Agent Core**: `src/lib/agent/core.ts` contains main AI agent implementation
- **Database**: `src/lib/db/schema.ts` defines all database tables
- **API**: RESTful endpoints in `src/app/api/` for missions and Confluence