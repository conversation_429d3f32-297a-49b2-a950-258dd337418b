import { ChromaClient, IncludeEnum } from 'chromadb';
import { CacheManager } from '../cache/cache-manager';
import { <PERSON>rror<PERSON>and<PERSON> } from '../error/error-handler';
import { ErrorCategory, ErrorSeverity } from '../error/types';

export interface ChromaDocument {
  id: string;
  content: string;
  metadata: Record<string, any>;
}

export interface ChromaSearchResult {
  id: string;
  content: string;
  metadata: Record<string, any>;
  similarity: number;
}

export interface ChromaCollection {
  name: string;
  documentCount: number;
  metadata?: Record<string, any>;
}

export interface ChromaQueryOptions {
  nResults?: number;
  where?: Record<string, any>;
  whereDocument?: Record<string, any>;
  include?: Array<'documents' | 'metadatas' | 'distances' | 'embeddings'>;
}

/**
 * ChromaDB-powered vector store for efficient similarity search
 * Integrates with DeepSeek for embeddings and provides high-performance retrieval
 */
export class ChromaVectorStore {
  private client: ChromaClient;
  private collections: Map<string, any> = new Map();
  private cacheManager: CacheManager;
  private errorHandler: ErrorHandler;

  constructor(host: string = 'localhost', port: number = 8000) {
    this.cacheManager = CacheManager.getInstance();
    this.errorHandler = ErrorHandler.getInstance();
    
    try {
      this.client = new ChromaClient({
        path: `http://${host}:${port}`,
      });
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to initialize ChromaDB client',
        ErrorCategory.SYSTEM,
        ErrorSeverity.HIGH,
        'CHROMA_INIT_ERROR',
        { service: 'chromadb', endpoint: `${host}:${port}` },
        error as Error
      );
      throw serviceError;
    }
  }

  async createCollection(
    name: string,
    metadata?: Record<string, any>
  ): Promise<any> {
    try {
      const collection = await this.client.createCollection({
        name,
        metadata,
      });
      
      this.collections.set(name, collection);
      return collection;
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to create collection',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'CHROMA_CREATE_COLLECTION_ERROR',
        { operation: 'create_collection', collection: name },
        error as Error
      );
      throw serviceError;
    }
  }

  async getCollection(name: string): Promise<any | null> {
    try {
      if (this.collections.has(name)) {
        return this.collections.get(name)!;
      }

      const collection = await this.client.getCollection({
        name,
      });
      
      this.collections.set(name, collection);
      return collection;
    } catch (error) {
      // Don't throw error for missing collection, just return null
      return null;
    }
  }

  async deleteCollection(name: string): Promise<void> {
    try {
      await this.client.deleteCollection({
        name,
      });
      
      this.collections.delete(name);
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to delete collection',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'CHROMA_DELETE_COLLECTION_ERROR',
        { operation: 'delete_collection', collection: name },
        error as Error
      );
      throw serviceError;
    }
  }

  async addDocuments(
    collectionName: string,
    documents: ChromaDocument[]
  ): Promise<void> {
    try {
      const collection = await this.getCollection(collectionName);
      if (!collection) {
        throw new Error(`Collection ${collectionName} not found`);
      }

      const ids = documents.map(doc => doc.id);
      const texts = documents.map(doc => doc.content);
      const metadatas = documents.map(doc => doc.metadata);

      await collection.add({
        ids,
        documents: texts,
        metadatas,
      });
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to add documents to collection',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'CHROMA_ADD_DOCUMENTS_ERROR',
        { operation: 'add_documents', collection: collectionName, count: documents.length },
        error as Error
      );
      throw serviceError;
    }
  }

  async queryDocuments(
    collectionName: string,
    query: string,
    options: ChromaQueryOptions = {}
  ): Promise<ChromaSearchResult[]> {
    try {
      const cacheKey = `chroma:query:${collectionName}:${query}:${JSON.stringify(options)}`;
      const cachedResults = this.cacheManager.getSearchResults(query, { collectionName, ...options });
      
      if (cachedResults) {
        return cachedResults;
      }

      const collection = await this.getCollection(collectionName);
      if (!collection) {
        throw new Error(`Collection ${collectionName} not found`);
      }

      const results = await collection.query({
        queryTexts: [query],
        nResults: options.nResults || 10,
        where: options.where,
        include: options.include || [IncludeEnum.documents, IncludeEnum.metadatas, IncludeEnum.distances],
      });

      const documents: ChromaSearchResult[] = [];
      
      if (results.ids && results.ids[0]) {
        for (let i = 0; i < results.ids[0].length; i++) {
          const id = results.ids[0][i];
          const content = results.documents?.[0]?.[i] || '';
          const metadata = results.metadatas?.[0]?.[i] || {};
          const distance = results.distances?.[0]?.[i];
          
          const similarity = distance !== null && distance !== undefined ? 1 - distance : 0;

          documents.push({
            id,
            content,
            metadata,
            similarity,
          });
        }
      }

      this.cacheManager.setSearchResults(query, { collectionName, ...options }, documents);
      return documents;
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to query documents',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'CHROMA_QUERY_DOCUMENTS_ERROR',
        { operation: 'query_documents', collection: collectionName, query: query.substring(0, 100) },
        error as Error
      );
      throw serviceError;
    }
  }

  async updateDocuments(
    collectionName: string,
    documents: ChromaDocument[]
  ): Promise<void> {
    try {
      const collection = await this.getCollection(collectionName);
      if (!collection) {
        throw new Error(`Collection ${collectionName} not found`);
      }

      const ids = documents.map(doc => doc.id);
      const texts = documents.map(doc => doc.content);
      const metadatas = documents.map(doc => doc.metadata);

      await collection.update({
        ids,
        documents: texts,
        metadatas,
      });
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to update documents',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'CHROMA_UPDATE_DOCUMENTS_ERROR',
        { operation: 'update_documents', collection: collectionName, count: documents.length },
        error as Error
      );
      throw serviceError;
    }
  }

  async deleteDocuments(
    collectionName: string,
    ids: string[]
  ): Promise<void> {
    try {
      const collection = await this.getCollection(collectionName);
      if (!collection) {
        throw new Error(`Collection ${collectionName} not found`);
      }

      await collection.delete({
        ids,
      });
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to delete documents',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'CHROMA_DELETE_DOCUMENTS_ERROR',
        { operation: 'delete_documents', collection: collectionName, count: ids.length },
        error as Error
      );
      throw serviceError;
    }
  }

  async listCollections(): Promise<string[]> {
    try {
      const collections = await this.client.listCollections();
      return collections.map((c: any) => c.name);
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to list collections',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'CHROMA_LIST_COLLECTIONS_ERROR',
        { operation: 'list_collections' },
        error as Error
      );
      throw serviceError;
    }
  }

  async getCollectionInfo(name: string): Promise<ChromaCollection | null> {
    try {
      const collection = await this.getCollection(name);
      if (!collection) {
        return null;
      }

      const count = await collection.count();
      return {
        name,
        documentCount: count,
        metadata: {},
      };
    } catch (error) {
      return null;
    }
  }

  async reset(): Promise<void> {
    try {
      await this.client.reset();
      this.collections.clear();
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to reset ChromaDB',
        ErrorCategory.SYSTEM,
        ErrorSeverity.HIGH,
        'CHROMA_RESET_ERROR',
        { operation: 'reset' },
        error as Error
      );
      throw serviceError;
    }
  }
}

// Export singleton factory
export function createChromaVectorStore(host: string = 'localhost', port: number = 8000): ChromaVectorStore {
  return new ChromaVectorStore(host, port);
}