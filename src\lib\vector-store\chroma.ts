import { ChromaApi, Collection, OpenAIEmbeddingFunction } from 'chromadb';
import { DeepSeekClient } from '@/lib/agent/deepseek';

export interface VectorDocument {
  id: string;
  content: string;
  metadata: Record<string, any>;
  embedding?: number[];
}

export interface VectorSearchResult {
  id: string;
  content: string;
  metadata: Record<string, any>;
  score: number;
}

export interface VectorSearchOptions {
  limit?: number;
  threshold?: number;
  filter?: Record<string, any>;
}

/**
 * ChromaDB-powered vector store for efficient similarity search
 * Integrates with DeepSeek for embeddings and provides high-performance retrieval
 */
export class ChromaVectorStore {
  private client: ChromaApi;
  private deepseek: DeepSeekClient;
  private collections: Map<string, Collection> = new Map();

  constructor(deepseek: DeepSeekClient, chromaUrl: string = 'http://localhost:8000') {
    this.deepseek = deepseek;
    this.client = new ChromaApi({
      path: chromaUrl,
    });
  }

  /**
   * Initialize or get a collection
   */
  async getCollection(collectionName: string): Promise<Collection> {
    if (this.collections.has(collectionName)) {
      return this.collections.get(collectionName)!;
    }

    try {
      // Try to get existing collection first
      const collection = await this.client.getCollection({
        name: collectionName,
      });
      this.collections.set(collectionName, collection);
      return collection;
    } catch (error) {
      // Create collection if it doesn't exist
      const collection = await this.client.createCollection({
        name: collectionName,
        metadata: {
          description: `Vector collection for ${collectionName}`,
          created_at: new Date().toISOString(),
        },
      });
      this.collections.set(collectionName, collection);
      return collection;
    }
  }

  /**
   * Add documents to the vector store
   */
  async addDocuments(
    collectionName: string,
    documents: VectorDocument[]
  ): Promise<void> {
    try {
      const collection = await this.getCollection(collectionName);

      // Generate embeddings for documents that don't have them
      const documentsWithEmbeddings = await Promise.all(
        documents.map(async (doc) => {
          if (!doc.embedding) {
            doc.embedding = await this.deepseek.generateEmbedding(doc.content);
          }
          return doc;
        })
      );

      // Prepare data for Chroma
      const ids = documentsWithEmbeddings.map(doc => doc.id);
      const embeddings = documentsWithEmbeddings.map(doc => doc.embedding!);
      const metadatas = documentsWithEmbeddings.map(doc => ({
        ...doc.metadata,
        content: doc.content, // Store content in metadata for retrieval
      }));
      const documents_data = documentsWithEmbeddings.map(doc => doc.content);

      // Add to collection
      await collection.add({
        ids,
        embeddings,
        metadatas,
        documents: documents_data,
      });

      console.log(`Added ${documents.length} documents to collection ${collectionName}`);
    } catch (error) {
      console.error('Error adding documents to vector store:', error);
      throw new Error(`Failed to add documents: ${error}`);
    }
  }

  /**
   * Search for similar documents
   */
  async search(
    collectionName: string,
    query: string,
    options: VectorSearchOptions = {}
  ): Promise<VectorSearchResult[]> {
    try {
      const { limit = 10, threshold = 0.7, filter } = options;
      const collection = await this.getCollection(collectionName);

      // Generate query embedding
      const queryEmbedding = await this.deepseek.generateEmbedding(query);

      // Perform vector search
      const results = await collection.query({
        queryEmbeddings: [queryEmbedding],
        nResults: limit,
        where: filter,
        include: ['documents', 'metadatas', 'distances'],
      });

      // Process results
      const searchResults: VectorSearchResult[] = [];
      
      if (results.ids && results.ids[0] && results.documents && results.metadatas && results.distances) {
        for (let i = 0; i < results.ids[0].length; i++) {
          const distance = results.distances[0][i];
          const similarity = 1 - distance; // Convert distance to similarity

          if (similarity >= threshold) {
            searchResults.push({
              id: results.ids[0][i],
              content: results.documents[0][i] || '',
              metadata: results.metadatas[0][i] || {},
              score: similarity,
            });
          }
        }
      }

      return searchResults.sort((a, b) => b.score - a.score);
    } catch (error) {
      console.error('Error searching vector store:', error);
      throw new Error(`Search failed: ${error}`);
    }
  }

  /**
   * Update document in the vector store
   */
  async updateDocument(
    collectionName: string,
    documentId: string,
    content: string,
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      const collection = await this.getCollection(collectionName);
      const embedding = await this.deepseek.generateEmbedding(content);

      await collection.update({
        ids: [documentId],
        embeddings: [embedding],
        metadatas: [{ ...metadata, content }],
        documents: [content],
      });

      console.log(`Updated document ${documentId} in collection ${collectionName}`);
    } catch (error) {
      console.error('Error updating document:', error);
      throw new Error(`Failed to update document: ${error}`);
    }
  }

  /**
   * Delete document from the vector store
   */
  async deleteDocument(collectionName: string, documentId: string): Promise<void> {
    try {
      const collection = await this.getCollection(collectionName);
      
      await collection.delete({
        ids: [documentId],
      });

      console.log(`Deleted document ${documentId} from collection ${collectionName}`);
    } catch (error) {
      console.error('Error deleting document:', error);
      throw new Error(`Failed to delete document: ${error}`);
    }
  }

  /**
   * Get document by ID
   */
  async getDocument(collectionName: string, documentId: string): Promise<VectorDocument | null> {
    try {
      const collection = await this.getCollection(collectionName);
      
      const results = await collection.get({
        ids: [documentId],
        include: ['documents', 'metadatas', 'embeddings'],
      });

      if (results.ids && results.ids.length > 0) {
        return {
          id: results.ids[0],
          content: results.documents?.[0] || '',
          metadata: results.metadatas?.[0] || {},
          embedding: results.embeddings?.[0] || undefined,
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting document:', error);
      return null;
    }
  }

  /**
   * List all collections
   */
  async listCollections(): Promise<string[]> {
    try {
      const collections = await this.client.listCollections();
      return collections.map(c => c.name);
    } catch (error) {
      console.error('Error listing collections:', error);
      return [];
    }
  }

  /**
   * Get collection stats
   */
  async getCollectionStats(collectionName: string): Promise<{
    count: number;
    name: string;
    metadata: Record<string, any>;
  } | null> {
    try {
      const collection = await this.getCollection(collectionName);
      const count = await collection.count();
      
      return {
        count,
        name: collectionName,
        metadata: collection.metadata || {},
      };
    } catch (error) {
      console.error('Error getting collection stats:', error);
      return null;
    }
  }

  /**
   * Delete collection
   */
  async deleteCollection(collectionName: string): Promise<void> {
    try {
      await this.client.deleteCollection({ name: collectionName });
      this.collections.delete(collectionName);
      console.log(`Deleted collection ${collectionName}`);
    } catch (error) {
      console.error('Error deleting collection:', error);
      throw new Error(`Failed to delete collection: ${error}`);
    }
  }

  /**
   * Batch search across multiple queries
   */
  async batchSearch(
    collectionName: string,
    queries: string[],
    options: VectorSearchOptions = {}
  ): Promise<VectorSearchResult[][]> {
    try {
      const results = await Promise.all(
        queries.map(query => this.search(collectionName, query, options))
      );
      return results;
    } catch (error) {
      console.error('Error in batch search:', error);
      throw new Error(`Batch search failed: ${error}`);
    }
  }

  /**
   * Health check for Chroma connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.client.heartbeat();
      return true;
    } catch (error) {
      console.error('Chroma health check failed:', error);
      return false;
    }
  }
}

// Export singleton factory
export function createChromaVectorStore(deepseek: DeepSeekClient, chromaUrl?: string): ChromaVectorStore {
  return new ChromaVectorStore(deepseek, chromaUrl);
}