# Technology Stack

## Frontend
- **Framework**: Next.js 15.3.5 with App Router
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS 4
- **UI Components**: Radix UI primitives with Shadcn/ui
- **Icons**: Lucide React

## Backend  
- **Runtime**: Node.js
- **Framework**: Next.js API routes
- **WebSockets**: Fastify with @fastify/websocket (for real-time updates)

## AI & ML
- **AI Models**: DeepSeek reasoning model for planning and decision-making
- **Embeddings**: DeepSeek embedding model for semantic search
- **Document Processing**: LlamaIndex.js for document processing and retrieval
- **Content Parsing**: Cheerio for HTML content extraction

## Database
- **Primary DB**: SQLite3 (libsql)
- **ORM**: Drizzle ORM with TypeScript support
- **Vector Storage**: Embedded vector database for embeddings
- **Caching**: Node.js native caching (node-cache)

## Development Tools
- **Package Manager**: npm
- **Build Tool**: Next.js with Turbopack (dev mode)
- **Database Migrations**: Drizzle Kit
- **TypeScript**: Strict mode enabled
- **Linting**: ESLint with Next.js config

## Key Dependencies
- `llamaindex`: Document processing and retrieval
- `axios`: HTTP client for external API calls
- `uuid` & `@paralleldrive/cuid2`: ID generation
- `date-fns`: Date manipulation
- `ws`: WebSocket client/server