// Comprehensive error handling system exports

export * from './types';
export * from './error-handler';
export * from './monitoring';

// Convenience exports
export { errorHandler } from './error-handler';
export { errorMonitor, defaultAlertRules } from './monitoring';

// Error factory functions for common error types
export function createNetworkError(message: string, context: any = {}) {
  const { errorHandler } = require('./error-handler');
  return errorHandler.createError(
    message,
    'network' as any,
    'high' as any,
    'NETWORK_ERROR',
    context
  );
}

export function createValidationError(message: string, context: any = {}) {
  const { errorHandler } = require('./error-handler');
  return errorHandler.createError(
    message,
    'validation' as any,
    'medium' as any,
    'VALIDATION_ERROR',
    context
  );
}

export function createApiError(message: string, context: any = {}) {
  const { errorHandler } = require('./error-handler');
  return errorHandler.createError(
    message,
    'api' as any,
    'high' as any,
    'API_ERROR',
    context
  );
}

export function createDatabaseError(message: string, context: any = {}) {
  const { errorHandler } = require('./error-handler');
  return errorHandler.createError(
    message,
    'database' as any,
    'high' as any,
    'DATABASE_ERROR',
    context
  );
}

export function createSystemError(message: string, context: any = {}) {
  const { errorHandler } = require('./error-handler');
  return errorHandler.createError(
    message,
    'system' as any,
    'critical' as any,
    'SYSTEM_ERROR',
    context
  );
}