import { EventEmitter } from 'events';

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  clientId?: string;
  missionId?: string;
}

export interface MissionUpdate {
  missionId: string;
  status: 'pending' | 'planning' | 'executing' | 'completed' | 'failed' | 'paused';
  progress?: {
    currentTask?: string;
    completedTasks: number;
    totalTasks: number;
    percentage: number;
  };
  message?: string;
  data?: any;
}

export interface TaskUpdate {
  missionId: string;
  taskId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  result?: any;
  error?: string;
  startTime?: string;
  endTime?: string;
}

export interface LogUpdate {
  missionId: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: string;
  context?: any;
}

export interface ConnectionStatus {
  connected: boolean;
  connecting: boolean;
  error?: string;
  clientId?: string;
  lastPong?: string;
}

/**
 * WebSocket client for real-time communication with the agent server
 */
export class WebSocketClient extends EventEmitter {
  private ws: WebSocket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private pingInterval: NodeJS.Timeout | null = null;
  private connectionTimeout: NodeJS.Timeout | null = null;
  private status: ConnectionStatus = { connected: false, connecting: false };
  private subscribedMissions: Set<string> = new Set();

  constructor(wsUrl?: string) {
    super();
    
    // Default to localhost:8080 for development
    this.url = wsUrl || 
      (typeof window !== 'undefined' && window.location.hostname === 'localhost' 
        ? 'ws://localhost:8080/ws'
        : typeof window !== 'undefined' 
          ? `wss://${window.location.host}/ws`
          : 'ws://localhost:8080/ws');
  }

  /**
   * Connect to the WebSocket server
   */
  public connect(): Promise<void> {
    if (this.status.connected || this.status.connecting) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      this.status.connecting = true;
      this.emit('status', this.status);

      try {
        this.ws = new WebSocket(this.url);

        // Connection timeout
        this.connectionTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close();
            reject(new Error('Connection timeout'));
          }
        }, 10000);

        this.ws.onopen = () => {
          if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = null;
          }

          this.status = { connected: true, connecting: false };
          this.reconnectAttempts = 0;
          this.reconnectDelay = 1000;

          this.setupPing();
          this.resubscribeToMissions();

          this.emit('connected');
          this.emit('status', this.status);
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          this.cleanup();
          
          if (event.code !== 1000) { // Not a normal closure
            this.handleDisconnection();
          } else {
            this.status = { connected: false, connecting: false };
            this.emit('disconnected');
            this.emit('status', this.status);
          }
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.status = { 
            connected: false, 
            connecting: false, 
            error: 'Connection error' 
          };
          this.emit('error', error);
          this.emit('status', this.status);
          reject(error);
        };

      } catch (error) {
        this.status = { 
          connected: false, 
          connecting: false, 
          error: (error as Error).message 
        };
        this.emit('status', this.status);
        reject(error);
      }
    });
  }

  /**
   * Disconnect from the WebSocket server
   */
  public disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
    }
    this.cleanup();
  }

  /**
   * Subscribe to updates for a specific mission
   */
  public subscribeMission(missionId: string): void {
    this.subscribedMissions.add(missionId);
    
    if (this.status.connected) {
      this.send({
        type: 'mission.subscribe',
        payload: { missionId },
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Unsubscribe from updates for a specific mission
   */
  public unsubscribeMission(missionId: string): void {
    this.subscribedMissions.delete(missionId);
    
    if (this.status.connected) {
      this.send({
        type: 'mission.unsubscribe',
        payload: { missionId },
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Request current status for a mission
   */
  public requestMissionStatus(missionId: string): void {
    if (this.status.connected) {
      this.send({
        type: 'mission.status.request',
        payload: { missionId },
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Get current connection status
   */
  public getStatus(): ConnectionStatus {
    return { ...this.status };
  }

  /**
   * Send a message to the server
   */
  private send(message: Partial<WebSocketMessage>): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  /**
   * Handle incoming messages from the server
   */
  private handleMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case 'connection.established':
        this.status.clientId = message.payload.clientId;
        this.emit('status', this.status);
        break;

      case 'pong':
        this.status.lastPong = message.payload.timestamp;
        this.emit('status', this.status);
        break;

      case 'mission.update':
        this.emit('mission.update', message.payload as MissionUpdate);
        break;

      case 'task.update':
        this.emit('task.update', message.payload as TaskUpdate);
        break;

      case 'log.update':
        this.emit('log.update', message.payload as LogUpdate);
        break;

      case 'reflection.update':
        this.emit('reflection.update', message.payload);
        break;

      case 'mission.subscribed':
        this.emit('mission.subscribed', message.payload.missionId);
        break;

      case 'mission.unsubscribed':
        this.emit('mission.unsubscribed', message.payload.missionId);
        break;

      case 'error':
        this.emit('server.error', message.payload);
        break;

      default:
        this.emit('message', message);
    }
  }

  /**
   * Handle disconnection and attempt reconnection
   */
  private handleDisconnection(): void {
    this.status = { connected: false, connecting: false };
    this.emit('disconnected');
    this.emit('status', this.status);

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
      this.status.error = 'Max reconnection attempts reached';
      this.emit('status', this.status);
    }
  }

  /**
   * Setup ping/pong mechanism to keep connection alive
   */
  private setupPing(): void {
    this.pingInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({
          type: 'ping',
          payload: { timestamp: new Date().toISOString() },
          timestamp: new Date().toISOString(),
        });
      }
    }, 30000); // Ping every 30 seconds
  }

  /**
   * Resubscribe to missions after reconnection
   */
  private resubscribeToMissions(): void {
    for (const missionId of this.subscribedMissions) {
      this.subscribeMission(missionId);
    }
  }

  /**
   * Clean up resources
   */
  private cleanup(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }

    this.status = { connected: false, connecting: false };
    this.ws = null;
  }
}

// React Hook for WebSocket connection
export function useWebSocket(wsUrl?: string) {
  const [client] = useState(() => new WebSocketClient(wsUrl));
  const [status, setStatus] = useState<ConnectionStatus>(client.getStatus());
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const handleStatus = (newStatus: ConnectionStatus) => {
      setStatus(newStatus);
      setIsConnected(newStatus.connected);
    };

    client.on('status', handleStatus);
    
    // Auto-connect when the hook is used
    client.connect().catch(error => {
      console.error('Failed to connect WebSocket:', error);
    });

    return () => {
      client.off('status', handleStatus);
      client.disconnect();
    };
  }, [client]);

  return {
    client,
    status,
    isConnected,
    connect: () => client.connect(),
    disconnect: () => client.disconnect(),
    subscribeMission: (missionId: string) => client.subscribeMission(missionId),
    unsubscribeMission: (missionId: string) => client.unsubscribeMission(missionId),
    requestMissionStatus: (missionId: string) => client.requestMissionStatus(missionId),
  };
}

// Export singleton client for direct use
let globalClient: WebSocketClient | null = null;

export function getWebSocketClient(wsUrl?: string): WebSocketClient {
  if (!globalClient) {
    globalClient = new WebSocketClient(wsUrl);
  }
  return globalClient;
}

// Add React imports for the hook
import { useState, useEffect } from 'react';