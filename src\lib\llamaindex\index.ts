import { 
  Document,
  VectorStoreIndex,
  SimpleDirectoryReader,
  StorageContext,
  serviceContextFromDefaults,
  OpenAI,
  SimpleNodeParser,
  HuggingFaceEmbedding,
} from 'llamaindex';
import { DeepSeekClient } from '@/lib/agent/deepseek';
import db from '@/lib/db';
import * as schema from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export interface DocumentMetadata {
  title: string;
  sourceUrl: string;
  sourceType: 'confluence' | 'file' | 'web';
  tags?: string[];
  lastIndexed: Date;
}

export interface ProcessedDocument {
  id: string;
  content: string;
  metadata: DocumentMetadata;
  embedding: number[];
  chunks: DocumentChunk[];
}

export interface DocumentChunk {
  id: string;
  content: string;
  startIndex: number;
  endIndex: number;
  embedding: number[];
  metadata: Record<string, any>;
}

export interface SearchResult {
  document: ProcessedDocument;
  chunk: DocumentChunk;
  score: number;
  snippet: string;
}

/**
 * LlamaIndex-powered document processing and retrieval service
 * Integrates with DeepSeek for embeddings and provides advanced document operations
 */
export class DocumentProcessor {
  private deepseek: DeepSeekClient;
  private nodeParser: SimpleNodeParser;
  private vectorIndex?: VectorStoreIndex;

  constructor(deepseek: DeepSeekClient) {
    this.deepseek = deepseek;
    
    // Configure node parser for optimal chunking
    this.nodeParser = new SimpleNodeParser({
      chunkSize: 1024,
      chunkOverlap: 50,
    });
  }

  /**
   * Process a document with LlamaIndex and store in knowledge base
   */
  async processDocument(
    content: string,
    metadata: DocumentMetadata
  ): Promise<ProcessedDocument> {
    try {
      // Create LlamaIndex document
      const document = new Document({
        text: content,
        metadata: {
          title: metadata.title,
          sourceUrl: metadata.sourceUrl,
          sourceType: metadata.sourceType,
          tags: metadata.tags?.join(',') || '',
        },
      });

      // Parse into nodes/chunks
      const nodes = this.nodeParser.getNodesFromDocuments([document]);
      
      // Generate embeddings for each chunk using DeepSeek
      const chunks: DocumentChunk[] = [];
      let startIndex = 0;

      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        const chunkContent = node.getText();
        const embedding = await this.deepseek.generateEmbedding(chunkContent);
        
        const chunk: DocumentChunk = {
          id: `${metadata.sourceUrl}-chunk-${i}`,
          content: chunkContent,
          startIndex,
          endIndex: startIndex + chunkContent.length,
          embedding,
          metadata: {
            nodeId: node.id_,
            chunkIndex: i,
            totalChunks: nodes.length,
          },
        };

        chunks.push(chunk);
        startIndex += chunkContent.length;
      }

      // Generate document-level embedding (average of chunks or full content)
      const documentEmbedding = await this.deepseek.generateEmbedding(
        content.length > 8000 ? content.substring(0, 8000) : content
      );

      const processedDoc: ProcessedDocument = {
        id: metadata.sourceUrl,
        content,
        metadata,
        embedding: documentEmbedding,
        chunks,
      };

      // Store in database
      await this.storeProcessedDocument(processedDoc);

      return processedDoc;
    } catch (error) {
      console.error('Error processing document:', error);
      throw new Error(`Failed to process document: ${error}`);
    }
  }

  /**
   * Search for relevant documents using vector similarity
   */
  async searchDocuments(
    query: string,
    options: {
      limit?: number;
      sourceType?: string;
      threshold?: number;
      useChunks?: boolean;
    } = {}
  ): Promise<SearchResult[]> {
    const { limit = 10, threshold = 0.7, useChunks = true } = options;

    try {
      // Generate query embedding
      const queryEmbedding = await this.deepseek.generateEmbedding(query);

      // Retrieve documents from database
      let dbQuery = db.select().from(schema.knowledgeBase);
      
      if (options.sourceType) {
        dbQuery = dbQuery.where(eq(schema.knowledgeBase.sourceType, options.sourceType));
      }

      const documents = await dbQuery;

      const results: SearchResult[] = [];

      for (const doc of documents) {
        if (!doc.embedding || !doc.chunks) continue;

        if (useChunks && doc.chunks) {
          // Search at chunk level for more precise results
          const chunks = JSON.parse(doc.chunks) as DocumentChunk[];
          
          for (const chunk of chunks) {
            const similarity = this.cosineSimilarity(queryEmbedding, chunk.embedding);
            
            if (similarity >= threshold) {
              results.push({
                document: {
                  id: doc.id,
                  content: doc.content || '',
                  metadata: {
                    title: doc.title,
                    sourceUrl: doc.sourceUrl,
                    sourceType: doc.sourceType as any,
                    tags: doc.tags ? JSON.parse(doc.tags) : [],
                    lastIndexed: doc.lastIndexed,
                  },
                  embedding: JSON.parse(doc.embedding),
                  chunks: chunks,
                },
                chunk,
                score: similarity,
                snippet: this.generateSnippet(chunk.content, query),
              });
            }
          }
        } else {
          // Search at document level
          const similarity = this.cosineSimilarity(
            queryEmbedding, 
            JSON.parse(doc.embedding)
          );
          
          if (similarity >= threshold) {
            results.push({
              document: {
                id: doc.id,
                content: doc.content || '',
                metadata: {
                  title: doc.title,
                  sourceUrl: doc.sourceUrl,
                  sourceType: doc.sourceType as any,
                  tags: doc.tags ? JSON.parse(doc.tags) : [],
                  lastIndexed: doc.lastIndexed,
                },
                embedding: JSON.parse(doc.embedding),
                chunks: doc.chunks ? JSON.parse(doc.chunks) : [],
              },
              chunk: {
                id: `${doc.id}-full`,
                content: doc.content || '',
                startIndex: 0,
                endIndex: doc.content?.length || 0,
                embedding: JSON.parse(doc.embedding),
                metadata: {},
              },
              score: similarity,
              snippet: this.generateSnippet(doc.content || '', query),
            });
          }
        }
      }

      // Sort by score and limit results
      return results
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);

    } catch (error) {
      console.error('Error searching documents:', error);
      throw new Error(`Search failed: ${error}`);
    }
  }

  /**
   * Extract key concepts and entities from document content
   */
  async extractConcepts(content: string): Promise<string[]> {
    try {
      const prompt = `Extract the key concepts, topics, and important terms from the following text. 
Return them as a comma-separated list of keywords and phrases:

${content.substring(0, 2000)}`;

      const response = await this.deepseek.generateCompletion([
        { role: 'user', content: prompt }
      ]);

      const concepts = response.content
        .split(',')
        .map(c => c.trim())
        .filter(c => c.length > 2);

      return concepts;
    } catch (error) {
      console.error('Error extracting concepts:', error);
      return [];
    }
  }

  /**
   * Generate a summary of document content
   */
  async summarizeDocument(content: string, maxLength: number = 200): Promise<string> {
    try {
      const prompt = `Summarize the following content in ${maxLength} characters or less. 
Focus on the main points and key information:

${content.substring(0, 4000)}`;

      const response = await this.deepseek.generateCompletion([
        { role: 'user', content: prompt }
      ]);

      return response.content.substring(0, maxLength);
    } catch (error) {
      console.error('Error summarizing document:', error);
      return content.substring(0, maxLength) + '...';
    }
  }

  /**
   * Store processed document in database
   */
  private async storeProcessedDocument(doc: ProcessedDocument): Promise<void> {
    try {
      // Extract concepts and generate summary
      const concepts = await this.extractConcepts(doc.content);
      const summary = await this.summarizeDocument(doc.content);

      await db.insert(schema.knowledgeBase).values({
        id: doc.id,
        title: doc.metadata.title,
        content: doc.content,
        summary,
        sourceUrl: doc.metadata.sourceUrl,
        sourceType: doc.metadata.sourceType,
        tags: JSON.stringify(doc.metadata.tags || []),
        concepts: JSON.stringify(concepts),
        embedding: JSON.stringify(doc.embedding),
        chunks: JSON.stringify(doc.chunks),
        lastIndexed: doc.metadata.lastIndexed,
      }).onConflictDoUpdate({
        target: schema.knowledgeBase.id,
        set: {
          title: doc.metadata.title,
          content: doc.content,
          summary,
          tags: JSON.stringify(doc.metadata.tags || []),
          concepts: JSON.stringify(concepts),
          embedding: JSON.stringify(doc.embedding),
          chunks: JSON.stringify(doc.chunks),
          lastIndexed: doc.metadata.lastIndexed,
        },
      });
    } catch (error) {
      console.error('Error storing document:', error);
      throw error;
    }
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude === 0 ? 0 : dotProduct / magnitude;
  }

  /**
   * Generate a context snippet around query matches
   */
  private generateSnippet(content: string, query: string, contextLength: number = 150): string {
    const queryWords = query.toLowerCase().split(/\s+/);
    const contentLower = content.toLowerCase();
    
    // Find the best match position
    let bestMatch = -1;
    let maxMatches = 0;
    
    for (let i = 0; i < content.length - contextLength; i++) {
      const snippet = contentLower.substring(i, i + contextLength);
      const matches = queryWords.filter(word => snippet.includes(word)).length;
      
      if (matches > maxMatches) {
        maxMatches = matches;
        bestMatch = i;
      }
    }
    
    if (bestMatch === -1) {
      return content.substring(0, contextLength) + '...';
    }
    
    const start = Math.max(0, bestMatch);
    const end = Math.min(content.length, bestMatch + contextLength);
    
    return (start > 0 ? '...' : '') + 
           content.substring(start, end) + 
           (end < content.length ? '...' : '');
  }
}

// Export singleton instance factory
export function createDocumentProcessor(deepseek: DeepSeekClient): DocumentProcessor {
  return new DocumentProcessor(deepseek);
}