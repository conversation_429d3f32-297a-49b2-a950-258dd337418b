import {
  VectorStoreIndex,
  Document,
  SimpleDirectoryReader,
  StorageContext,
  serviceContextFromDefaults,
  OpenAI,
  HuggingFaceEmbedding,
  ResponseSynthesizer,
  SentenceSplitter,
  TextSplitter,
  OpenAIEmbedding,
  Settings
} from 'llamaindex';
import { db } from '../db';
import { knowledgeBase, KnowledgeBase } from '../db/schema';
import { eq } from 'drizzle-orm';
import { DeepSeekClient } from '../agent/deepseek';
import { ErrorHandler } from '../error/error-handler';
import { ErrorCategory, ErrorSeverity } from '../error/types';

export interface DocumentMetadata {
  title: string;
  sourceType: 'document' | 'confluence' | 'web' | 'manual';
  sourceUrl: string;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
  author?: string;
  version?: string;
}

export interface ProcessedDocument {
  id: string;
  content: string;
  metadata: DocumentMetadata;
  embedding: number[];
  chunks: string[];
  summary?: string;
  concepts?: string[];
}

export interface SearchResult {
  id: string;
  content: string;
  metadata: DocumentMetadata;
  similarity: number;
  chunks: string[];
}

export interface LlamaIndexQueryOptions {
  query: string;
  topK?: number;
  retrievalMode?: 'similarity' | 'hybrid';
  responseMode?: 'compact' | 'tree_summarize' | 'simple_summarize';
  similarityThreshold?: number;
}

export interface LlamaIndexSearchResult {
  content: string;
  metadata: Record<string, any>;
  score: number;
  sourceId: string;
}

export class LlamaIndexService {
  private index: VectorStoreIndex | null = null;
  private deepseek: DeepSeekClient;
  private embeddings: OpenAIEmbedding;
  private textSplitter: TextSplitter;
  private errorHandler: ErrorHandler;
  private isInitialized = false;

  constructor(
    deepseek: DeepSeekClient,
    private apiKey: string = process.env.OPENAI_API_KEY || '',
    private chunkSize: number = 1000,
    private chunkOverlap: number = 200
  ) {
    this.deepseek = deepseek;
    this.errorHandler = ErrorHandler.getInstance();
    
    // Configure global settings
    Settings.llm = null; // We'll handle LLM separately
    Settings.embedModel = new OpenAIEmbedding({
      apiKey: this.apiKey,
      model: 'text-embedding-3-small'
    });
    
    this.embeddings = Settings.embedModel;
    this.textSplitter = new SentenceSplitter({
      chunkSize: this.chunkSize,
      chunkOverlap: this.chunkOverlap
    });
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Initialize empty index
      this.index = await VectorStoreIndex.fromDocuments([]);
      this.isInitialized = true;

      console.log('LlamaIndex service initialized');
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to initialize LlamaIndex service',
        ErrorCategory.SYSTEM,
        ErrorSeverity.HIGH,
        'LLAMAINDEX_INIT_ERROR',
        { service: 'llamaindex' },
        error as Error
      );
      throw serviceError;
    }
  }

  async addDocument(document: ProcessedDocument): Promise<void> {
    if (!this.isInitialized || !this.index) {
      await this.initialize();
    }

    try {
      // Create LlamaIndex document
      const doc = new Document({
        text: document.content,
        metadata: {
          id: document.id,
          title: document.metadata.title,
          sourceType: document.metadata.sourceType,
          sourceUrl: document.metadata.sourceUrl || '',
          tags: JSON.stringify(document.metadata.tags || []),
          createdAt: document.metadata.createdAt.toISOString(),
          updatedAt: document.metadata.updatedAt.toISOString(),
        },
      });

      // Add to index
      await this.index!.insertNodes([doc]);

      // Store in database
      await db.insert(knowledgeBase).values({
        title: document.metadata.title,
        content: document.content,
        sourceType: document.metadata.sourceType,
        sourceUrl: document.metadata.sourceUrl || '',
        tags: document.metadata.tags,
        embedding: document.embedding,
        chunks: document.chunks,
        createdAt: document.metadata.createdAt,
        updatedAt: document.metadata.updatedAt,
        lastIndexed: new Date(),
      });

      console.log(`Added document ${document.id} to index`);
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Error adding document to index',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_ADD_DOCUMENT_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'add_document',
          documentId: document.id
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async search(
    query: string,
    limit: number = 10,
    filters?: Record<string, any>
  ): Promise<SearchResult[]> {
    if (!this.isInitialized || !this.index) {
      await this.initialize();
    }

    try {
      // Search using LlamaIndex
      const queryEngine = this.index!.asQueryEngine({
        topK: limit,
      });
      const response = await queryEngine.query({
        query,
      });

      // Also search in database for additional metadata
      const dbResults = await db
        .select()
        .from(knowledgeBase)
        .where(eq(knowledgeBase.sourceType, filters?.sourceType || 'document'));

      const results: SearchResult[] = [];
      
      // Convert results
      if (response.sourceNodes) {
        for (const node of response.sourceNodes) {
          const metadata = node.node.metadata || {};
          
          results.push({
            id: metadata.id || 'unknown',
            content: node.node.text || '',
            metadata: {
              title: metadata.title || 'Untitled',
              sourceType: metadata.sourceType || 'document',
              sourceUrl: metadata.sourceUrl || '',
              tags: JSON.parse(metadata.tags || '[]'),
              createdAt: new Date(metadata.createdAt || Date.now()),
              updatedAt: new Date(metadata.updatedAt || Date.now()),
            },
            similarity: node.score || 0,
            chunks: []
          });
        }
      }

      return results;
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Error searching documents',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_SEARCH_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'search',
          query: query.substring(0, 100)
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async updateDocument(documentId: string, updates: Partial<ProcessedDocument>): Promise<void> {
    try {
      // Update in database
      await db
        .update(knowledgeBase)
        .set({
          title: updates.metadata?.title,
          content: updates.content,
          sourceType: updates.metadata?.sourceType,
          sourceUrl: updates.metadata?.sourceUrl || '',
          tags: updates.metadata?.tags,
          embedding: updates.embedding,
          chunks: updates.chunks,
          updatedAt: new Date(),
          lastIndexed: new Date(),
        })
        .where(eq(knowledgeBase.id, documentId));

      // For now, we'll rebuild the index when updating
      // In a production system, you'd want to implement incremental updates
      await this.rebuildIndex();

      console.log(`Updated document ${documentId}`);
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Error updating document',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_UPDATE_DOCUMENT_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'update_document',
          documentId
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async deleteDocument(documentId: string): Promise<void> {
    try {
      // Delete from database
      await db
        .delete(knowledgeBase)
        .where(eq(knowledgeBase.id, documentId));

      // Rebuild index
      await this.rebuildIndex();

      console.log(`Deleted document ${documentId}`);
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Error deleting document',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_DELETE_DOCUMENT_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'delete_document',
          documentId
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async generateSummary(text: string): Promise<string> {
    try {
      // Create a temporary document for summarization
      const doc = new Document({
        text: text,
        metadata: { purpose: 'summarization' }
      });

      // Create a temporary index
      const tempIndex = await VectorStoreIndex.fromDocuments([doc]);
      const queryEngine = tempIndex.asQueryEngine();

      const response = await queryEngine.query({
        query: 'Please provide a concise summary of this document.',
      });

      return response.response || '';
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Error generating summary',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_GENERATE_SUMMARY_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'generate_summary',
          textLength: text.length
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async extractKeywords(text: string): Promise<string[]> {
    try {
      // Create a temporary document for keyword extraction
      const doc = new Document({
        text: text,
        metadata: { purpose: 'keyword_extraction' }
      });

      // Create a temporary index
      const tempIndex = await VectorStoreIndex.fromDocuments([doc]);
      const queryEngine = tempIndex.asQueryEngine();

      const response = await queryEngine.query({
        query: 'Extract the main keywords from this document as a comma-separated list.',
      });

      const keywords = response.response
        ?.split(',')
        .map(k => k.trim())
        .filter(k => k.length > 0) || [];

      return keywords;
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Error extracting keywords',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_EXTRACT_KEYWORDS_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'extract_keywords',
          textLength: text.length
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async generateConcepts(text: string): Promise<string[]> {
    try {
      // Create a temporary document for concept extraction
      const doc = new Document({
        text: text,
        metadata: { purpose: 'concept_extraction' }
      });

      // Create a temporary index
      const tempIndex = await VectorStoreIndex.fromDocuments([doc]);
      const queryEngine = tempIndex.asQueryEngine();

      const response = await queryEngine.query({
        query: 'Extract the main concepts and ideas from this document as a comma-separated list.',
      });

      const concepts = response.response
        ?.split(',')
        .map(c => c.trim())
        .filter(c => c.length > 0) || [];

      return concepts;
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Error generating concepts',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_GENERATE_CONCEPTS_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'generate_concepts',
          textLength: text.length
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async rebuildIndex(): Promise<void> {
    try {
      // Get all documents from database
      const documents = await db.select().from(knowledgeBase);

      // Convert to LlamaIndex documents
      const docs = documents.map(doc => new Document({
        text: doc.content,
        metadata: {
          id: doc.id,
          title: doc.title,
          sourceType: doc.sourceType,
          sourceUrl: doc.sourceUrl || '',
          tags: JSON.stringify(doc.tags || []),
          createdAt: doc.createdAt.toISOString(),
          updatedAt: doc.updatedAt.toISOString(),
        },
      }));

      // Rebuild index
      this.index = await VectorStoreIndex.fromDocuments(docs);
      this.isInitialized = true;

      console.log(`Rebuilt index with ${documents.length} documents`);
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Error rebuilding index',
        ErrorCategory.SYSTEM,
        ErrorSeverity.HIGH,
        'LLAMAINDEX_REBUILD_INDEX_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'rebuild_index'
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async getIndexStats(): Promise<{
    totalDocuments: number;
    lastUpdated: Date;
    indexSize: string;
  }> {
    try {
      // Get document count from database
      const documents = await db.select().from(knowledgeBase);
      
      return {
        totalDocuments: documents.length,
        lastUpdated: new Date(),
        indexSize: `${documents.length} documents`
      };
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Error getting index stats',
        ErrorCategory.SYSTEM,
        ErrorSeverity.LOW,
        'LLAMAINDEX_GET_INDEX_STATS_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'get_index_stats'
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async processDocument(
    content: string,
    metadata: Record<string, any> = {},
    sourceId?: string
  ): Promise<ProcessedDocument> {
    try {
      await this.initialize();

      const document = new Document({
        text: content,
        metadata: {
          ...metadata,
          sourceId: sourceId || metadata.id || 'unknown',
          processedAt: new Date().toISOString()
        }
      });

      // Split into chunks
      const chunks = this.textSplitter.splitText(content);
      const processedChunks: ProcessedChunk[] = [];
      
      let currentIndex = 0;
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const startIndex = currentIndex;
        const endIndex = startIndex + chunk.length;
        
        processedChunks.push({
          id: `${sourceId || 'doc'}_chunk_${i}`,
          content: chunk,
          metadata: {
            ...metadata,
            chunkIndex: i,
            sourceId: sourceId || metadata.id || 'unknown',
          },
          startIndex,
          endIndex
        });
        
        currentIndex = endIndex;
      }

      // Generate document embedding
      const embedding = await this.embeddings.getTextEmbedding(content);

      return {
        id: sourceId || metadata.id || 'unknown',
        content,
        metadata,
        chunks: processedChunks,
        embedding
      };
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to process document',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_PROCESS_DOCUMENT_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'process_document',
          sourceId: sourceId || 'unknown'
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async indexDocument(document: ProcessedDocument): Promise<void> {
    try {
      await this.initialize();
      
      if (!this.index) {
        throw new Error('Index not initialized');
      }

      const llamaDoc = new Document({
        text: document.content,
        metadata: {
          ...document.metadata,
          documentId: document.id,
          indexedAt: new Date().toISOString()
        }
      });

      // Add to index
      await this.index.insertNodes([llamaDoc]);
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to index document',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_INDEX_DOCUMENT_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'index_document',
          documentId: document.id
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async query(
    query: string,
    options: LlamaIndexQueryOptions = {}
  ): Promise<LlamaIndexSearchResult[]> {
    try {
      await this.initialize();
      
      if (!this.index) {
        throw new Error('Index not initialized');
      }

      const queryEngine = this.index.asQueryEngine({
        topK: options.topK || 10,
        responseSynthesizer: new ResponseSynthesizer({
          responseMode: options.responseMode || 'compact'
        })
      });

      const result = await queryEngine.query({
        query
      });

      // Convert to search results
      const searchResults: LlamaIndexSearchResult[] = [];
      
      if (result.sourceNodes) {
        for (const node of result.sourceNodes) {
          searchResults.push({
            content: node.node.text || '',
            metadata: node.node.metadata || {},
            score: node.score || 0,
            sourceId: node.node.metadata?.sourceId || 'unknown'
          });
        }
      }

      return searchResults;
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to query documents',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_QUERY_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'query',
          query: query.substring(0, 100)
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async buildIndexFromKnowledgeBase(documents: KnowledgeBase[]): Promise<void> {
    try {
      const llamaDocs = documents.map(doc => new Document({
        text: doc.content,
        metadata: {
          id: doc.id,
          sourceType: doc.sourceType,
          sourceUrl: doc.sourceUrl,
          title: doc.title,
          tags: JSON.stringify(doc.tags || []),
          concepts: JSON.stringify(doc.concepts || []),
          lastIndexed: doc.lastIndexed.toISOString(),
          createdAt: doc.createdAt.toISOString(),
          updatedAt: doc.updatedAt.toISOString(),
        }
      }));

      // Build index from documents
      this.index = await VectorStoreIndex.fromDocuments(llamaDocs);
      this.isInitialized = true;
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to build index from knowledge base',
        ErrorCategory.SYSTEM,
        ErrorSeverity.HIGH,
        'LLAMAINDEX_BUILD_INDEX_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'build_index',
          documentCount: documents.length
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async generateSummary(content: string, maxLength: number = 200): Promise<string> {
    try {
      await this.initialize();
      
      if (!this.index) {
        throw new Error('Index not initialized');
      }

      const document = new Document({
        text: content,
        metadata: { purpose: 'summary_generation' }
      });

      const summaryIndex = await VectorStoreIndex.fromDocuments([document]);
      const queryEngine = summaryIndex.asQueryEngine({
        responseSynthesizer: new ResponseSynthesizer({
          responseMode: 'simple_summarize'
        })
      });

      const result = await queryEngine.query({
        query: `Summarize this content in ${maxLength} characters or less:`
      });

      return result.response || '';
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to generate summary',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_SUMMARY_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'generate_summary',
          contentLength: content.length
        },
        error as Error
      );
      throw serviceError;
    }
  }

  async extractConcepts(content: string, maxConcepts: number = 10): Promise<string[]> {
    try {
      await this.initialize();
      
      if (!this.index) {
        throw new Error('Index not initialized');
      }

      const document = new Document({
        text: content,
        metadata: { purpose: 'concept_extraction' }
      });

      const conceptIndex = await VectorStoreIndex.fromDocuments([document]);
      const queryEngine = conceptIndex.asQueryEngine();

      const result = await queryEngine.query({
        query: `Extract the top ${maxConcepts} key concepts from this content as a comma-separated list:`
      });

      const concepts = result.response
        ?.split(',')
        .map(c => c.trim())
        .filter(c => c.length > 0)
        .slice(0, maxConcepts) || [];

      return concepts;
    } catch (error) {
      const serviceError = this.errorHandler.createError(
        'Failed to extract concepts',
        ErrorCategory.SYSTEM,
        ErrorSeverity.MEDIUM,
        'LLAMAINDEX_CONCEPT_EXTRACTION_ERROR',
        { 
          service: 'llamaindex', 
          operation: 'extract_concepts',
          contentLength: content.length
        },
        error as Error
      );
      throw serviceError;
    }
  }
}

// Export singleton factory
export function createLlamaIndexService(
  apiKey?: string,
  chunkSize?: number,
  chunkOverlap?: number
): LlamaIndexService {
  return new LlamaIndexService(apiKey, chunkSize, chunkOverlap);
}