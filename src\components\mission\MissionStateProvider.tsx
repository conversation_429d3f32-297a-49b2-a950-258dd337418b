import React, { createContext, useContext, ReactNode } from 'react';
import { useMissionState } from '@/hooks/useMissionState';
import { useChatOperations } from '@/hooks/useChatOperations';

/**
 * Context for mission state and operations
 */
interface MissionContextType {
  // Mission state
  missions: ReturnType<typeof useMissionState>['missions'];
  selectedMissionId: ReturnType<typeof useMissionState>['selectedMissionId'];
  currentView: ReturnType<typeof useMissionState>['currentView'];
  missionDetails: ReturnType<typeof useMissionState>['missionDetails'];
  loadingMissionDetails: ReturnType<typeof useMissionState>['loadingMissionDetails'];
  wsConnected: ReturnType<typeof useMissionState>['wsConnected'];
  connectionError: ReturnType<typeof useMissionState>['connectionError'];

  // Chat state
  messages: ReturnType<typeof useMissionState>['messages'];
  input: ReturnType<typeof useMissionState>['input'];
  isLoading: ReturnType<typeof useMissionState>['isLoading'];

  // Search and editing state
  searchQuery: ReturnType<typeof useMissionState>['searchQuery'];
  editingMissionId: ReturnType<typeof useMissionState>['editingMissionId'];
  editingValue: ReturnType<typeof useMissionState>['editingValue'];

  // Dialog state
  deleteDialogOpen: ReturnType<typeof useMissionState>['deleteDialogOpen'];
  missionToDelete: ReturnType<typeof useMissionState>['missionToDelete'];

  // Mission operations
  startNewMission: ReturnType<typeof useMissionState>['startNewMission'];
  selectMission: ReturnType<typeof useMissionState>['selectMission'];
  selectKnowledgeBase: ReturnType<typeof useMissionState>['selectKnowledgeBase'];
  renameMission: ReturnType<typeof useMissionState>['renameMission'];
  openDeleteDialog: ReturnType<typeof useMissionState>['openDeleteDialog'];
  confirmDeleteMission: ReturnType<typeof useMissionState>['confirmDeleteMission'];
  cancelDeleteMission: ReturnType<typeof useMissionState>['cancelDeleteMission'];
  clearSearch: ReturnType<typeof useMissionState>['clearSearch'];
  startEditingMission: ReturnType<typeof useMissionState>['startEditingMission'];
  saveEditingMission: ReturnType<typeof useMissionState>['saveEditingMission'];
  cancelEditingMission: ReturnType<typeof useMissionState>['cancelEditingMission'];
  getRealtimeMissionStatus: ReturnType<typeof useMissionState>['getRealtimeMissionStatus'];
  getRealtimeStatus: ReturnType<typeof useMissionState>['getRealtimeStatus'];

  // Chat operations
  handleSubmit: (e: React.FormEvent) => void;
  handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
  handleMissionAction: ReturnType<typeof useChatOperations>['handleMissionAction'];
  setInput: ReturnType<typeof useMissionState>['setInput'];
  setMessages: ReturnType<typeof useMissionState>['setMessages'];
  setSearchQuery: ReturnType<typeof useMissionState>['setSearchQuery'];
  setEditingValue: ReturnType<typeof useMissionState>['setEditingValue'];
}

const MissionContext = createContext<MissionContextType | undefined>(undefined);

/**
 * Provider component that manages all mission-related state and operations
 */
export function MissionStateProvider({ children }: { children: ReactNode }) {
  const missionState = useMissionState();
  const chatOps = useChatOperations();

  // Create wrapped handlers that use the current state
  const handleSubmit = (e: React.FormEvent) => {
    chatOps.handleSubmit(
      e,
      missionState.input,
      missionState.selectedMissionId,
      missionState.messages,
      missionState.setMessages,
      missionState.setInput
    );
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    chatOps.handleKeyDown(e, () => handleSubmit(e as any));
  };

  const contextValue: MissionContextType = {
    // Mission state
    missions: missionState.missions,
    selectedMissionId: missionState.selectedMissionId,
    currentView: missionState.currentView,
    missionDetails: missionState.missionDetails,
    loadingMissionDetails: missionState.loadingMissionDetails,
    wsConnected: missionState.wsConnected,
    connectionError: missionState.connectionError,

    // Chat state
    messages: missionState.messages,
    input: missionState.input,
    isLoading: missionState.isLoading,

    // Search and editing state
    searchQuery: missionState.searchQuery,
    editingMissionId: missionState.editingMissionId,
    editingValue: missionState.editingValue,

    // Dialog state
    deleteDialogOpen: missionState.deleteDialogOpen,
    missionToDelete: missionState.missionToDelete,

    // Mission operations
    startNewMission: missionState.startNewMission,
    selectMission: missionState.selectMission,
    selectKnowledgeBase: missionState.selectKnowledgeBase,
    renameMission: missionState.renameMission,
    openDeleteDialog: missionState.openDeleteDialog,
    confirmDeleteMission: missionState.confirmDeleteMission,
    cancelDeleteMission: missionState.cancelDeleteMission,
    clearSearch: missionState.clearSearch,
    startEditingMission: missionState.startEditingMission,
    saveEditingMission: missionState.saveEditingMission,
    cancelEditingMission: missionState.cancelEditingMission,
    getRealtimeMissionStatus: missionState.getRealtimeMissionStatus,
    getRealtimeStatus: missionState.getRealtimeStatus,

    // Chat operations
    handleSubmit,
    handleKeyDown,
    handleMissionAction: chatOps.handleMissionAction,
    setInput: missionState.setInput,
    setMessages: missionState.setMessages,
    setSearchQuery: missionState.setSearchQuery,
    setEditingValue: missionState.setEditingValue,
  };

  return (
    <MissionContext.Provider value={contextValue}>
      {children}
    </MissionContext.Provider>
  );
}

/**
 * Hook to access mission context
 */
export function useMissionContext() {
  const context = useContext(MissionContext);
  if (context === undefined) {
    throw new Error('useMissionContext must be used within a MissionStateProvider');
  }
  return context;
}

/**
 * HOC to wrap components with mission context
 */
export function withMissionContext<P extends object>(
  Component: React.ComponentType<P>
) {
  return function WrappedComponent(props: P) {
    return (
      <MissionStateProvider>
        <Component {...props} />
      </MissionStateProvider>
    );
  };
}
