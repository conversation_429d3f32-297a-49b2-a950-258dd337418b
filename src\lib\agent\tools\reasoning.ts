import { BaseTool } from './base';
import { ToolResult, ExecutionContext } from '../types';
import { DeepSeekClient } from '../deepseek';

/**
 * Knowledge-Based Reasoning Tool
 * This tool ONLY generates responses based on previously retrieved knowledge base data
 */
export class KnowledgeBasedReasoningTool extends BaseTool {
  name = 'knowledge_based_reasoning';
  description = 'Generate responses and analysis based ONLY on previously retrieved knowledge base data. Cannot be used without prior knowledge retrieval.';
  parameters = {
    query: { type: 'string', required: true, description: 'The question or analysis request' },
    knowledgeContext: { type: 'array', required: true, description: 'Previously retrieved knowledge base data' },
    analysisType: { type: 'string', required: false, description: 'Type of analysis: summary, comparison, trend_analysis, recommendation' },
  };

  constructor(private deepseek: DeepSeekClient) {
    super();
  }

  async execute(params: Record<string, any>, context?: ExecutionContext): Promise<ToolResult> {
    try {
      this.validateParams(params, ['query', 'knowledgeContext']);
      
      const { query, knowledgeContext, analysisType = 'summary' } = params;
      
      // CRITICAL VALIDATION: Ensure we have knowledge context
      if (!knowledgeContext || !Array.isArray(knowledgeContext) || knowledgeContext.length === 0) {
        const errorMessage = 'Cannot perform reasoning without knowledge base data. Knowledge-base-first principle violated.';
        context?.logger.error(errorMessage);
        return this.createResult(false, null, errorMessage);
      }

      context?.logger.info(`Performing knowledge-based reasoning: ${query}`);
      context?.logger.info(`Using ${knowledgeContext.length} knowledge sources`);

      let response: string;

      switch (analysisType) {
        case 'incident_analysis':
          response = await this.deepseek.analyzeIncidents(query, knowledgeContext);
          break;
        case 'summary':
        case 'comparison':
        case 'trend_analysis':
        case 'recommendation':
        default:
          response = await this.deepseek.generateKnowledgeBasedResponse(
            query, 
            knowledgeContext,
            `Analysis type: ${analysisType}`
          );
          break;
      }

      // Validate that response doesn't indicate lack of knowledge
      if (this.indicatesInsufficientKnowledge(response)) {
        context?.logger.warn('Generated response indicates insufficient knowledge base data');
        return this.createResult(true, {
          query,
          response,
          hasInsufficientData: true,
          knowledgeSourceCount: knowledgeContext.length,
          analysisType
        }, undefined, {
          knowledgeBasedResponse: true,
          sourceCount: knowledgeContext.length
        });
      }

      context?.logger.info('Knowledge-based reasoning completed successfully');

      return this.createResult(true, {
        query,
        response,
        hasInsufficientData: false,
        knowledgeSourceCount: knowledgeContext.length,
        analysisType,
        sources: knowledgeContext.map((item: any, index: number) => ({
          id: index + 1,
          title: item.title || 'Unknown',
          score: item.score || 0,
          source: item.source || 'knowledge_base'
        }))
      }, undefined, {
        knowledgeBasedResponse: true,
        sourceCount: knowledgeContext.length,
        analysisType
      });

    } catch (error) {
      context?.logger.error(`Knowledge-based reasoning failed: ${error}`, undefined, error as Error);
      return this.createResult(false, null, (error as Error).message);
    }
  }

  /**
   * Check if response indicates insufficient knowledge
   */
  private indicatesInsufficientKnowledge(response: string): boolean {
    const insufficientKnowledgeIndicators = [
      "don't have enough information",
      "insufficient information",
      "no relevant data",
      "cannot answer",
      "not enough data",
      "knowledge base doesn't contain"
    ];

    const lowerResponse = response.toLowerCase();
    return insufficientKnowledgeIndicators.some(indicator => 
      lowerResponse.includes(indicator)
    );
  }
}

/**
 * Response Generation Tool
 * Generates final user responses based ONLY on knowledge-based reasoning results
 */
export class ResponseGenerationTool extends BaseTool {
  name = 'response_generation';
  description = 'Generate final user responses based on knowledge-based reasoning results. Must have prior knowledge retrieval and reasoning.';
  parameters = {
    originalQuery: { type: 'string', required: true, description: 'The original user query' },
    reasoningResult: { type: 'object', required: true, description: 'Result from knowledge-based reasoning' },
    responseFormat: { type: 'string', required: false, description: 'Format: detailed, summary, bullet_points, report' },
  };

  constructor(private deepseek: DeepSeekClient) {
    super();
  }

  async execute(params: Record<string, any>, context?: ExecutionContext): Promise<ToolResult> {
    try {
      this.validateParams(params, ['originalQuery', 'reasoningResult']);
      
      const { originalQuery, reasoningResult, responseFormat = 'detailed' } = params;
      
      // CRITICAL VALIDATION: Ensure we have reasoning results based on knowledge
      if (!reasoningResult || !reasoningResult.response) {
        const errorMessage = 'Cannot generate response without prior knowledge-based reasoning results.';
        context?.logger.error(errorMessage);
        return this.createResult(false, null, errorMessage);
      }

      context?.logger.info(`Generating final response for: ${originalQuery}`);

      // Check if reasoning indicated insufficient knowledge
      if (reasoningResult.hasInsufficientData) {
        const response = this.generateInsufficientDataResponse(originalQuery, reasoningResult);
        return this.createResult(true, {
          originalQuery,
          response,
          hasInsufficientData: true,
          knowledgeSourceCount: reasoningResult.knowledgeSourceCount || 0,
          responseFormat
        });
      }

      // Generate formatted response
      const response = await this.formatResponse(
        originalQuery,
        reasoningResult.response,
        responseFormat,
        reasoningResult.sources || []
      );

      context?.logger.info('Final response generated successfully');

      return this.createResult(true, {
        originalQuery,
        response,
        hasInsufficientData: false,
        knowledgeSourceCount: reasoningResult.knowledgeSourceCount || 0,
        responseFormat,
        sources: reasoningResult.sources || []
      }, undefined, {
        finalResponse: true,
        basedOnKnowledge: true,
        sourceCount: reasoningResult.knowledgeSourceCount || 0
      });

    } catch (error) {
      context?.logger.error(`Response generation failed: ${error}`, undefined, error as Error);
      return this.createResult(false, null, (error as Error).message);
    }
  }

  /**
   * Generate response for insufficient data scenarios
   */
  private generateInsufficientDataResponse(query: string, reasoningResult: any): string {
    return `I apologize, but I don't have enough information in the knowledge base to fully answer your question: "${query}".

${reasoningResult.response}

To get a complete answer, you may need to:
1. Check if the relevant documents are indexed in the knowledge base
2. Try rephrasing your question with different keywords
3. Ensure the information you're looking for has been added to the system

Knowledge sources checked: ${reasoningResult.knowledgeSourceCount || 0}`;
  }

  /**
   * Format response according to specified format
   */
  private async formatResponse(
    query: string,
    reasoningResponse: string,
    format: string,
    sources: any[]
  ): Promise<string> {
    const sourcesList = sources.length > 0 
      ? `\n\nSources:\n${sources.map(s => `- ${s.title} (relevance: ${Math.round((s.score || 0) * 100)}%)`).join('\n')}`
      : '';

    switch (format) {
      case 'summary':
        return `${reasoningResponse}${sourcesList}`;
      
      case 'bullet_points':
        // Convert response to bullet points
        const bullets = reasoningResponse.split(/[.!?]+/)
          .filter(sentence => sentence.trim().length > 10)
          .map(sentence => `• ${sentence.trim()}`)
          .join('\n');
        return `${bullets}${sourcesList}`;
      
      case 'report':
        return `# Analysis Report

## Query
${query}

## Findings
${reasoningResponse}

## Summary
Based on the analysis of ${sources.length} knowledge base sources, the key findings have been presented above.
${sourcesList}`;
      
      case 'detailed':
      default:
        return `${reasoningResponse}${sourcesList}`;
    }
  }
}
