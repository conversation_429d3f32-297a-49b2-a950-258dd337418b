import { EventEmitter } from 'events';
import { useState, useEffect, useCallback } from 'react';

export interface SSEMessage {
  type: string;
  payload: any;
  timestamp: string;
  missionId?: string;
  clientId?: string;
}

export interface ConnectionStatus {
  connected: boolean;
  connecting: boolean;
  error?: string;
  missionId?: string;
  clientId?: string;
  lastPing?: string;
}

export interface MissionUpdate {
  missionId: string;
  status: string;
  progress?: any;
  currentTask?: string;
  message?: string;
}

export interface TaskUpdate {
  missionId: string;
  taskId: string;
  status: string;
  progress?: number;
  result?: any;
  error?: string;
}

export interface LogUpdate {
  missionId: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: string;
  context?: any;
}

export interface ReflectionUpdate {
  missionId: string;
  reflection: string;
  timestamp: string;
  context?: any;
}

/**
 * Server-Sent Events client for real-time mission updates
 * Provides similar functionality to WebSocket but unidirectional
 */
export class SSEClient extends EventEmitter {
  private eventSource: EventSource | null = null;
  private url: string;
  private missionId: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private connectionTimeout: NodeJS.Timeout | null = null;
  private status: ConnectionStatus = { connected: false, connecting: false };
  private heartbeatTimeout: NodeJS.Timeout | null = null;
  private readonly heartbeatInterval = 35000; // 35 seconds (server sends ping every 30s)

  constructor(missionId: string, baseUrl?: string) {
    super();
    this.missionId = missionId;
    
    // Build SSE URL - handle both development and production
    if (baseUrl) {
      this.url = `${baseUrl}/api/missions/${missionId}/stream`;
    } else if (typeof window !== 'undefined') {
      const origin = window.location.origin;
      this.url = `${origin}/api/missions/${missionId}/stream`;
    } else {
      // Server-side fallback
      this.url = `http://localhost:3000/api/missions/${missionId}/stream`;
    }
  }

  /**
   * Connect to the SSE endpoint
   */
  public connect(): Promise<void> {
    if (this.status.connected || this.status.connecting) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      this.status.connecting = true;
      this.status.missionId = this.missionId;
      this.status.error = undefined;
      this.emit('status', this.status);

      try {
        // Check if SSE is supported
        if (typeof EventSource === 'undefined') {
          throw new Error('Server-Sent Events not supported in this environment');
        }

        this.eventSource = new EventSource(this.url);

        // Connection timeout
        this.connectionTimeout = setTimeout(() => {
          if (this.eventSource && this.eventSource.readyState === EventSource.CONNECTING) {
            this.eventSource.close();
            this.cleanup();
            reject(new Error('SSE connection timeout'));
          }
        }, 10000);

        this.eventSource.onopen = () => {
          if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = null;
          }

          this.status = { 
            connected: true, 
            connecting: false, 
            missionId: this.missionId 
          };
          this.reconnectAttempts = 0;
          this.reconnectDelay = 1000;

          this.setupHeartbeat();
          this.emit('connected');
          this.emit('status', this.status);
          resolve();
        };

        this.eventSource.onmessage = (event) => {
          try {
            const message: SSEMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse SSE message:', error, event.data);
            this.emit('error', { message: 'Invalid SSE message format', data: event.data });
          }
        };

        this.eventSource.onerror = (error) => {
          console.warn('SSE connection error:', error);
          
          if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = null;
          }

          if (this.status.connecting) {
            this.status = { 
              connected: false, 
              connecting: false, 
              error: 'SSE connection failed',
              missionId: this.missionId
            };
            this.emit('error', { message: 'SSE connection failed', originalError: error });
            this.emit('status', this.status);
            reject(new Error('SSE connection failed'));
          } else {
            this.handleDisconnection();
          }
        };

      } catch (error) {
        this.status = { 
          connected: false, 
          connecting: false, 
          error: (error as Error).message,
          missionId: this.missionId
        };
        this.emit('error', { message: (error as Error).message, originalError: error });
        this.emit('status', this.status);
        reject(error);
      }
    });
  }

  /**
   * Disconnect from the SSE endpoint
   */
  public disconnect(): void {
    this.reconnectAttempts = this.maxReconnectAttempts; // Prevent reconnection
    
    if (this.eventSource) {
      this.eventSource.close();
    }
    this.cleanup();
  }

  /**
   * Get current connection status
   */
  public getStatus(): ConnectionStatus {
    return { ...this.status };
  }

  /**
   * Get mission ID this client is connected to
   */
  public getMissionId(): string {
    return this.missionId;
  }

  /**
   * Check if connected
   */
  public isConnected(): boolean {
    return this.status.connected;
  }

  /**
   * Handle incoming messages from the server
   */
  private handleMessage(message: SSEMessage): void {
    // Reset heartbeat timeout on any message
    this.resetHeartbeat();

    switch (message.type) {
      case 'connection.established':
        this.status.clientId = message.payload?.clientId;
        this.emit('status', this.status);
        break;

      case 'ping':
        this.status.lastPing = message.timestamp;
        this.emit('status', this.status);
        // Send pong event (even though server doesn't need it for SSE)
        this.emit('pong', { timestamp: message.timestamp });
        break;

      case 'mission.update':
        this.emit('mission.update', message.payload as MissionUpdate);
        break;

      case 'task.update':
        this.emit('task.update', message.payload as TaskUpdate);
        break;

      case 'log.update':
        this.emit('log.update', message.payload as LogUpdate);
        break;

      case 'reflection.update':
        this.emit('reflection.update', message.payload as ReflectionUpdate);
        break;

      case 'error':
        this.emit('server.error', message.payload);
        break;

      default:
        this.emit('message', message);
    }
  }

  /**
   * Setup heartbeat monitoring
   */
  private setupHeartbeat(): void {
    this.resetHeartbeat();
  }

  /**
   * Reset heartbeat timeout
   */
  private resetHeartbeat(): void {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
    }

    this.heartbeatTimeout = setTimeout(() => {
      console.warn('SSE heartbeat timeout - connection may be stale');
      this.handleDisconnection();
    }, this.heartbeatInterval);
  }

  /**
   * Handle disconnection and attempt reconnection
   */
  private handleDisconnection(): void {
    this.cleanup();
    this.status = { 
      connected: false, 
      connecting: false, 
      missionId: this.missionId 
    };
    this.emit('disconnected');
    this.emit('status', this.status);

    // Attempt reconnection if not manually disconnected
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);
      
      console.log(`Attempting to reconnect SSE in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect().catch(error => {
          console.error('SSE reconnection failed:', error);
        });
      }, delay);
    } else {
      console.error('Max SSE reconnection attempts reached');
      this.status.error = 'Max reconnection attempts reached';
      this.emit('status', this.status);
      this.emit('error', { message: 'Max reconnection attempts reached' });
    }
  }

  /**
   * Clean up resources
   */
  private cleanup(): void {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }

    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }

    this.status = { 
      connected: false, 
      connecting: false, 
      missionId: this.missionId 
    };
    this.eventSource = null;
  }
}

// React Hook for SSE connection
export function useSSE(missionId: string, baseUrl?: string) {
  const [client] = useState(() => new SSEClient(missionId, baseUrl));
  const [status, setStatus] = useState<ConnectionStatus>(client.getStatus());
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleStatus = (newStatus: ConnectionStatus) => {
      setStatus(newStatus);
      setIsConnected(newStatus.connected);
      setError(newStatus.error || null);
    };

    const handleError = (errorData: any) => {
      setError(errorData.message || 'Connection error');
    };

    client.on('status', handleStatus);
    client.on('error', handleError);
    
    // Auto-connect when the hook is used
    if (missionId) {
      client.connect().catch(error => {
        console.error('Failed to connect SSE:', error);
        setError(error.message);
      });
    }

    return () => {
      client.off('status', handleStatus);
      client.off('error', handleError);
      client.disconnect();
    };
  }, [client, missionId]);

  const reconnect = useCallback(() => {
    client.connect().catch(error => {
      console.error('Failed to reconnect SSE:', error);
      setError(error.message);
    });
  }, [client]);

  return {
    client,
    status,
    isConnected,
    error,
    connect: reconnect,
    disconnect: () => client.disconnect(),
    getMissionId: () => client.getMissionId(),
  };
}

// Export singleton clients for direct use
const clientInstances: Map<string, SSEClient> = new Map();

export function getSSEClient(missionId: string, baseUrl?: string): SSEClient {
  const key = `${missionId}-${baseUrl || 'default'}`;
  if (!clientInstances.has(key)) {
    clientInstances.set(key, new SSEClient(missionId, baseUrl));
  }
  return clientInstances.get(key)!;
}

export function clearSSEClient(missionId: string, baseUrl?: string): void {
  const key = `${missionId}-${baseUrl || 'default'}`;
  const client = clientInstances.get(key);
  if (client) {
    client.disconnect();
    clientInstances.delete(key);
  }
} 