import { jest } from '@jest/globals';
import { 
  TestUtils, 
  AgentTestUtils, 
  DbTestUtils, 
  mockMission, 
  mockPlan, 
  mockTask,
  MockWebSocket 
} from '@/test/utils';

// Mock WebSocket
global.WebSocket = MockWebSocket as any;

describe('Mission Lifecycle Integration', () => {
  let agent: any;
  let db: any;
  let lifecycleManager: any;
  let mockWs: MockWebSocket;

  beforeEach(async () => {
    // Reset database
    db = DbTestUtils.createMockDb();
    await DbTestUtils.resetDatabase(db);

    // Setup agent
    agent = AgentTestUtils.createMockDeepSeekClient();
    lifecycleManager = AgentTestUtils.createMockLifecycleManager();

    // Setup WebSocket
    mockWs = new MockWebSocket('ws://localhost:3001');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Complete Mission Flow', () => {
    it('executes a full mission lifecycle successfully', async () => {
      // 1. Create mission
      const missionRequest = {
        title: 'Integration Test Mission',
        description: 'Test the complete mission flow',
        priority: 'high' as const
      };

      const createdMission = await lifecycleManager.createMission(missionRequest);
      expect(createdMission).toHaveProperty('id');
      expect(createdMission.status).toBe('pending');

      // 2. Generate plan
      agent.generatePlan.mockResolvedValueOnce(JSON.stringify({
        title: 'Test Plan',
        description: 'Generated test plan',
        tasks: [
          {
            title: 'Task 1',
            description: 'First task',
            toolName: 'test-tool',
            dependencies: []
          },
          {
            title: 'Task 2', 
            description: 'Second task',
            toolName: 'test-tool',
            dependencies: ['task-1']
          }
        ]
      }));

      const plan = await agent.generatePlan({
        missionId: createdMission.id,
        requirements: ['Complete integration test']
      });

      expect(plan).toHaveProperty('tasks');
      expect(plan.tasks).toHaveLength(2);

      // 3. Execute mission
      lifecycleManager.getMissionState.mockReturnValue({
        mission: { ...createdMission, status: 'running' },
        plan,
        tasks: plan.tasks,
        progress: 0,
        isRunning: true,
        isPaused: false,
        cancellationRequested: false
      });

      // Simulate WebSocket updates
      mockWs.simulateMessage({
        type: 'mission_status_update',
        missionId: createdMission.id,
        status: 'running',
        progress: 0
      });

      // Execute first task
      mockWs.simulateMessage({
        type: 'task_started',
        missionId: createdMission.id,
        taskId: 'task-1',
        taskName: 'Task 1'
      });

      // Task completion
      mockWs.simulateMessage({
        type: 'task_completed',
        missionId: createdMission.id,
        taskId: 'task-1',
        result: { success: true, data: 'Task 1 result' }
      });

      // Execute second task
      mockWs.simulateMessage({
        type: 'task_started',
        missionId: createdMission.id,
        taskId: 'task-2',
        taskName: 'Task 2'
      });

      // Complete mission
      mockWs.simulateMessage({
        type: 'mission_completed',
        missionId: createdMission.id,
        status: 'completed',
        progress: 100
      });

      // 4. Verify final state
      lifecycleManager.getMissionState.mockReturnValue({
        mission: { ...createdMission, status: 'completed' },
        plan,
        tasks: plan.tasks.map((t: any) => ({ ...t, status: 'completed' })),
        progress: 100,
        isRunning: false,
        isPaused: false,
        cancellationRequested: false
      });

      const finalState = lifecycleManager.getMissionState(createdMission.id);
      expect(finalState.mission.status).toBe('completed');
      expect(finalState.progress).toBe(100);
    });

    it('handles mission pause and resume', async () => {
      const missionId = 'test-mission-1';

      // Start mission
      lifecycleManager.getMissionState.mockReturnValue({
        mission: { ...mockMission, status: 'running' },
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0.5,
        isRunning: true,
        isPaused: false,
        cancellationRequested: false
      });

      // Pause mission
      await lifecycleManager.requestPause(missionId);
      
      mockWs.simulateMessage({
        type: 'mission_paused',
        missionId,
        status: 'paused'
      });

      lifecycleManager.getMissionState.mockReturnValue({
        mission: { ...mockMission, status: 'paused' },
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0.5,
        isRunning: false,
        isPaused: true,
        cancellationRequested: false
      });

      let state = lifecycleManager.getMissionState(missionId);
      expect(state.isPaused).toBe(true);

      // Resume mission
      await lifecycleManager.requestResume(missionId);
      
      mockWs.simulateMessage({
        type: 'mission_resumed',
        missionId,
        status: 'running'
      });

      lifecycleManager.getMissionState.mockReturnValue({
        mission: { ...mockMission, status: 'running' },
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0.5,
        isRunning: true,
        isPaused: false,
        cancellationRequested: false
      });

      state = lifecycleManager.getMissionState(missionId);
      expect(state.isRunning).toBe(true);
      expect(state.isPaused).toBe(false);
    });

    it('handles mission cancellation', async () => {
      const missionId = 'test-mission-1';

      // Start mission
      lifecycleManager.getMissionState.mockReturnValue({
        mission: { ...mockMission, status: 'running' },
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0.3,
        isRunning: true,
        isPaused: false,
        cancellationRequested: false
      });

      // Cancel mission
      await lifecycleManager.requestCancellation(missionId);
      
      mockWs.simulateMessage({
        type: 'mission_cancelled',
        missionId,
        status: 'cancelled'
      });

      lifecycleManager.getMissionState.mockReturnValue({
        mission: { ...mockMission, status: 'cancelled' },
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0.3,
        isRunning: false,
        isPaused: false,
        cancellationRequested: true
      });

      const state = lifecycleManager.getMissionState(missionId);
      expect(state.mission.status).toBe('cancelled');
      expect(state.cancellationRequested).toBe(true);
    });

    it('handles task failures gracefully', async () => {
      const missionId = 'test-mission-1';

      // Start mission
      lifecycleManager.getMissionState.mockReturnValue({
        mission: { ...mockMission, status: 'running' },
        plan: mockPlan,
        tasks: [{ ...mockTask, status: 'running' }],
        progress: 0.5,
        isRunning: true,
        isPaused: false,
        cancellationRequested: false
      });

      // Simulate task failure
      mockWs.simulateMessage({
        type: 'task_failed',
        missionId,
        taskId: mockTask.id,
        error: 'Task execution failed',
        details: 'Network timeout'
      });

      // Should trigger reflection and retry logic
      agent.reflect.mockResolvedValueOnce(JSON.stringify({
        insights: ['Task failed due to network issues'],
        recommendations: ['Retry with exponential backoff'],
        confidence: 0.8
      }));

      const reflection = await agent.reflect({
        missionId,
        context: 'Task failure analysis'
      });

      expect(reflection.recommendations).toContain('Retry with exponential backoff');

      // Simulate retry
      mockWs.simulateMessage({
        type: 'task_retry',
        missionId,
        taskId: mockTask.id,
        attempt: 2
      });

      // Successful retry
      mockWs.simulateMessage({
        type: 'task_completed',
        missionId,
        taskId: mockTask.id,
        result: { success: true, data: 'Task completed on retry' }
      });

      lifecycleManager.getMissionState.mockReturnValue({
        mission: { ...mockMission, status: 'running' },
        plan: mockPlan,
        tasks: [{ ...mockTask, status: 'completed' }],
        progress: 1.0,
        isRunning: true,
        isPaused: false,
        cancellationRequested: false
      });

      const finalState = lifecycleManager.getMissionState(missionId);
      expect(finalState.tasks[0].status).toBe('completed');
    });
  });

  describe('Real-time Updates', () => {
    it('broadcasts mission progress updates via WebSocket', async () => {
      const missionId = 'test-mission-1';
      const messages: any[] = [];

      // Capture WebSocket messages
      mockWs.onmessage = (event) => {
        messages.push(JSON.parse(event.data));
      };

      // Simulate progress updates
      for (let progress = 0; progress <= 100; progress += 25) {
        mockWs.simulateMessage({
          type: 'mission_progress',
          missionId,
          progress: progress / 100,
          currentTask: 'Simulated task',
          timeRemaining: Math.max(0, 300 - (progress * 3))
        });
      }

      // Verify messages were received
      expect(messages).toHaveLength(5);
      expect(messages[0].progress).toBe(0);
      expect(messages[4].progress).toBe(1);
    });

    it('handles WebSocket connection errors', async () => {
      let connectionError = false;
      
      mockWs.onerror = () => {
        connectionError = true;
      };

      mockWs.simulateError();
      
      expect(connectionError).toBe(true);

      // Should reconnect automatically
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Verify reconnection logic (in real implementation)
      expect(mockWs.readyState).toBe(WebSocket.CLOSED);
    });
  });

  describe('Error Recovery', () => {
    it('recovers from database connection errors', async () => {
      // Simulate database connection failure
      db.all.mockRejectedValueOnce(new Error('Database connection lost'));

      // Should retry with exponential backoff
      db.all.mockResolvedValueOnce([mockMission]);

      const result = await TestUtils.waitForElement('mission-list');
      expect(result).toBeDefined();
    });

    it('handles API service unavailability', async () => {
      // Mock service unavailable
      global.fetch = jest.fn().mockRejectedValue(
        new Error('Service unavailable')
      );

      // Should show appropriate error message
      const errorElement = await TestUtils.waitForElement('service-error');
      expect(errorElement).toHaveTextContent('Service temporarily unavailable');
    });

    it('implements circuit breaker pattern', async () => {
      // Simulate multiple failures
      const errors = Array.from({ length: 5 }, () =>
        new Error('Service failure')
      );

      global.fetch = jest.fn()
        .mockRejectedValueOnce(errors[0])
        .mockRejectedValueOnce(errors[1])
        .mockRejectedValueOnce(errors[2])
        .mockRejectedValueOnce(errors[3])
        .mockRejectedValueOnce(errors[4]);

      // Circuit breaker should open after threshold
      try {
        await fetch('/api/missions');
      } catch (error) {
        expect(error.message).toContain('Circuit breaker open');
      }
    });
  });

  describe('Performance', () => {
    it('completes mission creation within performance budget', async () => {
      const startTime = performance.now();
      
      await lifecycleManager.createMission({
        title: 'Performance Test Mission',
        description: 'Testing mission creation performance'
      });
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Mission creation should complete within 500ms
      expect(duration).toBeLessThan(500);
    });

    it('handles concurrent mission operations', async () => {
      const concurrentOperations = 10;
      const operations = Array.from({ length: concurrentOperations }, (_, i) =>
        lifecycleManager.createMission({
          title: `Concurrent Mission ${i}`,
          description: `Testing concurrent operation ${i}`
        })
      );

      const results = await Promise.all(operations);
      
      expect(results).toHaveLength(concurrentOperations);
      results.forEach(result => {
        expect(result).toHaveProperty('id');
      });
    });

    it('maintains responsive UI during heavy operations', async () => {
      // Simulate heavy processing
      const heavyOperation = new Promise(resolve => {
        setTimeout(resolve, 2000);
      });

      const startTime = performance.now();
      
      // UI should remain responsive
      await TestUtils.navigateWithKeyboard(['Tab', 'Enter']);
      
      const responseTime = performance.now() - startTime;
      
      // UI interactions should remain under 100ms
      expect(responseTime).toBeLessThan(100);
      
      await heavyOperation;
    });
  });
});