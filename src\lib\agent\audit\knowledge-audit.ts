import { ExecutionContext, ToolResult } from '../types';
import { AgentLogger } from '../logger';
import db, { schema } from '../../db';

/**
 * Comprehensive Knowledge Base Audit System
 * Tracks all knowledge base interactions and validates compliance
 */
export class KnowledgeBaseAuditSystem {
  private static instance: KnowledgeBaseAuditSystem;
  private auditLogs: KnowledgeAuditEntry[] = [];

  private constructor() {}

  static getInstance(): KnowledgeBaseAuditSystem {
    if (!KnowledgeBaseAuditSystem.instance) {
      KnowledgeBaseAuditSystem.instance = new KnowledgeBaseAuditSystem();
    }
    return KnowledgeBaseAuditSystem.instance;
  }

  /**
   * Log knowledge base query with detailed metrics
   */
  async logKnowledgeQuery(
    query: string,
    results: any[],
    context: ExecutionContext,
    metadata: {
      toolName: string;
      threshold?: number;
      searchMode?: string;
      sourceType?: string;
    }
  ): Promise<void> {
    const entry: KnowledgeAuditEntry = {
      id: this.generateId(),
      timestamp: new Date(),
      type: 'knowledge_query',
      missionId: context.missionId,
      planId: context.planId,
      taskId: context.taskId,
      query,
      resultCount: results.length,
      hasData: results.length > 0,
      averageScore: this.calculateAverageScore(results),
      toolName: metadata.toolName,
      metadata: {
        threshold: metadata.threshold,
        searchMode: metadata.searchMode,
        sourceType: metadata.sourceType,
        topScore: results.length > 0 ? Math.max(...results.map(r => r.score || 0)) : 0,
        sources: results.map(r => ({
          id: r.id,
          title: r.title,
          score: r.score,
          source: r.source
        }))
      }
    };

    this.auditLogs.push(entry);
    await this.persistAuditEntry(entry);

    context.logger.info(`Knowledge audit: Query "${query}" returned ${results.length} results (avg score: ${entry.averageScore?.toFixed(2)})`);
  }

  /**
   * Log validation failure with detailed context
   */
  async logValidationFailure(
    validationType: 'plan_validation' | 'task_validation' | 'knowledge_validation',
    issues: any[],
    context: ExecutionContext
  ): Promise<void> {
    const entry: KnowledgeAuditEntry = {
      id: this.generateId(),
      timestamp: new Date(),
      type: 'validation_failure',
      missionId: context.missionId,
      planId: context.planId,
      taskId: context.taskId,
      query: null,
      resultCount: 0,
      hasData: false,
      toolName: 'validator',
      metadata: {
        validationType,
        issues: issues.map(issue => ({
          type: issue.type,
          code: issue.code,
          message: issue.message,
          taskId: issue.taskId
        })),
        errorCount: issues.filter(i => i.type === 'error').length,
        warningCount: issues.filter(i => i.type === 'warning').length
      }
    };

    this.auditLogs.push(entry);
    await this.persistAuditEntry(entry);

    context.logger.error(`Knowledge validation failed: ${validationType} - ${issues.length} issues`);
  }

  /**
   * Log reasoning without knowledge violation
   */
  async logReasoningViolation(
    taskName: string,
    toolName: string,
    context: ExecutionContext
  ): Promise<void> {
    const entry: KnowledgeAuditEntry = {
      id: this.generateId(),
      timestamp: new Date(),
      type: 'reasoning_without_knowledge',
      missionId: context.missionId,
      planId: context.planId,
      taskId: context.taskId,
      query: null,
      resultCount: 0,
      hasData: false,
      toolName,
      metadata: {
        taskName,
        violation: 'reasoning_without_knowledge',
        severity: 'critical'
      }
    };

    this.auditLogs.push(entry);
    await this.persistAuditEntry(entry);

    context.logger.error(`CRITICAL VIOLATION: Task "${taskName}" attempted reasoning without knowledge base data`);
  }

  /**
   * Generate compliance report for a mission
   */
  async generateComplianceReport(missionId: string): Promise<KnowledgeComplianceReport> {
    const missionLogs = this.auditLogs.filter(log => log.missionId === missionId);
    
    const knowledgeQueries = missionLogs.filter(log => log.type === 'knowledge_query');
    const validationFailures = missionLogs.filter(log => log.type === 'validation_failure');
    const reasoningViolations = missionLogs.filter(log => log.type === 'reasoning_without_knowledge');

    const totalQueries = knowledgeQueries.length;
    const successfulQueries = knowledgeQueries.filter(log => log.hasData).length;
    const averageResultCount = totalQueries > 0 
      ? knowledgeQueries.reduce((sum, log) => sum + log.resultCount, 0) / totalQueries 
      : 0;

    const compliance: KnowledgeComplianceReport = {
      missionId,
      timestamp: new Date(),
      isCompliant: reasoningViolations.length === 0 && validationFailures.length === 0,
      summary: {
        totalKnowledgeQueries: totalQueries,
        successfulQueries,
        querySuccessRate: totalQueries > 0 ? (successfulQueries / totalQueries) * 100 : 0,
        averageResultCount,
        validationFailures: validationFailures.length,
        reasoningViolations: reasoningViolations.length
      },
      details: {
        knowledgeQueries: knowledgeQueries.map(log => ({
          timestamp: log.timestamp,
          query: log.query!,
          resultCount: log.resultCount,
          hasData: log.hasData,
          toolName: log.toolName,
          averageScore: log.averageScore
        })),
        violations: [
          ...validationFailures.map(log => ({
            type: 'validation_failure' as const,
            timestamp: log.timestamp,
            details: log.metadata
          })),
          ...reasoningViolations.map(log => ({
            type: 'reasoning_violation' as const,
            timestamp: log.timestamp,
            details: log.metadata
          }))
        ]
      },
      recommendations: this.generateRecommendations(missionLogs)
    };

    return compliance;
  }

  /**
   * Get audit trail for a specific mission
   */
  getAuditTrail(missionId: string): KnowledgeAuditEntry[] {
    return this.auditLogs
      .filter(log => log.missionId === missionId)
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * Get system-wide compliance metrics
   */
  getSystemMetrics(): SystemComplianceMetrics {
    const totalMissions = new Set(this.auditLogs.map(log => log.missionId)).size;
    const totalQueries = this.auditLogs.filter(log => log.type === 'knowledge_query').length;
    const totalViolations = this.auditLogs.filter(log => log.type === 'reasoning_without_knowledge').length;
    const totalValidationFailures = this.auditLogs.filter(log => log.type === 'validation_failure').length;

    return {
      totalMissions,
      totalKnowledgeQueries: totalQueries,
      totalViolations,
      totalValidationFailures,
      complianceRate: totalMissions > 0 ? ((totalMissions - totalViolations) / totalMissions) * 100 : 100,
      averageQueriesPerMission: totalMissions > 0 ? totalQueries / totalMissions : 0
    };
  }

  /**
   * Clear audit logs (for testing or maintenance)
   */
  clearAuditLogs(): void {
    this.auditLogs = [];
  }

  // Private helper methods
  private generateId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateAverageScore(results: any[]): number | undefined {
    if (results.length === 0) return undefined;
    const scores = results.map(r => r.score || 0).filter(score => score > 0);
    return scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : undefined;
  }

  private async persistAuditEntry(entry: KnowledgeAuditEntry): Promise<void> {
    try {
      // Store in database for persistence
      await db.insert(schema.executionLogs).values({
        missionId: entry.missionId,
        planId: entry.planId,
        taskId: entry.taskId,
        level: entry.type === 'reasoning_without_knowledge' ? 'error' : 'info',
        message: `Knowledge Audit: ${entry.type}`,
        data: {
          auditEntry: entry
        },
        timestamp: entry.timestamp
      });
    } catch (error) {
      console.error('Failed to persist audit entry:', error);
    }
  }

  private generateRecommendations(logs: KnowledgeAuditEntry[]): string[] {
    const recommendations: string[] = [];
    
    const knowledgeQueries = logs.filter(log => log.type === 'knowledge_query');
    const successfulQueries = knowledgeQueries.filter(log => log.hasData);
    
    if (knowledgeQueries.length === 0) {
      recommendations.push('No knowledge base queries detected. Ensure all missions start with knowledge retrieval.');
    } else if (successfulQueries.length / knowledgeQueries.length < 0.5) {
      recommendations.push('Low knowledge retrieval success rate. Consider improving query formulation or expanding the knowledge base.');
    }

    const validationFailures = logs.filter(log => log.type === 'validation_failure');
    if (validationFailures.length > 0) {
      recommendations.push('Validation failures detected. Review plan structure to ensure knowledge-base-first compliance.');
    }

    const reasoningViolations = logs.filter(log => log.type === 'reasoning_without_knowledge');
    if (reasoningViolations.length > 0) {
      recommendations.push('CRITICAL: Reasoning without knowledge violations detected. Review task dependencies and tool selection logic.');
    }

    if (recommendations.length === 0) {
      recommendations.push('Mission demonstrates good compliance with knowledge-base-first principles.');
    }

    return recommendations;
  }
}

// Type definitions
export interface KnowledgeAuditEntry {
  id: string;
  timestamp: Date;
  type: 'knowledge_query' | 'validation_failure' | 'reasoning_without_knowledge';
  missionId?: string;
  planId?: string;
  taskId?: string;
  query: string | null;
  resultCount: number;
  hasData: boolean;
  averageScore?: number;
  toolName: string;
  metadata: Record<string, any>;
}

export interface KnowledgeComplianceReport {
  missionId: string;
  timestamp: Date;
  isCompliant: boolean;
  summary: {
    totalKnowledgeQueries: number;
    successfulQueries: number;
    querySuccessRate: number;
    averageResultCount: number;
    validationFailures: number;
    reasoningViolations: number;
  };
  details: {
    knowledgeQueries: Array<{
      timestamp: Date;
      query: string;
      resultCount: number;
      hasData: boolean;
      toolName: string;
      averageScore?: number;
    }>;
    violations: Array<{
      type: 'validation_failure' | 'reasoning_violation';
      timestamp: Date;
      details: any;
    }>;
  };
  recommendations: string[];
}

export interface SystemComplianceMetrics {
  totalMissions: number;
  totalKnowledgeQueries: number;
  totalViolations: number;
  totalValidationFailures: number;
  complianceRate: number;
  averageQueriesPerMission: number;
}
