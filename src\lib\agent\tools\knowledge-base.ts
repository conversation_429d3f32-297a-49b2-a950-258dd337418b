import { BaseTool } from './base';
import { ToolResult, ExecutionContext } from '../types';
import { DeepSeekClient } from '../deepseek';

/**
 * Knowledge Base Query Tool - Primary tool for retrieving information from the vector database
 * This tool MUST be used before any reasoning or response generation
 */
export class KnowledgeBaseQueryTool extends BaseTool {
  name = 'knowledge_base_query';
  description = 'Query the vector database to retrieve relevant documents and information. This tool must be used first for any mission that requires information retrieval.';
  parameters = {
    query: { type: 'string', required: true, description: 'Natural language query to search the knowledge base' },
    limit: { type: 'number', required: false, description: 'Maximum number of results to return (default: 10)' },
    threshold: { type: 'number', required: false, description: 'Minimum similarity threshold 0-1 (default: 0.7)' },
    sourceType: { type: 'string', required: false, description: 'Filter by source type: document, confluence, web, manual' },
    searchMode: { type: 'string', required: false, description: 'Search mode: semantic, vector, hybrid (default: hybrid)' },
    timeRange: { type: 'string', required: false, description: 'Time range filter: last_week, last_month, last_year' },
  };

  constructor(private deepseek: DeepSeekClient) {
    super();
  }

  async execute(params: Record<string, any>, context?: ExecutionContext): Promise<ToolResult> {
    try {
      this.validateParams(params, ['query']);
      
      const { 
        query, 
        limit = 10, 
        threshold = 0.7, 
        sourceType,
        searchMode = 'hybrid',
        timeRange
      } = params;
      
      context?.logger.info(`Querying knowledge base: ${query}`);

      // Add time range filter to query if specified
      let enhancedQuery = query;
      if (timeRange) {
        const timeFilter = this.buildTimeFilter(timeRange);
        enhancedQuery = `${query} ${timeFilter}`;
      }

      // Call the knowledge search API
      const searchResponse = await fetch('/api/knowledge/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: enhancedQuery,
          limit,
          threshold,
          sourceType,
          searchMode,
        }),
      });

      if (!searchResponse.ok) {
        throw new Error(`Knowledge base search failed: ${searchResponse.statusText}`);
      }

      const searchData = await searchResponse.json();

      if (!searchData.success) {
        throw new Error(`Knowledge base search error: ${searchData.error}`);
      }

      const results = searchData.data.results || [];
      const insights = searchData.data.insights || [];

      // Validate that we have meaningful results
      if (results.length === 0) {
        context?.logger.warn(`No results found for query: ${query}`);
        return this.createResult(true, {
          query,
          results: [],
          totalFound: 0,
          insights: [],
          hasData: false,
          message: "No relevant information found in the knowledge base for this query."
        }, undefined, {
          searchMode,
          threshold,
          sourceType,
          queryEnhanced: enhancedQuery !== query
        });
      }

      // Filter results by threshold
      const filteredResults = results.filter((result: any) => result.score >= threshold);

      context?.logger.info(`Found ${filteredResults.length} relevant results above threshold ${threshold}`);

      // Extract key information for downstream reasoning
      const extractedData = await this.extractKeyInformation(filteredResults, query, context);

      return this.createResult(true, {
        query,
        results: filteredResults,
        totalFound: filteredResults.length,
        insights,
        hasData: true,
        extractedData,
        summary: await this.generateSummary(filteredResults, query, context),
      }, undefined, {
        searchMode,
        threshold,
        sourceType,
        queryEnhanced: enhancedQuery !== query,
        averageScore: filteredResults.reduce((sum: number, r: any) => sum + r.score, 0) / filteredResults.length
      });

    } catch (error) {
      context?.logger.error(`Knowledge base query failed: ${error}`, undefined, error as Error);
      return this.createResult(false, null, (error as Error).message);
    }
  }

  /**
   * Build time filter for queries
   */
  private buildTimeFilter(timeRange: string): string {
    const now = new Date();
    let startDate: Date;

    switch (timeRange) {
      case 'last_week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return `from the last week (since ${startDate.toISOString().split('T')[0]})`;
      case 'last_month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        return `from the last month (since ${startDate.toISOString().split('T')[0]})`;
      case 'last_year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        return `from the last year (since ${startDate.toISOString().split('T')[0]})`;
      default:
        return '';
    }
  }

  /**
   * Extract key information from search results for downstream reasoning
   */
  private async extractKeyInformation(results: any[], query: string, context?: ExecutionContext): Promise<any> {
    try {
      const topResults = results.slice(0, 5); // Use top 5 results
      const combinedContent = topResults.map(r => r.content || r.snippet || '').join('\n\n');

      if (!combinedContent.trim()) {
        return { message: "No content available for extraction" };
      }

      // Use DeepSeek to extract structured information
      const extractionPrompt = `
Extract key information from the following search results for the query: "${query}"

Search Results:
${combinedContent}

Please extract and structure the following information:
1. Key facts and data points
2. Relevant metrics or numbers
3. Important dates or time periods
4. Main concepts or topics
5. Any actionable insights

Return the information in a structured JSON format.
`;

      const response = await this.deepseek.chat([
        {
          role: 'system',
          content: 'You are an expert at extracting and structuring information from search results. Return only valid JSON.'
        },
        {
          role: 'user',
          content: extractionPrompt
        }
      ], { temperature: 0.1, maxTokens: 1000 });

      try {
        return JSON.parse(response.choices[0].message.content);
      } catch (parseError) {
        context?.logger.warn('Failed to parse extracted information as JSON');
        return { 
          rawExtraction: response.choices[0].message.content,
          message: "Information extracted but not in structured format"
        };
      }

    } catch (error) {
      context?.logger.error(`Failed to extract key information: ${error}`);
      return { error: "Failed to extract key information from results" };
    }
  }

  /**
   * Generate a summary of search results
   */
  private async generateSummary(results: any[], query: string, context?: ExecutionContext): Promise<string> {
    try {
      if (results.length === 0) {
        return "No relevant information found in the knowledge base.";
      }

      const topResults = results.slice(0, 3);
      const summaryContent = topResults.map(r => 
        `${r.title || 'Document'}: ${r.content || r.snippet || 'No content available'}`
      ).join('\n\n');

      const summaryPrompt = `
Summarize the following search results for the query: "${query}"

Results:
${summaryContent}

Provide a concise summary (2-3 sentences) of the key information found.
`;

      const response = await this.deepseek.chat([
        {
          role: 'system',
          content: 'You are an expert at summarizing search results. Provide concise, factual summaries.'
        },
        {
          role: 'user',
          content: summaryPrompt
        }
      ], { temperature: 0.2, maxTokens: 200 });

      return response.choices[0].message.content.trim();

    } catch (error) {
      context?.logger.error(`Failed to generate summary: ${error}`);
      return `Found ${results.length} relevant results for the query.`;
    }
  }
}

/**
 * Incident Query Tool - Specialized tool for querying incident data
 */
export class IncidentQueryTool extends BaseTool {
  name = 'incident_query';
  description = 'Query incident data from the knowledge base with specialized filters for incident analysis';
  parameters = {
    service: { type: 'string', required: false, description: 'Service name to filter incidents (e.g., "payment services")' },
    timeRange: { type: 'string', required: false, description: 'Time range: last_week, last_month, last_year' },
    severity: { type: 'string', required: false, description: 'Incident severity: low, medium, high, critical' },
    status: { type: 'string', required: false, description: 'Incident status: open, resolved, investigating' },
    limit: { type: 'number', required: false, description: 'Maximum number of incidents to return (default: 20)' },
  };

  constructor(private deepseek: DeepSeekClient) {
    super();
  }

  async execute(params: Record<string, any>, context?: ExecutionContext): Promise<ToolResult> {
    try {
      const { 
        service, 
        timeRange, 
        severity, 
        status, 
        limit = 20 
      } = params;

      // Build incident query
      let query = 'incidents';
      const filters = [];

      if (service) {
        filters.push(`service:${service}`);
        query += ` ${service}`;
      }

      if (severity) {
        filters.push(`severity:${severity}`);
        query += ` severity:${severity}`;
      }

      if (status) {
        filters.push(`status:${status}`);
        query += ` status:${status}`;
      }

      if (timeRange) {
        query += ` ${this.buildTimeFilter(timeRange)}`;
      }

      context?.logger.info(`Querying incidents: ${query}`);

      // Use the knowledge base query tool
      const knowledgeQuery = new KnowledgeBaseQueryTool(this.deepseek);
      const result = await knowledgeQuery.execute({
        query,
        limit,
        threshold: 0.6, // Lower threshold for incident data
        sourceType: 'incident',
        timeRange
      }, context);

      if (!result.success || !result.data?.hasData) {
        return this.createResult(true, {
          query,
          incidents: [],
          totalFound: 0,
          hasData: false,
          message: "No incidents found matching the specified criteria."
        });
      }

      // Process incident data
      const incidents = await this.processIncidentData(result.data.results, context);

      return this.createResult(true, {
        query,
        incidents,
        totalFound: incidents.length,
        hasData: true,
        summary: await this.generateIncidentSummary(incidents, params, context),
        filters: { service, timeRange, severity, status }
      });

    } catch (error) {
      context?.logger.error(`Incident query failed: ${error}`, undefined, error as Error);
      return this.createResult(false, null, (error as Error).message);
    }
  }

  private buildTimeFilter(timeRange: string): string {
    const now = new Date();
    switch (timeRange) {
      case 'last_week':
        return 'from the last week';
      case 'last_month':
        return 'from the last month';
      case 'last_year':
        return 'from the last year';
      default:
        return '';
    }
  }

  private async processIncidentData(results: any[], context?: ExecutionContext): Promise<any[]> {
    // Process and structure incident data
    return results.map(result => ({
      id: result.id,
      title: result.title,
      severity: this.extractSeverity(result.content),
      status: this.extractStatus(result.content),
      service: this.extractService(result.content),
      timestamp: result.metadata?.timestamp || result.metadata?.createdAt,
      description: result.snippet || result.content,
      score: result.score
    }));
  }

  private extractSeverity(content: string): string {
    const severityMatch = content.match(/severity:\s*(low|medium|high|critical)/i);
    return severityMatch ? severityMatch[1].toLowerCase() : 'unknown';
  }

  private extractStatus(content: string): string {
    const statusMatch = content.match(/status:\s*(open|resolved|investigating|closed)/i);
    return statusMatch ? statusMatch[1].toLowerCase() : 'unknown';
  }

  private extractService(content: string): string {
    const serviceMatch = content.match(/service:\s*([^\n,]+)/i);
    return serviceMatch ? serviceMatch[1].trim() : 'unknown';
  }

  private async generateIncidentSummary(incidents: any[], params: any, context?: ExecutionContext): Promise<string> {
    const total = incidents.length;
    const severityCounts = incidents.reduce((acc, inc) => {
      acc[inc.severity] = (acc[inc.severity] || 0) + 1;
      return acc;
    }, {});

    let summary = `Found ${total} incidents`;
    if (params.service) summary += ` for ${params.service}`;
    if (params.timeRange) summary += ` from ${params.timeRange.replace('_', ' ')}`;
    
    if (severityCounts.critical || severityCounts.high) {
      const critical = severityCounts.critical || 0;
      const high = severityCounts.high || 0;
      summary += `. High priority: ${critical} critical, ${high} high severity.`;
    }

    return summary;
  }
}
