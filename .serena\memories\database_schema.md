# Database Schema

The project uses SQLite with Drizzle ORM. Database file location: `./data/agent.db`

## Tables

### Missions
- **id**: Primary key (CUID)
- **title**: Mission title
- **description**: Detailed description  
- **status**: 'pending' | 'planning' | 'executing' | 'completed' | 'failed'
- **priority**: 'low' | 'medium' | 'high' | 'urgent'
- **createdAt**: Timestamp
- **updatedAt**: Timestamp  
- **completedAt**: Optional completion timestamp
- **metadata**: JSON metadata

### Plans
- Related to missions, stores planning information

### Tasks
- Individual tasks within plans

### Reflections
- Agent reflective learning data

### Execution Logs
- Logs of task execution

### Knowledge Base
- Indexed content from sources like Confluence

### Tool Usage
- Logs of tool utilization

## Database Commands
- `npm run db:generate` - Generate migrations
- `npm run db:migrate` - Run migrations
- `npm run db:init` - Initialize database
- `npm run db:studio` - Open Drizzle Studio