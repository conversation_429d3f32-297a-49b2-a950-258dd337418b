/**
 * Application-wide constants and configuration values
 */

// Mission Status Constants
export const MISSION_STATUS = {
  PENDING: 'pending',
  PLANNING: 'planning',
  EXECUTING: 'executing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  PAUSED: 'paused'
} as const;

export type MissionStatus = typeof MISSION_STATUS[keyof typeof MISSION_STATUS];

// Task Status Constants
export const TASK_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  FAILED: 'failed',
  SKIPPED: 'skipped'
} as const;

export type TaskStatus = typeof TASK_STATUS[keyof typeof TASK_STATUS];

// Plan Status Constants
export const PLAN_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  COMPLETED: 'completed',
  FAILED: 'failed',
  SUPERSEDED: 'superseded'
} as const;

export type PlanStatus = typeof PLAN_STATUS[keyof typeof PLAN_STATUS];

// Priority Levels
export const PRIORITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
} as const;

export type PriorityLevel = typeof PRIORITY_LEVELS[keyof typeof PRIORITY_LEVELS];

// Log Levels
export const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error'
} as const;

export type LogLevel = typeof LOG_LEVELS[keyof typeof LOG_LEVELS];

// Reflection Types
export const REFLECTION_TYPES = {
  PROGRESS_ASSESSMENT: 'progress_assessment',
  PLAN_OPTIMIZATION: 'plan_optimization',
  ERROR_ANALYSIS: 'error_analysis',
  SUCCESS_ANALYSIS: 'success_analysis'
} as const;

export type ReflectionType = typeof REFLECTION_TYPES[keyof typeof REFLECTION_TYPES];

// Cache Configuration
export const CACHE_CONFIG = {
  DEFAULT_TTL: 3600, // 1 hour in seconds
  SHORT_TTL: 300,    // 5 minutes
  LONG_TTL: 86400,   // 24 hours
  MAX_KEYS: 1000
} as const;

// Retry Configuration
export const RETRY_CONFIG = {
  MAX_ATTEMPTS: 3,
  BASE_DELAY: 1000,     // 1 second
  MAX_DELAY: 10000,     // 10 seconds
  BACKOFF_FACTOR: 2
} as const;

// WebSocket Configuration
export const WEBSOCKET_CONFIG = {
  RECONNECT_ATTEMPTS: 5,
  RECONNECT_DELAY: 1000,
  PING_INTERVAL: 30000,  // 30 seconds
  CONNECTION_TIMEOUT: 10000, // 10 seconds
  MAX_MESSAGE_SIZE: 1024 * 1024 // 1MB
} as const;

// Database Configuration
export const DATABASE_CONFIG = {
  BATCH_SIZE: 100,
  QUERY_TIMEOUT: 30000,  // 30 seconds
  MAX_CONNECTIONS: 10,
  VACUUM_THRESHOLD: 1000 // rows
} as const;

// Agent Configuration
export const AGENT_CONFIG = {
  COMPLETION_THRESHOLD: 0.9,
  PROGRESS_LOW_THRESHOLD: 0.5,
  MAX_TASK_RETRIES: 3,
  PLANNING_TIMEOUT: 60000,  // 1 minute
  EXECUTION_TIMEOUT: 300000 // 5 minutes
} as const;

// API Configuration
export const API_CONFIG = {
  DEFAULT_TIMEOUT: 30000,
  MAX_RETRIES: 3,
  RATE_LIMIT_DELAY: 5000,
  MAX_PAYLOAD_SIZE: 10 * 1024 * 1024 // 10MB
} as const;

// UI Configuration
export const UI_CONFIG = {
  MAX_LOGS_DISPLAY: 50,
  DEBOUNCE_DELAY: 300,
  ANIMATION_DURATION: 200,
  TOAST_DURATION: 5000
} as const;

// File Size Limits
export const FILE_LIMITS = {
  MAX_UPLOAD_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_DOCUMENT_SIZE: 10 * 1024 * 1024, // 10MB
  CHUNK_SIZE: 1024 * 1024 // 1MB
} as const;

// Error Codes
export const ERROR_CODES = {
  // Network Errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  
  // API Errors
  API_ERROR: 'API_ERROR',
  RATE_LIMITED: 'RATE_LIMITED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  
  // Database Errors
  DATABASE_ERROR: 'DATABASE_ERROR',
  CONSTRAINT_VIOLATION: 'CONSTRAINT_VIOLATION',
  TRANSACTION_FAILED: 'TRANSACTION_FAILED',
  
  // Validation Errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Agent Errors
  PLANNING_FAILED: 'PLANNING_FAILED',
  EXECUTION_FAILED: 'EXECUTION_FAILED',
  TOOL_NOT_FOUND: 'TOOL_NOT_FOUND',
  MISSION_NOT_FOUND: 'MISSION_NOT_FOUND',
  
  // System Errors
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  CIRCUIT_BREAKER_OPEN: 'CIRCUIT_BREAKER_OPEN',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE'
} as const;

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

// Default Values
export const DEFAULTS = {
  MISSION_PRIORITY: PRIORITY_LEVELS.MEDIUM,
  MISSION_STATUS: MISSION_STATUS.PENDING,
  TASK_STATUS: TASK_STATUS.PENDING,
  PLAN_STATUS: PLAN_STATUS.DRAFT,
  LOG_LEVEL: LOG_LEVELS.INFO,
  CACHE_TTL: CACHE_CONFIG.DEFAULT_TTL,
  RETRY_ATTEMPTS: RETRY_CONFIG.MAX_ATTEMPTS
} as const;

// Environment-specific configurations
export const ENV_CONFIG = {
  DEVELOPMENT: {
    LOG_LEVEL: LOG_LEVELS.DEBUG,
    WEBSOCKET_URL: 'ws://localhost:8080/ws',
    API_BASE_URL: 'http://localhost:3000/api'
  },
  PRODUCTION: {
    LOG_LEVEL: LOG_LEVELS.INFO,
    WEBSOCKET_URL: 'wss://your-domain.com/ws',
    API_BASE_URL: 'https://your-domain.com/api'
  }
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  MISSION_TITLE: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 200
  },
  MISSION_DESCRIPTION: {
    MIN_LENGTH: 10,
    MAX_LENGTH: 2000
  },
  TASK_TITLE: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 100
  },
  PLAN_TITLE: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 150
  }
} as const;
