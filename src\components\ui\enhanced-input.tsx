import * as React from 'react';
import { cn } from '@/lib/utils';
import { useUniqueId, announceToScreenReader } from '@/lib/ui/accessibility';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onClear?: () => void;
  showClearButton?: boolean;
  containerClassName?: string;
  labelClassName?: string;
  errorClassName?: string;
  helperTextClassName?: string;
}

const EnhancedInput = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    type = 'text',
    label,
    error,
    helperText,
    leftIcon,
    rightIcon,
    onClear,
    showClearButton = false,
    containerClassName,
    labelClassName,
    errorClassName,
    helperTextClassName,
    id,
    'aria-describedby': ariaDescribedBy,
    onChange,
    value,
    ...props
  }, ref) => {
    const uniqueId = useUniqueId('input');
    const inputId = id || uniqueId;
    const errorId = error ? `${inputId}-error` : undefined;
    const helperId = helperText ? `${inputId}-helper` : undefined;
    
    const describedBy = [
      ariaDescribedBy,
      errorId,
      helperId
    ].filter(Boolean).join(' ') || undefined;
    
    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      onChange?.(event);
      
      // Announce validation errors to screen readers
      if (error) {
        announceToScreenReader(`Input error: ${error}`, 'assertive');
      }
    };
    
    const handleClear = () => {
      onClear?.();
      announceToScreenReader('Input cleared', 'polite');
    };
    
    const shouldShowClearButton = showClearButton && onClear && value && String(value).length > 0;
    
    return (
      <div className={cn('space-y-1', containerClassName)}>
        {label && (
          <label 
            htmlFor={inputId}
            className={cn(
              'block text-sm font-medium text-foreground',
              error && 'text-destructive',
              labelClassName
            )}
          >
            {label}
            {props.required && (
              <span className="ml-1 text-destructive" aria-label="required">
                *
              </span>
            )}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}
          
          <input
            type={type}
            className={cn(
              'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
              leftIcon && 'pl-10',
              (rightIcon || shouldShowClearButton) && 'pr-10',
              error && 'border-destructive focus-visible:ring-destructive',
              className
            )}
            ref={ref}
            id={inputId}
            aria-describedby={describedBy}
            aria-invalid={!!error}
            value={value}
            onChange={handleChange}
            {...props}
          />
          
          {(rightIcon || shouldShowClearButton) && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
              {shouldShowClearButton && (
                <button
                  type="button"
                  onClick={handleClear}
                  className="text-muted-foreground hover:text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1 rounded-sm p-0.5"
                  aria-label="Clear input"
                  tabIndex={0}
                >
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              )}
              {rightIcon && (
                <div className="text-muted-foreground">
                  {rightIcon}
                </div>
              )}
            </div>
          )}
        </div>
        
        {error && (
          <p 
            id={errorId}
            className={cn(
              'text-sm text-destructive',
              errorClassName
            )}
            role="alert"
            aria-live="polite"
          >
            {error}
          </p>
        )}
        
        {helperText && !error && (
          <p 
            id={helperId}
            className={cn(
              'text-sm text-muted-foreground',
              helperTextClassName
            )}
          >
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

EnhancedInput.displayName = 'EnhancedInput';

export { EnhancedInput };