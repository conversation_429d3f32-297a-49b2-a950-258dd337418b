import { useEffect, useState, useCallback } from 'react';
import { SSEClient, MissionUpdate, TaskUpdate, LogUpdate, ReflectionUpdate, ConnectionStatus } from '@/lib/sse/client';
import { WebSocketClient } from '@/lib/websocket/client';

export type ConnectionType = 'websocket' | 'sse' | 'hybrid';

export interface MissionUpdateState {
  missionId: string;
  status: string;
  lastUpdate: string;
  tasks: Map<string, TaskUpdate>;
  logs: LogUpdate[];
  reflections?: ReflectionUpdate[];
  currentTask?: string;
  progress?: any;
}

export interface UseMissionUpdatesSSEOptions {
  connectionType?: ConnectionType;
  wsUrl?: string;
  baseUrl?: string;
  autoConnect?: boolean;
  maxLogs?: number;
  fallbackToSSE?: boolean;
  preferredConnection?: 'websocket' | 'sse'; // Which to try first in hybrid mode
  retryOnFailure?: boolean;
}

export function useMissionUpdatesSSE(
  missionId: string,
  options: UseMissionUpdatesSSEOptions = {}
) {
  const {
    connectionType = 'hybrid',
    wsUrl,
    baseUrl,
    autoConnect = true,
    maxLogs = 100,
    fallbackToSSE = true,
    preferredConnection = 'websocket',
    retryOnFailure = true,
  } = options;
  
  const [sseClient] = useState(() => 
    connectionType !== 'websocket' ? new SSEClient(missionId, baseUrl) : null
  );
  const [wsClient] = useState(() => 
    connectionType !== 'sse' ? new WebSocketClient(wsUrl) : null
  );
  
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [activeConnection, setActiveConnection] = useState<'websocket' | 'sse' | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({ 
    connected: false, 
    connecting: false 
  });
  
  const [missionState, setMissionState] = useState<MissionUpdateState>({
    missionId,
    status: 'pending',
    lastUpdate: new Date().toISOString(),
    tasks: new Map(),
    logs: [],
    reflections: [],
  });

  // Update mission state
  const updateMissionState = useCallback((updates: Partial<MissionUpdateState>) => {
    setMissionState(prev => ({
      ...prev,
      ...updates,
      lastUpdate: new Date().toISOString(),
    }));
  }, []);

  // Connection management
  const connectSSE = useCallback(async (): Promise<boolean> => {
    if (!sseClient) return false;
    
    try {
      console.log(`[SSE] Attempting to connect for mission: ${missionId}`);
      await sseClient.connect();
      setActiveConnection('sse');
      setConnectionError(null);
      console.log(`[SSE] Successfully connected for mission: ${missionId}`);
      return true;
    } catch (error) {
      console.error(`[SSE] Connection failed for mission ${missionId}:`, error);
      setConnectionError(`SSE connection failed: ${(error as Error).message}`);
      return false;
    }
  }, [sseClient, missionId]);

  const connectWebSocket = useCallback(async (): Promise<boolean> => {
    if (!wsClient) return false;
    
    try {
      console.log(`[WS] Attempting to connect for mission: ${missionId}`);
      await wsClient.connect();
      wsClient.subscribeMission(missionId);
      setActiveConnection('websocket');
      setConnectionError(null);
      console.log(`[WS] Successfully connected for mission: ${missionId}`);
      return true;
    } catch (error) {
      console.error(`[WS] Connection failed for mission ${missionId}:`, error);
      setConnectionError(`WebSocket connection failed: ${(error as Error).message}`);
      return false;
    }
  }, [wsClient, missionId]);

  // Hybrid connection strategy
  const connectHybrid = useCallback(async (): Promise<boolean> => {
    if (connectionType === 'websocket') {
      return connectWebSocket();
    }
    
    if (connectionType === 'sse') {
      return connectSSE();
    }
    
    // Hybrid mode: try preferred connection first, fallback to other
    const primaryConnect = preferredConnection === 'websocket' ? connectWebSocket : connectSSE;
    const fallbackConnect = preferredConnection === 'websocket' ? connectSSE : connectWebSocket;
    
    console.log(`[Hybrid] Trying ${preferredConnection} first for mission: ${missionId}`);
    
    if (await primaryConnect()) {
      return true;
    }
    
    if (fallbackToSSE || preferredConnection === 'sse') {
      console.log(`[Hybrid] Falling back to ${preferredConnection === 'websocket' ? 'SSE' : 'WebSocket'} for mission: ${missionId}`);
      return fallbackConnect();
    }
    
    return false;
  }, [connectionType, preferredConnection, connectWebSocket, connectSSE, fallbackToSSE, missionId]);

  // Manual connection methods
  const connect = useCallback(async (): Promise<boolean> => {
    setConnectionError(null);
    return connectHybrid();
  }, [connectHybrid]);

  const disconnect = useCallback(() => {
    console.log(`[Hybrid] Disconnecting from mission: ${missionId}`);
    
    if (wsClient) {
      wsClient.unsubscribeMission(missionId);
      wsClient.disconnect();
    }
    
    if (sseClient) {
      sseClient.disconnect();
    }
    
    setActiveConnection(null);
    setIsConnected(false);
    setConnectionError(null);
  }, [wsClient, sseClient, missionId]);

  // Connection switching
  const switchConnection = useCallback(async (newType: 'websocket' | 'sse'): Promise<boolean> => {
    console.log(`[Hybrid] Switching to ${newType} for mission: ${missionId}`);
    
    // Disconnect current connection
    disconnect();
    
    // Connect to new type
    if (newType === 'websocket') {
      return connectWebSocket();
    } else {
      return connectSSE();
    }
  }, [disconnect, connectWebSocket, connectSSE, missionId]);

  // Data access methods
  const getTasks = useCallback((): TaskUpdate[] => {
    return Array.from(missionState.tasks.values());
  }, [missionState.tasks]);

  const getLogs = useCallback((): LogUpdate[] => {
    return missionState.logs;
  }, [missionState.logs]);

  const getReflections = useCallback((): ReflectionUpdate[] => {
    return missionState.reflections || [];
  }, [missionState.reflections]);

  // Clear mission data
  const clearMission = useCallback(() => {
    setMissionState({
      missionId,
      status: 'pending',
      lastUpdate: new Date().toISOString(),
      tasks: new Map(),
      logs: [],
      reflections: [],
    });
  }, [missionId]);

  // Event handlers
  useEffect(() => {
    if (!missionId) return;

    const handleConnected = () => {
      setIsConnected(true);
      setConnectionError(null);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
      
      // Auto-reconnect if enabled
      if (retryOnFailure && autoConnect) {
        console.log(`[Hybrid] Connection lost, attempting reconnect for mission: ${missionId}`);
        setTimeout(() => {
          connectHybrid().catch(error => {
            console.error(`[Hybrid] Reconnection failed for mission ${missionId}:`, error);
          });
        }, 2000);
      }
    };

    const handleError = (error: any) => {
      console.error(`[Hybrid] Error for mission ${missionId}:`, error);
      setConnectionError(error.message || 'Connection error');
    };

    const handleStatusUpdate = (status: ConnectionStatus) => {
      setConnectionStatus(status);
    };

    const handleMissionUpdate = (update: MissionUpdate) => {
      updateMissionState({
        status: update.status,
        currentTask: update.currentTask,
        progress: update.progress,
      });
    };

    const handleTaskUpdate = (update: TaskUpdate) => {
      setMissionState(prev => {
        const newTasks = new Map(prev.tasks);
        newTasks.set(update.taskId, update);
        
        return {
          ...prev,
          tasks: newTasks,
          lastUpdate: new Date().toISOString(),
        };
      });
    };

    const handleLogUpdate = (update: LogUpdate) => {
      setMissionState(prev => {
        const newLogs = [...prev.logs, update];
        
        // Keep only the most recent logs
        if (newLogs.length > maxLogs) {
          newLogs.splice(0, newLogs.length - maxLogs);
        }
        
        return {
          ...prev,
          logs: newLogs,
          lastUpdate: new Date().toISOString(),
        };
      });
    };

    const handleReflectionUpdate = (update: ReflectionUpdate) => {
      setMissionState(prev => {
        const newReflections = [...(prev.reflections || []), update];
        
        return {
          ...prev,
          reflections: newReflections,
          lastUpdate: new Date().toISOString(),
        };
      });
    };

    // Register SSE event listeners
    if (sseClient) {
      sseClient.on('connected', handleConnected);
      sseClient.on('disconnected', handleDisconnected);
      sseClient.on('error', handleError);
      sseClient.on('status', handleStatusUpdate);
      sseClient.on('mission.update', handleMissionUpdate);
      sseClient.on('task.update', handleTaskUpdate);
      sseClient.on('log.update', handleLogUpdate);
      sseClient.on('reflection.update', handleReflectionUpdate);
    }

    // Register WebSocket event listeners
    if (wsClient) {
      wsClient.on('connected', handleConnected);
      wsClient.on('disconnected', handleDisconnected);
      wsClient.on('error', handleError);
      wsClient.on('status', handleStatusUpdate);
      wsClient.on('mission.update', handleMissionUpdate);
      wsClient.on('task.update', handleTaskUpdate);
      wsClient.on('log.update', handleLogUpdate);
      wsClient.on('reflection.update', handleReflectionUpdate);
    }

    // Auto-connect if enabled
    if (autoConnect && !isConnected) {
      connectHybrid().catch(error => {
        console.warn(`[Hybrid] Auto-connect failed for mission ${missionId}:`, error);
        setConnectionError('Real-time updates unavailable');
      });
    }

    // Cleanup
    return () => {
      if (sseClient) {
        sseClient.off('connected', handleConnected);
        sseClient.off('disconnected', handleDisconnected);
        sseClient.off('error', handleError);
        sseClient.off('status', handleStatusUpdate);
        sseClient.off('mission.update', handleMissionUpdate);
        sseClient.off('task.update', handleTaskUpdate);
        sseClient.off('log.update', handleLogUpdate);
        sseClient.off('reflection.update', handleReflectionUpdate);
      }

      if (wsClient) {
        wsClient.off('connected', handleConnected);
        wsClient.off('disconnected', handleDisconnected);
        wsClient.off('error', handleError);
        wsClient.off('status', handleStatusUpdate);
        wsClient.off('mission.update', handleMissionUpdate);
        wsClient.off('task.update', handleTaskUpdate);
        wsClient.off('log.update', handleLogUpdate);
        wsClient.off('reflection.update', handleReflectionUpdate);
      }
    };
  }, [
    missionId,
    sseClient,
    wsClient,
    autoConnect,
    isConnected,
    maxLogs,
    connectHybrid,
    updateMissionState,
    retryOnFailure,
  ]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    // Connection state
    isConnected,
    connectionError,
    activeConnection,
    connectionStatus,
    
    // Mission state
    missionState,
    
    // Connection management
    connect,
    disconnect,
    switchConnection,
    
    // Data access
    getTasks,
    getLogs,
    getReflections,
    clearMission,
    
    // Compatibility with existing WebSocket hook
    missions: new Map([[missionId, missionState]]),
    subscribeMission: (id: string) => {
      if (id === missionId) {
        connect();
      }
    },
    unsubscribeMission: (id: string) => {
      if (id === missionId) {
        disconnect();
      }
    },
    getMission: (id: string) => {
      return id === missionId ? missionState : undefined;
    },
  };
}

// Simplified hook for SSE-only usage
export function useSSEMissionUpdates(
  missionId: string,
  options: Omit<UseMissionUpdatesSSEOptions, 'connectionType'> = {}
) {
  return useMissionUpdatesSSE(missionId, {
    ...options,
    connectionType: 'sse',
  });
}

// Backward compatibility hook
export function useWebSocketMissionUpdates(
  missionId: string,
  options: Omit<UseMissionUpdatesSSEOptions, 'connectionType'> = {}
) {
  return useMissionUpdatesSSE(missionId, {
    ...options,
    connectionType: 'websocket',
  });
} 