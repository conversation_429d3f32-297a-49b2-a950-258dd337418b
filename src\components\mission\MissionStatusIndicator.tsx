"use client"

import { <PERSON>Circle, XCircle, Clock, Brain, Zap } from "lucide-react"
import { cn } from "@/lib/utils"

interface MissionStatusIndicatorProps {
  status: string;
  isActive?: boolean;
  showIcon?: boolean;
  progress?: number;
  size?: 'sm' | 'md' | 'lg';
}

export function MissionStatusIndicator({ 
  status, 
  isActive = false, 
  showIcon = false, 
  progress,
  size = 'md' 
}: MissionStatusIndicatorProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'executing': return <Zap className="h-4 w-4 text-blue-500" />;
      case 'planning': return <Brain className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDotSize = () => {
    switch (size) {
      case 'sm': return 'w-1.5 h-1.5';
      case 'lg': return 'w-3 h-3';
      default: return 'w-2 h-2';
    }
  };

  const getDotColor = () => {
    switch (status) {
      case "completed": return "bg-green-500";
      case "executing": return "bg-blue-500";
      case "planning": return "bg-orange-500";
      case "in-progress": return "bg-yellow-500";
      case "failed": return "bg-red-500";
      case "pending": return "bg-gray-400";
      default: return "bg-gray-400";
    }
  };

  if (showIcon) {
    return getStatusIcon();
  }

  return (
    <div className="flex items-center gap-1">
      <div className={cn(
        getDotSize(),
        "rounded-full",
        getDotColor(),
        isActive && "animate-pulse"
      )} />
      {progress !== undefined && progress > 0 && (
        <span className="text-xs text-muted-foreground">
          {progress}%
        </span>
      )}
    </div>
  );
}

export function getStatusColor(status: string) {
  switch (status) {
    case 'completed': return 'bg-green-100 text-green-800';
    case 'failed': return 'bg-red-100 text-red-800';
    case 'executing': return 'bg-blue-100 text-blue-800';
    case 'planning': return 'bg-yellow-100 text-yellow-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}