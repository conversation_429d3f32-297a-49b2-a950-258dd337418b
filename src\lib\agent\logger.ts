import { Logger } from './types';
import { LOG_LEVELS, LogLevel } from '../constants';
import db, { schema } from '../db';

/**
 * Context for logging operations
 */
export interface LogContext {
  missionId?: string;
  planId?: string;
  taskId?: string;
  userId?: string;
  sessionId?: string;
}

/**
 * Log entry structure
 */
export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  context?: LogContext;
  data?: any;
  error?: Error;
}

/**
 * Enhanced logger implementation with database persistence and WebSocket broadcasting
 */
export class AgentLogger implements Logger {
  private context: LogContext;
  private enableConsoleOutput: boolean;
  private enableDatabaseStorage: boolean;
  private enableWebSocketBroadcast: boolean;

  constructor(
    context: LogContext = {},
    options: {
      enableConsoleOutput?: boolean;
      enableDatabaseStorage?: boolean;
      enableWebSocketBroadcast?: boolean;
    } = {}
  ) {
    this.context = context;
    this.enableConsoleOutput = options.enableConsoleOutput ?? true;
    this.enableDatabaseStorage = options.enableDatabaseStorage ?? true;
    this.enableWebSocketBroadcast = options.enableWebSocketBroadcast ?? true;
  }

  /**
   * Update the logging context
   */
  updateContext(context: Partial<LogContext>): void {
    this.context = { ...this.context, ...context };
  }

  /**
   * Get current logging context
   */
  getContext(): LogContext {
    return { ...this.context };
  }

  /**
   * Core logging method
   */
  private async log(level: LogLevel, message: string, data?: any, error?: Error): Promise<void> {
    const logEntry: LogEntry = {
      level,
      message,
      timestamp: new Date(),
      context: this.context,
      data,
      error
    };

    // Console output
    if (this.enableConsoleOutput) {
      this.logToConsole(logEntry);
    }

    // Database storage
    if (this.enableDatabaseStorage) {
      await this.logToDatabase(logEntry);
    }

    // WebSocket broadcast
    if (this.enableWebSocketBroadcast && this.context.missionId) {
      await this.broadcastLogUpdate(logEntry);
    }
  }

  /**
   * Log to console with formatting
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const contextStr = entry.context?.missionId ? `[Mission:${entry.context.missionId}]` : '';
    const prefix = `[${timestamp}] [${entry.level.toUpperCase()}] ${contextStr}`;
    
    const consoleMethod = this.getConsoleMethod(entry.level);
    
    if (entry.data) {
      consoleMethod(`${prefix} ${entry.message}`, entry.data);
    } else {
      consoleMethod(`${prefix} ${entry.message}`);
    }

    if (entry.error) {
      console.error('Error details:', entry.error);
    }
  }

  /**
   * Get appropriate console method for log level
   */
  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LOG_LEVELS.ERROR:
        return console.error;
      case LOG_LEVELS.WARN:
        return console.warn;
      case LOG_LEVELS.DEBUG:
        return console.debug;
      default:
        return console.log;
    }
  }

  /**
   * Store log entry in database
   */
  private async logToDatabase(entry: LogEntry): Promise<void> {
    try {
      await db.insert(schema.executionLogs).values({
        missionId: entry.context?.missionId,
        planId: entry.context?.planId,
        taskId: entry.context?.taskId,
        level: entry.level,
        message: entry.message,
        data: entry.data,
        timestamp: entry.timestamp,
      });
    } catch (error) {
      // Don't fail the main operation if logging fails
      console.error('Failed to store log in database:', error);
    }
  }

  /**
   * Broadcast log update via WebSocket
   */
  private async broadcastLogUpdate(entry: LogEntry): Promise<void> {
    try {
      const { getWebSocketServer } = await import('@/lib/websocket/server');
      const wsServer = getWebSocketServer();
      
      wsServer.broadcastLogUpdate({
        missionId: entry.context?.missionId!,
        level: entry.level,
        message: entry.message,
        timestamp: entry.timestamp.toISOString(),
        context: entry.data,
      });
    } catch (error) {
      // Don't fail the main operation if WebSocket broadcast fails
      console.warn('Failed to broadcast log update:', error);
    }
  }

  /**
   * Create a child logger with additional context
   */
  child(additionalContext: Partial<LogContext>): AgentLogger {
    return new AgentLogger(
      { ...this.context, ...additionalContext },
      {
        enableConsoleOutput: this.enableConsoleOutput,
        enableDatabaseStorage: this.enableDatabaseStorage,
        enableWebSocketBroadcast: this.enableWebSocketBroadcast,
      }
    );
  }

  /**
   * Log debug message
   */
  debug(message: string, data?: any): void {
    this.log(LOG_LEVELS.DEBUG, message, data);
  }

  /**
   * Log info message
   */
  info(message: string, data?: any): void {
    this.log(LOG_LEVELS.INFO, message, data);
  }

  /**
   * Log warning message
   */
  warn(message: string, data?: any): void {
    this.log(LOG_LEVELS.WARN, message, data);
  }

  /**
   * Log error message
   */
  error(message: string, data?: any, error?: Error): void {
    this.log(LOG_LEVELS.ERROR, message, data, error);
  }

  /**
   * Log with custom level
   */
  logWithLevel(level: LogLevel, message: string, data?: any, error?: Error): void {
    this.log(level, message, data, error);
  }
}

/**
 * Create a logger instance with context
 */
export function createLogger(context: LogContext = {}): AgentLogger {
  return new AgentLogger(context);
}

/**
 * Create a logger for a specific mission
 */
export function createMissionLogger(missionId: string, additionalContext: Partial<LogContext> = {}): AgentLogger {
  return new AgentLogger({ missionId, ...additionalContext });
}

/**
 * Create a logger for a specific task
 */
export function createTaskLogger(
  missionId: string,
  planId: string,
  taskId: string,
  additionalContext: Partial<LogContext> = {}
): AgentLogger {
  return new AgentLogger({ missionId, planId, taskId, ...additionalContext });
}
