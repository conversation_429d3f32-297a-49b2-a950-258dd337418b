"use client"

import { Target, Zap } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { MissionDetails, RealtimeStatus } from "./types"
import { MissionStatusIndicator, getStatusColor } from "./MissionStatusIndicator"
import { MissionControls } from "./MissionControls"

interface MissionProgressHeaderProps {
  missionDetails: MissionDetails;
  realtimeStatus: RealtimeStatus;
  isLoading: boolean;
  onMissionAction: (action: 'pause' | 'resume' | 'cancel') => void;
}

export function MissionProgressHeader({
  missionDetails,
  realtimeStatus,
  isLoading,
  onMissionAction
}: MissionProgressHeaderProps) {
  const calculateProgress = () => {
    // Use real-time WebSocket progress if available, otherwise fallback to task-based calculation
    if (realtimeStatus.progress?.percentage !== undefined) {
      return realtimeStatus.progress.percentage;
    }
    
    if (!missionDetails?.tasks.length) return 0;
    const completedTasks = missionDetails.tasks.filter(t => t.status === 'completed').length;
    return (completedTasks / missionDetails.tasks.length) * 100;
  };

  if (isLoading) {
    return (
      <div className="border-b border-border bg-muted/30 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Target className="h-4 w-4 animate-pulse" />
            <span>Loading mission details...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="border-b border-border bg-muted/30 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="space-y-3">
          {/* Mission Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <MissionStatusIndicator 
                status={realtimeStatus.status} 
                showIcon={true}
              />
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="font-medium">{missionDetails.mission.title}</h3>
                  <Badge className={`${getStatusColor(realtimeStatus.status)} ${realtimeStatus.isActive ? 'animate-pulse' : ''}`}>
                    {realtimeStatus.status}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {missionDetails.mission.description}
                </p>
                {realtimeStatus.currentTask && (
                  <div className="mt-2 flex items-center gap-1 text-sm text-blue-600">
                    <Zap className="h-3 w-3" />
                    <span>Current: {realtimeStatus.currentTask}</span>
                  </div>
                )}
              </div>
            </div>
            <MissionControls
              status={realtimeStatus.status}
              onPause={() => onMissionAction('pause')}
              onResume={() => onMissionAction('resume')}
              onCancel={() => onMissionAction('cancel')}
            />
          </div>

          {/* Progress Bar */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Progress</span>
              <div className="flex items-center gap-2">
                <span>{Math.round(calculateProgress())}%</span>
                {realtimeStatus.isActive && (
                  <div className="flex items-center gap-1 text-xs text-blue-600">
                    <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" />
                    <span>Active</span>
                  </div>
                )}
              </div>
            </div>
            <Progress value={calculateProgress()} className="h-2" />
            {realtimeStatus.lastUpdate && (
              <div className="text-xs text-muted-foreground mt-1">
                Last updated: {realtimeStatus.lastUpdate?.toLocaleTimeString()}
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-4 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Total Tasks</p>
              <p className="font-semibold">{missionDetails.tasks.length}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Completed</p>
              <p className="font-semibold text-green-600">
                {missionDetails.tasks.filter(t => t.status === 'completed').length}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">In Progress</p>
              <p className="font-semibold text-blue-600">
                {missionDetails.tasks.filter(t => t.status === 'in_progress').length}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Failed</p>
              <p className="font-semibold text-red-600">
                {missionDetails.tasks.filter(t => t.status === 'failed').length}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}