import { useEffect, useState, useCallback } from 'react';
import { WebSocketClient, MissionUpdate, TaskUpdate, LogUpdate } from '@/lib/websocket/client';

export interface MissionUpdateState {
  missionId: string;
  status: string;
  progress?: {
    currentTask?: string;
    completedTasks: number;
    totalTasks: number;
    percentage: number;
  };
  message?: string;
  lastUpdate: string;
  tasks: Map<string, TaskUpdate>;
  logs: LogUpdate[];
}

export interface UseMissionUpdatesOptions {
  wsUrl?: string;
  autoConnect?: boolean;
  maxLogs?: number;
}

export function useMissionUpdates(options: UseMissionUpdatesOptions = {}) {
  const { wsUrl, autoConnect = true, maxLogs = 100 } = options;
  
  const [client] = useState(() => new WebSocketClient(wsUrl));
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [missions, setMissions] = useState<Map<string, MissionUpdateState>>(new Map());

  // Update mission state
  const updateMission = useCallback((missionId: string, updates: Partial<MissionUpdateState>) => {
    setMissions(prev => {
      const newMissions = new Map(prev);
      const existing = newMissions.get(missionId);
      
      newMissions.set(missionId, {
        missionId,
        status: 'pending',
        lastUpdate: new Date().toISOString(),
        tasks: new Map(),
        logs: [],
        ...existing,
        ...updates,
      });
      
      return newMissions;
    });
  }, []);

  // Subscribe to a mission for updates
  const subscribeMission = useCallback((missionId: string) => {
    client.subscribeMission(missionId);
    
    // Initialize mission state if not exists
    if (!missions.has(missionId)) {
      updateMission(missionId, {
        missionId,
        status: 'pending',
        tasks: new Map(),
        logs: [],
      });
    }
  }, [client, missions, updateMission]);

  // Unsubscribe from mission updates
  const unsubscribeMission = useCallback((missionId: string) => {
    client.unsubscribeMission(missionId);
  }, [client]);

  // Request current status for a mission
  const requestMissionStatus = useCallback((missionId: string) => {
    client.requestMissionStatus(missionId);
  }, [client]);

  // Get mission state
  const getMission = useCallback((missionId: string): MissionUpdateState | undefined => {
    return missions.get(missionId);
  }, [missions]);

  // Get all missions
  const getAllMissions = useCallback((): MissionUpdateState[] => {
    return Array.from(missions.values());
  }, [missions]);

  // Clear mission data
  const clearMission = useCallback((missionId: string) => {
    setMissions(prev => {
      const newMissions = new Map(prev);
      newMissions.delete(missionId);
      return newMissions;
    });
  }, []);

  // Clear all mission data
  const clearAllMissions = useCallback(() => {
    setMissions(new Map());
  }, []);

  useEffect(() => {
    // Connection status handlers
    const handleConnected = () => {
      setIsConnected(true);
      setConnectionError(null);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
    };

    const handleError = (error: any) => {
      setConnectionError(error.message || 'Connection error');
      setIsConnected(false);
    };

    // Mission update handlers
    const handleMissionUpdate = (update: MissionUpdate) => {
      updateMission(update.missionId, {
        status: update.status,
        progress: update.progress,
        message: update.message,
        lastUpdate: new Date().toISOString(),
      });
    };

    const handleTaskUpdate = (update: TaskUpdate) => {
      setMissions(prev => {
        const newMissions = new Map(prev);
        const mission = newMissions.get(update.missionId);
        
        if (mission) {
          const newTasks = new Map(mission.tasks);
          newTasks.set(update.taskId, update);
          
          newMissions.set(update.missionId, {
            ...mission,
            tasks: newTasks,
            lastUpdate: new Date().toISOString(),
          });
        }
        
        return newMissions;
      });
    };

    const handleLogUpdate = (update: LogUpdate) => {
      setMissions(prev => {
        const newMissions = new Map(prev);
        const mission = newMissions.get(update.missionId);
        
        if (mission) {
          const newLogs = [...mission.logs, update];
          
          // Keep only the most recent logs
          if (newLogs.length > maxLogs) {
            newLogs.splice(0, newLogs.length - maxLogs);
          }
          
          newMissions.set(update.missionId, {
            ...mission,
            logs: newLogs,
            lastUpdate: new Date().toISOString(),
          });
        }
        
        return newMissions;
      });
    };

    // Register event listeners
    client.on('connected', handleConnected);
    client.on('disconnected', handleDisconnected);
    client.on('error', handleError);
    client.on('mission.update', handleMissionUpdate);
    client.on('task.update', handleTaskUpdate);
    client.on('log.update', handleLogUpdate);

    // Auto-connect if enabled
    if (autoConnect && !isConnected) {
      client.connect().catch(error => {
        console.error('Failed to connect to WebSocket:', error);
        setConnectionError(error.message);
      });
    }

    // Cleanup
    return () => {
      client.off('connected', handleConnected);
      client.off('disconnected', handleDisconnected);
      client.off('error', handleError);
      client.off('mission.update', handleMissionUpdate);
      client.off('task.update', handleTaskUpdate);
      client.off('log.update', handleLogUpdate);
    };
  }, [client, autoConnect, isConnected, maxLogs, updateMission]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      client.disconnect();
    };
  }, [client]);

  return {
    // Connection state
    isConnected,
    connectionError,
    
    // Mission management
    subscribeMission,
    unsubscribeMission,
    requestMissionStatus,
    
    // Mission data
    getMission,
    getAllMissions,
    missions: Array.from(missions.values()),
    
    // Utilities
    clearMission,
    clearAllMissions,
    
    // Direct client access (for advanced usage)
    client,
  };
}

// Hook for a single mission
export function useMissionUpdate(missionId: string, options: UseMissionUpdatesOptions = {}) {
  const {
    isConnected,
    connectionError,
    subscribeMission,
    unsubscribeMission,
    requestMissionStatus,
    getMission,
    clearMission,
    client,
  } = useMissionUpdates(options);

  const mission = getMission(missionId);

  useEffect(() => {
    if (missionId && isConnected) {
      subscribeMission(missionId);
      
      return () => {
        unsubscribeMission(missionId);
      };
    }
  }, [missionId, isConnected, subscribeMission, unsubscribeMission]);

  return {
    isConnected,
    connectionError,
    mission,
    requestStatus: () => requestMissionStatus(missionId),
    clearMission: () => clearMission(missionId),
    client,
  };
}