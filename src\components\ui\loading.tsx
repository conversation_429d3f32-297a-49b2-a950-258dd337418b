import * as React from 'react';
import { cn } from '@/lib/utils';
import { useReducedMotion } from '@/lib/ui/accessibility';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  color?: 'primary' | 'secondary' | 'muted';
}

export function LoadingSpinner({ 
  size = 'md', 
  className,
  color = 'primary'
}: LoadingSpinnerProps) {
  const prefersReducedMotion = useReducedMotion();
  
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12'
  };
  
  const colorClasses = {
    primary: 'border-primary',
    secondary: 'border-secondary',
    muted: 'border-muted-foreground'
  };
  
  return (
    <div
      className={cn(
        'rounded-full border-2 border-t-transparent',
        sizeClasses[size],
        colorClasses[color],
        !prefersReducedMotion && 'animate-spin',
        className
      )}
      role="status"
      aria-label="Loading"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
}

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

export function LoadingSkeleton({ 
  className,
  variant = 'rectangular',
  width,
  height,
  lines = 1
}: LoadingSkeletonProps) {
  const prefersReducedMotion = useReducedMotion();
  
  const baseClasses = cn(
    'bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%]',
    !prefersReducedMotion && 'animate-pulse',
    className
  );
  
  const variantClasses = {
    text: 'h-4 rounded',
    rectangular: 'rounded-md',
    circular: 'rounded-full'
  };
  
  const style: React.CSSProperties = {};
  if (width) style.width = width;
  if (height) style.height = height;
  
  if (variant === 'text' && lines > 1) {
    return (
      <div className="space-y-2" role="status" aria-label="Loading content">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              baseClasses,
              variantClasses[variant],
              index === lines - 1 && 'w-3/4' // Last line is shorter
            )}
            style={index === lines - 1 ? { ...style, width: '75%' } : style}
          />
        ))}
        <span className="sr-only">Loading text content...</span>
      </div>
    );
  }
  
  return (
    <div
      className={cn(baseClasses, variantClasses[variant])}
      style={style}
      role="status"
      aria-label="Loading content"
    >
      <span className="sr-only">Loading content...</span>
    </div>
  );
}

interface LoadingOverlayProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
  overlayClassName?: string;
  spinnerSize?: 'sm' | 'md' | 'lg' | 'xl';
}

export function LoadingOverlay({
  isLoading,
  children,
  loadingText = 'Loading...',
  className,
  overlayClassName,
  spinnerSize = 'lg'
}: LoadingOverlayProps) {
  return (
    <div className={cn('relative', className)}>
      {children}
      
      {isLoading && (
        <div
          className={cn(
            'absolute inset-0 flex flex-col items-center justify-center bg-background/80 backdrop-blur-sm z-50',
            overlayClassName
          )}
          role="status"
          aria-live="polite"
          aria-label={loadingText}
        >
          <LoadingSpinner size={spinnerSize} />
          <p className="mt-4 text-sm text-muted-foreground">
            {loadingText}
          </p>
        </div>
      )}
    </div>
  );
}

interface LoadingCardProps {
  className?: string;
  showAvatar?: boolean;
  lines?: number;
}

export function LoadingCard({ 
  className,
  showAvatar = false,
  lines = 3
}: LoadingCardProps) {
  return (
    <div 
      className={cn('p-4 space-y-3 border rounded-lg', className)}
      role="status"
      aria-label="Loading card content"
    >
      {showAvatar && (
        <div className="flex items-center space-x-3">
          <LoadingSkeleton variant="circular" width={40} height={40} />
          <div className="space-y-2 flex-1">
            <LoadingSkeleton variant="text" width="60%" />
            <LoadingSkeleton variant="text" width="40%" />
          </div>
        </div>
      )}
      
      <div className="space-y-2">
        <LoadingSkeleton variant="text" lines={lines} />
      </div>
      
      <div className="flex space-x-2">
        <LoadingSkeleton variant="rectangular" width={80} height={32} />
        <LoadingSkeleton variant="rectangular" width={60} height={32} />
      </div>
    </div>
  );
}

interface LoadingListProps {
  count?: number;
  className?: string;
  itemClassName?: string;
  showAvatar?: boolean;
}

export function LoadingList({ 
  count = 3,
  className,
  itemClassName,
  showAvatar = false
}: LoadingListProps) {
  return (
    <div 
      className={cn('space-y-3', className)}
      role="status"
      aria-label="Loading list content"
    >
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'flex items-center space-x-3 p-3 border rounded-lg',
            itemClassName
          )}
        >
          {showAvatar && (
            <LoadingSkeleton variant="circular" width={32} height={32} />
          )}
          <div className="flex-1 space-y-2">
            <LoadingSkeleton variant="text" width="80%" />
            <LoadingSkeleton variant="text" width="60%" />
          </div>
          <LoadingSkeleton variant="rectangular" width={60} height={24} />
        </div>
      ))}
    </div>
  );
}

interface ProgressiveLoadingProps {
  steps: Array<{
    id: string;
    label: string;
    completed: boolean;
    loading: boolean;
  }>;
  className?: string;
}

export function ProgressiveLoading({ steps, className }: ProgressiveLoadingProps) {
  const completedSteps = steps.filter(step => step.completed).length;
  const totalSteps = steps.length;
  const progress = (completedSteps / totalSteps) * 100;
  
  return (
    <div 
      className={cn('space-y-4', className)}
      role="status"
      aria-label="Loading progress"
    >
      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Progress</span>
          <span>{completedSteps}/{totalSteps}</span>
        </div>
        <div className="w-full bg-muted rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
            role="progressbar"
            aria-valuenow={progress}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label={`${progress.toFixed(0)}% complete`}
          />
        </div>
      </div>
      
      {/* Steps */}
      <div className="space-y-2">
        {steps.map((step, index) => (
          <div
            key={step.id}
            className="flex items-center space-x-3 text-sm"
          >
            <div className="flex-shrink-0">
              {step.completed ? (
                <div className="w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-primary-foreground" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              ) : step.loading ? (
                <LoadingSpinner size="sm" />
              ) : (
                <div className="w-5 h-5 bg-muted rounded-full" />
              )}
            </div>
            <span className={cn(
              step.completed && 'text-muted-foreground line-through',
              step.loading && 'font-medium'
            )}>
              {step.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}