import { eq, and, desc, asc, sql } from 'drizzle-orm';
import db, { schema } from './index';
import { 
  MISSION_STATUS, 
  TASK_STATUS, 
  PLAN_STATUS, 
  MissionStatus, 
  TaskStatus, 
  PlanStatus,
  DATABASE_CONFIG 
} from '../constants';
import { Mission, Plan, Task } from '../agent/types';

/**
 * Common database operation patterns to eliminate duplication
 */

// Mission Operations
export class MissionOperations {
  /**
   * Update mission status with timestamp
   */
  static async updateStatus(missionId: string, status: MissionStatus): Promise<void> {
    const updateData: any = { 
      status, 
      updatedAt: new Date() 
    };

    if (status === MISSION_STATUS.COMPLETED) {
      updateData.completedAt = new Date();
    }

    await db.update(schema.missions)
      .set(updateData)
      .where(eq(schema.missions.id, missionId));
  }

  /**
   * Get mission with related data
   */
  static async getWithRelations(missionId: string) {
    const mission = await db.query.missions.findFirst({
      where: eq(schema.missions.id, missionId),
      with: {
        plans: {
          orderBy: desc(schema.plans.version),
          with: {
            tasks: {
              orderBy: asc(schema.tasks.priority)
            }
          }
        },
        executionLogs: {
          orderBy: desc(schema.executionLogs.timestamp),
          limit: DATABASE_CONFIG.BATCH_SIZE
        },
        reflections: {
          orderBy: desc(schema.reflections.createdAt)
        }
      }
    });

    return mission;
  }

  /**
   * Get missions by status
   */
  static async getByStatus(status: MissionStatus, limit = DATABASE_CONFIG.BATCH_SIZE) {
    return await db.query.missions.findMany({
      where: eq(schema.missions.status, status),
      orderBy: desc(schema.missions.updatedAt),
      limit
    });
  }

  /**
   * Get active missions (planning or executing)
   */
  static async getActive() {
    return await db.query.missions.findMany({
      where: sql`${schema.missions.status} IN (${MISSION_STATUS.PLANNING}, ${MISSION_STATUS.EXECUTING})`,
      orderBy: desc(schema.missions.updatedAt)
    });
  }
}

// Plan Operations
export class PlanOperations {
  /**
   * Update plan status
   */
  static async updateStatus(planId: string, status: PlanStatus): Promise<void> {
    await db.update(schema.plans)
      .set({ 
        status, 
        updatedAt: new Date() 
      })
      .where(eq(schema.plans.id, planId));
  }

  /**
   * Create new plan version
   */
  static async createVersion(
    missionId: string, 
    planData: {
      title: string;
      description: string;
      estimatedDuration?: number;
    },
    previousVersion = 0
  ) {
    const [newPlan] = await db.insert(schema.plans).values({
      missionId,
      version: previousVersion + 1,
      title: planData.title,
      description: planData.description,
      estimatedDuration: planData.estimatedDuration,
      status: PLAN_STATUS.DRAFT,
    }).returning();

    return newPlan;
  }

  /**
   * Get latest plan for mission
   */
  static async getLatest(missionId: string) {
    return await db.query.plans.findFirst({
      where: eq(schema.plans.missionId, missionId),
      orderBy: desc(schema.plans.version),
      with: {
        tasks: {
          orderBy: asc(schema.tasks.priority)
        }
      }
    });
  }

  /**
   * Mark plan as superseded
   */
  static async markSuperseded(planId: string): Promise<void> {
    await this.updateStatus(planId, PLAN_STATUS.SUPERSEDED);
  }
}

// Task Operations
export class TaskOperations {
  /**
   * Update task status with timing
   */
  static async updateStatus(
    taskId: string, 
    status: TaskStatus, 
    result?: any
  ): Promise<void> {
    const updateData: any = { 
      status, 
      updatedAt: new Date() 
    };

    if (status === TASK_STATUS.IN_PROGRESS) {
      updateData.startedAt = new Date();
    } else if (status === TASK_STATUS.COMPLETED || status === TASK_STATUS.FAILED) {
      updateData.completedAt = new Date();
      
      // Calculate actual duration if started
      const task = await db.query.tasks.findFirst({
        where: eq(schema.tasks.id, taskId),
        columns: { startedAt: true }
      });
      
      if (task?.startedAt) {
        const duration = Date.now() - new Date(task.startedAt).getTime();
        updateData.actualDuration = Math.round(duration / 60000); // Convert to minutes
      }
    }

    if (result !== undefined) {
      updateData.result = result;
    }

    await db.update(schema.tasks)
      .set(updateData)
      .where(eq(schema.tasks.id, taskId));
  }

  /**
   * Get tasks by status
   */
  static async getByStatus(planId: string, status: TaskStatus) {
    return await db.query.tasks.findMany({
      where: and(
        eq(schema.tasks.planId, planId),
        eq(schema.tasks.status, status)
      ),
      orderBy: asc(schema.tasks.priority)
    });
  }

  /**
   * Get pending tasks for plan
   */
  static async getPending(planId: string) {
    return await this.getByStatus(planId, TASK_STATUS.PENDING);
  }

  /**
   * Get completed tasks for plan
   */
  static async getCompleted(planId: string) {
    return await this.getByStatus(planId, TASK_STATUS.COMPLETED);
  }

  /**
   * Get failed tasks for plan
   */
  static async getFailed(planId: string) {
    return await this.getByStatus(planId, TASK_STATUS.FAILED);
  }

  /**
   * Create multiple tasks in batch
   */
  static async createBatch(planId: string, tasksData: Array<{
    title: string;
    description: string;
    priority: number;
    toolName?: string;
    toolParams?: Record<string, any>;
    dependencies?: string[];
    estimatedDuration?: number;
  }>) {
    const tasks = tasksData.map(taskData => ({
      planId,
      title: taskData.title,
      description: taskData.description,
      priority: taskData.priority,
      toolName: taskData.toolName,
      toolParams: taskData.toolParams,
      dependencies: taskData.dependencies,
      estimatedDuration: taskData.estimatedDuration,
      status: TASK_STATUS.PENDING,
    }));

    return await db.insert(schema.tasks).values(tasks).returning();
  }
}

// Reflection Operations
export class ReflectionOperations {
  /**
   * Create reflection entry
   */
  static async create(data: {
    missionId?: string;
    planId?: string;
    taskId?: string;
    type: string;
    content: string;
    insights?: string[];
    recommendations?: string[];
    confidence?: number;
  }) {
    return await db.insert(schema.reflections).values(data).returning();
  }

  /**
   * Get reflections by type
   */
  static async getByType(missionId: string, type: string) {
    return await db.query.reflections.findMany({
      where: and(
        eq(schema.reflections.missionId, missionId),
        eq(schema.reflections.type, type)
      ),
      orderBy: desc(schema.reflections.createdAt)
    });
  }
}

// Tool Usage Operations
export class ToolUsageOperations {
  /**
   * Log tool usage
   */
  static async log(data: {
    taskId?: string;
    toolName: string;
    parameters: Record<string, any>;
    result: any;
    success: boolean;
    duration: number;
    errorMessage?: string;
  }) {
    return await db.insert(schema.toolUsage).values(data);
  }

  /**
   * Get tool usage statistics
   */
  static async getStats(toolName?: string) {
    const whereClause = toolName ? eq(schema.toolUsage.toolName, toolName) : undefined;
    
    return await db.select({
      toolName: schema.toolUsage.toolName,
      totalUsage: sql<number>`count(*)`,
      successRate: sql<number>`avg(case when success then 1.0 else 0.0 end)`,
      avgDuration: sql<number>`avg(duration)`
    })
    .from(schema.toolUsage)
    .where(whereClause)
    .groupBy(schema.toolUsage.toolName);
  }
}

// Batch Operations
export class BatchOperations {
  /**
   * Execute multiple operations in a transaction
   */
  static async transaction<T>(operations: (tx: typeof db) => Promise<T>): Promise<T> {
    return await db.transaction(operations);
  }

  /**
   * Cleanup old records
   */
  static async cleanup(retentionDays = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    // Clean up old execution logs
    await db.delete(schema.executionLogs)
      .where(sql`${schema.executionLogs.timestamp} < ${cutoffDate}`);

    // Clean up completed missions older than retention period
    await db.delete(schema.missions)
      .where(and(
        eq(schema.missions.status, MISSION_STATUS.COMPLETED),
        sql`${schema.missions.completedAt} < ${cutoffDate}`
      ));
  }
}
