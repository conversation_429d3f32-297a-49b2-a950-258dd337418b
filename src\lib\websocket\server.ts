import Fastify, { FastifyInstance } from 'fastify';
import fastifyWebsocket from '@fastify/websocket';
import fastifyCors from '@fastify/cors';
import { EventEmitter } from 'events';

export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  clientId?: string;
  missionId?: string;
}

export interface MissionUpdate {
  missionId: string;
  status: 'pending' | 'planning' | 'executing' | 'completed' | 'failed' | 'paused';
  progress?: {
    currentTask?: string;
    completedTasks: number;
    totalTasks: number;
    percentage: number;
  };
  message?: string;
  data?: any;
}

export interface TaskUpdate {
  missionId: string;
  taskId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  result?: any;
  error?: string;
  startTime?: string;
  endTime?: string;
}

export interface ReflectionUpdate {
  missionId: string;
  reflectionId: string;
  content: string;
  insights: string[];
  planAdjustments?: any;
}

export interface LogUpdate {
  missionId: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: string;
  context?: any;
}

/**
 * WebSocket server for real-time mission updates and agent communication
 */
export class WebSocketServer extends EventEmitter {
  private server: FastifyInstance;
  private clients: Map<string, any> = new Map();
  private missionSubscriptions: Map<string, Set<string>> = new Map();
  private port: number;
  private isRunning: boolean = false;

  constructor(port: number = 8080) {
    super();
    this.port = port;
    this.server = Fastify({
      logger: process.env.NODE_ENV !== 'production',
    });

    this.setupServer();
  }

  private async setupServer() {
    // Register CORS
    await this.server.register(fastifyCors, {
      origin: process.env.NODE_ENV === 'production' 
        ? ['https://your-domain.com'] 
        : ['http://localhost:3000', 'http://127.0.0.1:3000'],
      credentials: true,
    });

    // Register WebSocket support
    await this.server.register(fastifyWebsocket);

    // Health check endpoint
    this.server.get('/health', async (request, reply) => {
      return { status: 'ok', timestamp: new Date().toISOString() };
    });

    // WebSocket connection endpoint
    this.server.register(async (fastify) => {
      fastify.get('/ws', { websocket: true }, (connection, request) => {
        this.handleConnection(connection, request);
      });
    });

    // Mission-specific WebSocket endpoint
    this.server.register(async (fastify) => {
      fastify.get('/ws/mission/:missionId', { websocket: true }, (connection, request) => {
        const missionId = (request.params as any).missionId;
        this.handleMissionConnection(connection, request, missionId);
      });
    });

    // REST API for sending messages (for server-side broadcasting)
    this.server.post('/api/broadcast', async (request, reply) => {
      const { type, payload, missionId, clientId } = request.body as any;
      
      const message: WebSocketMessage = {
        type,
        payload,
        timestamp: new Date().toISOString(),
        missionId,
        clientId,
      };

      if (missionId) {
        this.broadcastToMission(missionId, message);
      } else if (clientId) {
        this.sendToClient(clientId, message);
      } else {
        this.broadcast(message);
      }

      return { success: true, message: 'Broadcast sent' };
    });
  }

  private handleConnection(connection: any, request: any) {
    const clientId = this.generateClientId();
    const clientInfo = {
      id: clientId,
      socket: connection.socket,
      connectedAt: new Date(),
      userAgent: request.headers['user-agent'],
      ip: request.ip,
    };

    this.clients.set(clientId, clientInfo);
    console.log(`Client connected: ${clientId}`);

    // Send welcome message
    this.sendToClient(clientId, {
      type: 'connection.established',
      payload: { clientId, serverTime: new Date().toISOString() },
      timestamp: new Date().toISOString(),
      clientId,
    });

    // Handle incoming messages
    connection.socket.on('message', (data: any) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleClientMessage(clientId, message);
      } catch (error) {
        console.error('Invalid message format:', error);
        this.sendToClient(clientId, {
          type: 'error',
          payload: { message: 'Invalid message format' },
          timestamp: new Date().toISOString(),
          clientId,
        });
      }
    });

    // Handle disconnection
    connection.socket.on('close', () => {
      this.handleDisconnection(clientId);
    });

    connection.socket.on('error', (error: any) => {
      console.error(`Client ${clientId} error:`, error);
      this.handleDisconnection(clientId);
    });
  }

  private handleMissionConnection(connection: any, request: any, missionId: string) {
    const clientId = this.generateClientId();
    const clientInfo = {
      id: clientId,
      socket: connection.socket,
      missionId,
      connectedAt: new Date(),
      userAgent: request.headers['user-agent'],
      ip: request.ip,
    };

    this.clients.set(clientId, clientInfo);
    this.subscribeMissionUpdates(clientId, missionId);

    console.log(`Client connected to mission ${missionId}: ${clientId}`);

    // Send mission-specific welcome
    this.sendToClient(clientId, {
      type: 'mission.connection.established',
      payload: { clientId, missionId, serverTime: new Date().toISOString() },
      timestamp: new Date().toISOString(),
      clientId,
      missionId,
    });

    // Handle messages and disconnection (same as general connection)
    connection.socket.on('message', (data: any) => {
      try {
        const message = JSON.parse(data.toString());
        message.missionId = missionId; // Ensure mission context
        this.handleClientMessage(clientId, message);
      } catch (error) {
        console.error('Invalid message format:', error);
      }
    });

    connection.socket.on('close', () => {
      this.handleDisconnection(clientId);
    });

    connection.socket.on('error', (error: any) => {
      console.error(`Mission client ${clientId} error:`, error);
      this.handleDisconnection(clientId);
    });
  }

  private handleClientMessage(clientId: string, message: any) {
    const client = this.clients.get(clientId);
    if (!client) return;

    console.log(`Message from ${clientId}:`, message.type);

    switch (message.type) {
      case 'ping':
        this.sendToClient(clientId, {
          type: 'pong',
          payload: { timestamp: new Date().toISOString() },
          timestamp: new Date().toISOString(),
          clientId,
        });
        break;

      case 'mission.subscribe':
        if (message.payload?.missionId) {
          this.subscribeMissionUpdates(clientId, message.payload.missionId);
        }
        break;

      case 'mission.unsubscribe':
        if (message.payload?.missionId) {
          this.unsubscribeMissionUpdates(clientId, message.payload.missionId);
        }
        break;

      case 'mission.status.request':
        // Client requesting current mission status
        this.emit('mission.status.request', {
          clientId,
          missionId: message.payload?.missionId,
        });
        break;

      default:
        // Emit the message for other parts of the application to handle
        this.emit('client.message', {
          clientId,
          message,
        });
    }
  }

  private handleDisconnection(clientId: string) {
    const client = this.clients.get(clientId);
    if (!client) return;

    console.log(`Client disconnected: ${clientId}`);

    // Remove from mission subscriptions
    for (const [missionId, subscribers] of this.missionSubscriptions.entries()) {
      subscribers.delete(clientId);
      if (subscribers.size === 0) {
        this.missionSubscriptions.delete(missionId);
      }
    }

    // Remove client
    this.clients.delete(clientId);

    this.emit('client.disconnected', { clientId, client });
  }

  // Public API for broadcasting updates

  public broadcastMissionUpdate(update: MissionUpdate) {
    const message: WebSocketMessage = {
      type: 'mission.update',
      payload: update,
      timestamp: new Date().toISOString(),
      missionId: update.missionId,
    };

    this.broadcastToMission(update.missionId, message);
  }

  public broadcastTaskUpdate(update: TaskUpdate) {
    const message: WebSocketMessage = {
      type: 'task.update',
      payload: update,
      timestamp: new Date().toISOString(),
      missionId: update.missionId,
    };

    this.broadcastToMission(update.missionId, message);
  }

  public broadcastReflectionUpdate(update: ReflectionUpdate) {
    const message: WebSocketMessage = {
      type: 'reflection.update',
      payload: update,
      timestamp: new Date().toISOString(),
      missionId: update.missionId,
    };

    this.broadcastToMission(update.missionId, message);
  }

  public broadcastLogUpdate(update: LogUpdate) {
    const message: WebSocketMessage = {
      type: 'log.update',
      payload: update,
      timestamp: new Date().toISOString(),
      missionId: update.missionId,
    };

    this.broadcastToMission(update.missionId, message);
  }

  // Utility methods

  private subscribeMissionUpdates(clientId: string, missionId: string) {
    if (!this.missionSubscriptions.has(missionId)) {
      this.missionSubscriptions.set(missionId, new Set());
    }
    this.missionSubscriptions.get(missionId)!.add(clientId);

    this.sendToClient(clientId, {
      type: 'mission.subscribed',
      payload: { missionId },
      timestamp: new Date().toISOString(),
      clientId,
      missionId,
    });
  }

  private unsubscribeMissionUpdates(clientId: string, missionId: string) {
    const subscribers = this.missionSubscriptions.get(missionId);
    if (subscribers) {
      subscribers.delete(clientId);
      if (subscribers.size === 0) {
        this.missionSubscriptions.delete(missionId);
      }
    }

    this.sendToClient(clientId, {
      type: 'mission.unsubscribed',
      payload: { missionId },
      timestamp: new Date().toISOString(),
      clientId,
      missionId,
    });
  }

  private broadcastToMission(missionId: string, message: WebSocketMessage) {
    const subscribers = this.missionSubscriptions.get(missionId);
    if (!subscribers) return;

    for (const clientId of subscribers) {
      this.sendToClient(clientId, message);
    }
  }

  private sendToClient(clientId: string, message: WebSocketMessage) {
    const client = this.clients.get(clientId);
    if (!client || client.socket.readyState !== 1) return; // 1 = OPEN

    try {
      client.socket.send(JSON.stringify(message));
    } catch (error) {
      console.error(`Failed to send message to client ${clientId}:`, error);
      this.handleDisconnection(clientId);
    }
  }

  private broadcast(message: WebSocketMessage) {
    for (const [clientId] of this.clients) {
      this.sendToClient(clientId, message);
    }
  }

  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Server lifecycle

  public async start(): Promise<void> {
    if (this.isRunning) return;

    try {
      await this.server.listen({ port: this.port, host: '0.0.0.0' });
      this.isRunning = true;
      console.log(`WebSocket server running on port ${this.port}`);
    } catch (error) {
      console.error('Failed to start WebSocket server:', error);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    if (!this.isRunning) return;

    try {
      await this.server.close();
      this.isRunning = false;
      console.log('WebSocket server stopped');
    } catch (error) {
      console.error('Error stopping WebSocket server:', error);
      throw error;
    }
  }

  public getStats() {
    return {
      isRunning: this.isRunning,
      connectedClients: this.clients.size,
      activeMissions: this.missionSubscriptions.size,
      port: this.port,
    };
  }
}

// Export singleton instance
let webSocketServer: WebSocketServer | null = null;

export function getWebSocketServer(port: number = 8080): WebSocketServer {
  if (!webSocketServer) {
    webSocketServer = new WebSocketServer(port);
  }
  return webSocketServer;
}

export function startWebSocketServer(port: number = 8080): Promise<WebSocketServer> {
  const server = getWebSocketServer(port);
  return server.start().then(() => server);
}