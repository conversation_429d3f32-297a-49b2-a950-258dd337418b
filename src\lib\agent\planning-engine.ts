import { PlanningCapability, Mission, Plan, Task, ExecutionContext, PlanningResult } from './types';
import { DeepSeekClient } from './deepseek';
import { MissionOperations, PlanOperations, TaskOperations } from '../db/operations';
import { PLAN_STATUS, TASK_STATUS, AGENT_CONFIG } from '../constants';
import { AgentLogger } from './logger';
import { KnowledgeBaseValidator, ValidationResult } from './knowledge-validator';

/**
 * Planning engine responsible for creating and updating execution plans
 */
export class PlanningEngine implements PlanningCapability {
  constructor(private deepseek: DeepSeekClient) {}

  /**
   * Create a comprehensive execution plan for a mission
   * ENFORCES KNOWLEDGE-BASE-FIRST PRINCIPLE
   */
  async createPlan(mission: Mission, context: ExecutionContext): Promise<PlanningResult> {
    const logger = context.logger;
    logger.info(`Creating knowledge-base-first plan for mission: ${mission.title}`);

    try {
      // Generate initial plan using AI with knowledge-first instructions
      const planData = await this.generateKnowledgeFirstPlan(mission);

      // Enforce knowledge-base-first task ordering
      const knowledgeFirstTasks = KnowledgeBaseValidator.generateKnowledgeFirstTasks(
        planData.tasks,
        mission.description
      );

      // Create plan in database
      const newPlan = await PlanOperations.createVersion(
        mission.id,
        {
          title: planData.title,
          description: planData.description,
          estimatedDuration: planData.estimatedDuration,
        }
      );

      // Create tasks in batch with enforced knowledge-first ordering
      const tasks = await TaskOperations.createBatch(
        newPlan.id,
        knowledgeFirstTasks.map((taskData: any, index: number) => ({
          title: taskData.title,
          description: taskData.description,
          priority: taskData.priority || index + 1,
          toolName: taskData.toolName,
          toolParams: taskData.toolParams,
          dependencies: taskData.dependencies,
          estimatedDuration: taskData.estimatedDuration,
        }))
      );

      const plan: Plan = {
        ...newPlan,
        tasks: tasks.map(task => ({
          ...task,
          createdAt: new Date(task.createdAt),
          updatedAt: new Date(task.updatedAt),
          startedAt: task.startedAt ? new Date(task.startedAt) : undefined,
          completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
        })),
        createdAt: new Date(newPlan.createdAt),
        updatedAt: new Date(newPlan.updatedAt),
      };

      // CRITICAL: Validate knowledge-base-first compliance
      const validation = KnowledgeBaseValidator.validatePlan(plan, context);
      if (!validation.isValid) {
        const errorMessage = `Plan validation failed: ${validation.issues.map(i => i.message).join('; ')}`;
        logger.error(errorMessage);
        throw new Error(`Knowledge-base-first validation failed: ${errorMessage}`);
      }

      // Activate the plan only after validation
      await PlanOperations.updateStatus(newPlan.id, PLAN_STATUS.ACTIVE);

      logger.info(`Knowledge-base-first plan created successfully with ${tasks.length} tasks`);
      logger.info(`First task: ${tasks[0]?.title} (${tasks[0]?.toolName})`);

      return {
        plan,
        reasoning: `${planData.reasoning}\n\nKNOWLEDGE-BASE-FIRST ENFORCEMENT: This plan ensures all reasoning and responses are based solely on information retrieved from the knowledge base.`,
        confidence: planData.confidence,
      };

    } catch (error) {
      logger.error(`Failed to create plan: ${error}`, undefined, error as Error);
      throw error;
    }
  }

  /**
   * Update an existing plan based on feedback or new information
   */
  async updatePlan(plan: Plan, feedback: string, context: ExecutionContext): Promise<PlanningResult> {
    const logger = context.logger;
    logger.info(`Updating plan: ${plan.title}`);

    try {
      // Generate updated plan using AI
      const updatePrompt = this.buildUpdatePrompt(plan, feedback);
      const updatedData = await this.generatePlanFromPrompt(updatePrompt);
      
      // Create new plan version
      const newPlan = await PlanOperations.createVersion(
        plan.missionId,
        {
          title: updatedData.title,
          description: updatedData.description,
          estimatedDuration: updatedData.estimatedDuration,
        },
        plan.version
      );

      // Create updated tasks
      const tasks = await TaskOperations.createBatch(
        newPlan.id,
        updatedData.tasks.map((taskData: any, index: number) => ({
          title: taskData.title,
          description: taskData.description,
          priority: taskData.priority || index + 1,
          toolName: taskData.toolName,
          toolParams: taskData.toolParams,
          dependencies: taskData.dependencies,
          estimatedDuration: taskData.estimatedDuration,
        }))
      );

      // Mark old plan as superseded
      await PlanOperations.markSuperseded(plan.id);

      // Activate new plan
      await PlanOperations.updateStatus(newPlan.id, PLAN_STATUS.ACTIVE);

      const updatedPlan: Plan = {
        ...newPlan,
        tasks: tasks.map(task => ({
          ...task,
          createdAt: new Date(task.createdAt),
          updatedAt: new Date(task.updatedAt),
          startedAt: task.startedAt ? new Date(task.startedAt) : undefined,
          completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
        })),
        createdAt: new Date(newPlan.createdAt),
        updatedAt: new Date(newPlan.updatedAt),
      };

      logger.info(`Plan updated successfully to version ${newPlan.version}`);

      return {
        plan: updatedPlan,
        reasoning: updatedData.reasoning,
        confidence: updatedData.confidence,
      };

    } catch (error) {
      logger.error(`Failed to update plan: ${error}`, undefined, error as Error);
      throw error;
    }
  }

  /**
   * Decompose a complex task into smaller subtasks
   */
  async decomposeTasks(task: Task, context: ExecutionContext): Promise<Task[]> {
    const logger = context.logger;
    logger.info(`Decomposing task: ${task.title}`);

    try {
      const decompositionPrompt = this.buildDecompositionPrompt(task);
      const decompositionData = await this.generatePlanFromPrompt(decompositionPrompt);

      // Create subtasks
      const subtasks = await TaskOperations.createBatch(
        task.planId,
        decompositionData.tasks.map((subtaskData: any, index: number) => ({
          title: subtaskData.title,
          description: subtaskData.description,
          priority: task.priority + (index + 1) * 0.1, // Maintain relative priority
          toolName: subtaskData.toolName,
          toolParams: subtaskData.toolParams,
          dependencies: [task.id, ...(subtaskData.dependencies || [])],
          estimatedDuration: subtaskData.estimatedDuration,
        }))
      );

      logger.info(`Task decomposed into ${subtasks.length} subtasks`);

      return subtasks.map(subtask => ({
        ...subtask,
        createdAt: new Date(subtask.createdAt),
        updatedAt: new Date(subtask.updatedAt),
        startedAt: subtask.startedAt ? new Date(subtask.startedAt) : undefined,
        completedAt: subtask.completedAt ? new Date(subtask.completedAt) : undefined,
      }));

    } catch (error) {
      logger.error(`Failed to decompose task: ${error}`, undefined, error as Error);
      throw error;
    }
  }

  /**
   * Generate knowledge-first plan data using AI with strict instructions
   */
  private async generateKnowledgeFirstPlan(mission: Mission): Promise<any> {
    const knowledgeFirstPrompt = `
CRITICAL REQUIREMENT: This plan MUST follow the knowledge-base-first principle.

Mission: ${mission.title}
Description: ${mission.description}
Priority: ${mission.priority}

MANDATORY PLANNING RULES:
1. The FIRST task must ALWAYS be a knowledge base query using 'knowledge_base_query' tool
2. NO reasoning, analysis, or response generation can occur without prior knowledge retrieval
3. If the mission involves incidents, include an 'incident_query' task after the initial knowledge query
4. All subsequent tasks must depend on knowledge retrieval tasks
5. If no relevant information is found in the knowledge base, the response must be "I don't have enough information in the knowledge base to answer this question"

Available Knowledge Tools (MUST use these first):
- knowledge_base_query: Query the vector database for relevant documents
- incident_query: Query incident data with specialized filters
- confluence_search: Search Confluence content

Example Knowledge-First Task Sequence:
1. Query Knowledge Base (knowledge_base_query)
2. Query Specific Data if needed (incident_query, confluence_search)
3. Analyze Retrieved Data (only if data was found)
4. Generate Response (only based on retrieved data)

Generate a plan that strictly enforces this knowledge-base-first approach.
`;

    const planResponse = await this.deepseek.generatePlan(knowledgeFirstPrompt);
    return JSON.parse(planResponse);
  }

  /**
   * Generate plan data using AI (legacy method)
   */
  private async generatePlanData(mission: Mission): Promise<any> {
    const prompt = `${mission.title}\n\n${mission.description}`;
    const context = `Priority: ${mission.priority}`;

    const planResponse = await this.deepseek.generatePlan(prompt, context);
    return JSON.parse(planResponse);
  }

  /**
   * Generate plan from custom prompt
   */
  private async generatePlanFromPrompt(prompt: string): Promise<any> {
    const planResponse = await this.deepseek.generatePlan(prompt);
    return JSON.parse(planResponse);
  }

  /**
   * Build update prompt for plan revision
   */
  private buildUpdatePrompt(plan: Plan, feedback: string): string {
    return `Update this plan based on feedback:

Original Plan: ${JSON.stringify({
  title: plan.title,
  description: plan.description,
  tasks: plan.tasks.map(t => ({
    title: t.title,
    description: t.description,
    status: t.status,
    priority: t.priority
  }))
}, null, 2)}

Feedback: ${feedback}

Please provide an updated plan that addresses the feedback while maintaining the core objectives.`;
  }

  /**
   * Build decomposition prompt for task breakdown
   */
  private buildDecompositionPrompt(task: Task): string {
    return `Decompose this task into smaller, manageable subtasks:

Task: ${JSON.stringify({
  title: task.title,
  description: task.description,
  toolName: task.toolName,
  toolParams: task.toolParams
}, null, 2)}

Please break this down into 2-5 smaller tasks that can be executed independently.`;
  }

  /**
   * Validate plan structure and constraints
   */
  private validatePlan(planData: any): boolean {
    // Basic validation
    if (!planData.title || !planData.description || !Array.isArray(planData.tasks)) {
      return false;
    }

    // Validate tasks
    for (const task of planData.tasks) {
      if (!task.title || !task.description) {
        return false;
      }
    }

    // Check estimated duration is reasonable
    if (planData.estimatedDuration && planData.estimatedDuration > AGENT_CONFIG.EXECUTION_TIMEOUT / 60000) {
      return false;
    }

    return true;
  }
}
