import {
  ServiceManagementAgent,
  AutonomousCapability,
  ReflectiveCapability,
  GoalOrientedCapability,
  Mission,
  Plan,
  Task,
  ExecutionContext,
  ExecutionResult,
  ReflectionResult,
  ToolResult,
  AgentConfig,
} from './types';
import { DeepSeekClient } from './deepseek';
import { ToolRegistry } from './tools/base';
import { ConfluenceLoaderTool, ConfluenceSearchTool } from './tools/confluence';
import { KnowledgeBaseQueryTool, IncidentQueryTool } from './tools/knowledge-base';
import { KnowledgeBasedReasoningTool, ResponseGenerationTool } from './tools/reasoning';
import { PlanningEngine } from './planning-engine';
import { ToolUseEngine } from './tool-engine';
import { AgentLogger, createMissionLogger } from './logger';
import { MissionOperations, PlanOperations, TaskOperations, ReflectionOperations } from '../db/operations';
import db, { schema } from '../db';
import { eq, and } from 'drizzle-orm';
import {
  MISSION_STATUS,
  TASK_STATUS,
  PLAN_STATUS,
  CACHE_CONFIG,
  AGENT_CONFIG,
  REFLECTION_TYPES
} from '../constants';
import { KnowledgeBaseValidator, KnowledgeAuditLogger } from './knowledge-validator';
import NodeCache from 'node-cache';

// Re-export the logger for backward compatibility
export { AgentLogger } from './logger';

// Remove old PlanningEngine class - now imported from separate module



// Remove old ToolUseEngine class - now imported from separate module

export class AutonomousEngine implements AutonomousCapability {
  constructor(
    private toolUse: ToolUseEngine,
    private deepseek: DeepSeekClient
  ) {}

  async executeTask(task: Task, context: ExecutionContext, previousResults: ToolResult[] = []): Promise<ToolResult> {
    context.logger.info(`Executing task: ${task.title}`);

    try {
      // KNOWLEDGE-BASE-FIRST VALIDATION
      const validation = KnowledgeBaseValidator.validateTaskExecution(task, context, previousResults);
      if (!validation.isValid) {
        const errorMessage = `Task validation failed: ${validation.issues.map(i => i.message).join('; ')}`;
        context.logger.error(errorMessage);
        KnowledgeAuditLogger.logValidationFailure(validation, context);
        throw new Error(errorMessage);
      }

      // Update task status to in_progress
      await TaskOperations.updateStatus(task.id, TASK_STATUS.IN_PROGRESS);

      // Select appropriate tool
      const tool = await this.toolUse.selectTool(task, context);
      if (!tool) {
        throw new Error(`No suitable tool found for task: ${task.title}`);
      }

      // Execute the tool
      const result = await this.toolUse.executeTool(
        tool,
        task.toolParams || {},
        { ...context, taskId: task.id }
      );

      // VALIDATE KNOWLEDGE RETRIEVAL RESULTS
      if (tool.name === 'knowledge_base_query' || tool.name === 'incident_query') {
        const knowledgeValidation = KnowledgeBaseValidator.validateKnowledgeResult(result, context);
        if (!knowledgeValidation.isValid) {
          context.logger.warn(`Knowledge retrieval validation failed: ${knowledgeValidation.issues.map(i => i.message).join('; ')}`);
          // Don't fail the task, but log the issue
          KnowledgeAuditLogger.logValidationFailure(knowledgeValidation, context);
        } else if (result.success && result.data) {
          // Log successful knowledge retrieval
          KnowledgeAuditLogger.logKnowledgeQuery(
            task.toolParams?.query || 'Unknown query',
            result.data.results || result.data.incidents || [],
            context
          );
        }
      }

      // Update task status based on result
      const status = result.success ? TASK_STATUS.COMPLETED : TASK_STATUS.FAILED;
      await TaskOperations.updateStatus(task.id, status, result.data);

      return result;

    } catch (error) {
      context.logger.error(`Task execution failed: ${error}`, undefined, error as Error);

      await TaskOperations.updateStatus(
        task.id,
        TASK_STATUS.FAILED,
        { error: (error as Error).message }
      );

      throw error;
    }
  }

  async executePlan(plan: Plan, context: ExecutionContext): Promise<ExecutionResult> {
    context.logger.info(`Executing knowledge-base-first plan: ${plan.title}`);

    // CRITICAL: VALIDATE KNOWLEDGE-BASE-FIRST COMPLIANCE
    const planValidation = KnowledgeBaseValidator.validatePlan(plan, context);
    if (!planValidation.isValid) {
      const errorMessage = `Plan execution blocked - Knowledge-base-first validation failed: ${planValidation.issues.map(i => i.message).join('; ')}`;
      context.logger.error(errorMessage);
      KnowledgeAuditLogger.logValidationFailure(planValidation, context);
      throw new Error(errorMessage);
    }

    context.logger.info('✓ Plan validated: Knowledge-base-first principle enforced');

    const completedTasks: Task[] = [];
    const failedTasks: Task[] = [];
    const reflections: any[] = [];
    let hasKnowledgeContext = false;

    // Initialize WebSocket server for task updates
    let wsServer: any = null;
    try {
      const { getWebSocketServer } = await import('@/lib/websocket/server');
      wsServer = getWebSocketServer();
    } catch (error) {
      context.logger.warn('WebSocket server not available for task updates');
    }

    const broadcastTaskUpdate = (task: Task, status: string, result?: any, error?: string) => {
      if (wsServer) {
        wsServer.broadcastTaskUpdate({
          missionId: context.missionId,
          taskId: task.id,
          status,
          result,
          error,
          startTime: status === 'in_progress' ? new Date().toISOString() : undefined,
          endTime: ['completed', 'failed'].includes(status) ? new Date().toISOString() : undefined,
        });
      }
    };

    try {
      // Update plan status to active
      await PlanOperations.updateStatus(plan.id, PLAN_STATUS.ACTIVE);

      // Execute tasks in priority order, respecting dependencies
      const sortedTasks = this.sortTasksByPriorityAndDependencies(plan.tasks);
      const totalTasks = sortedTasks.length;

      for (let i = 0; i < sortedTasks.length; i++) {
        const task = sortedTasks[i];
        
        try {
          context.logger.info(`Starting task ${i + 1}/${totalTasks}: ${task.title}`);
          
          // Broadcast task start
          broadcastTaskUpdate(task, 'in_progress');

          // Update mission progress
          if (wsServer) {
            wsServer.broadcastMissionUpdate({
              missionId: context.missionId,
              status: 'executing',
              progress: {
                currentTask: task.title,
                completedTasks: completedTasks.length,
                totalTasks,
                percentage: Math.round(((completedTasks.length) / totalTasks) * 100),
              },
              message: `Executing: ${task.title}`,
            });
          }

          // Check if dependencies are completed
          if (task.dependencies && task.dependencies.length > 0) {
            const dependencyStatuses = await db.select()
              .from(schema.tasks)
              .where(and(
                eq(schema.tasks.planId, plan.id),
                // Note: In production, you'd use a proper IN clause
              ));

            const incompleteDeps = task.dependencies.filter(depId =>
              !dependencyStatuses.find(t => t.id === depId && t.status === 'completed')
            );

            if (incompleteDeps.length > 0) {
              context.logger.warn(`Skipping task ${task.title} due to incomplete dependencies`);
              broadcastTaskUpdate(task, 'pending', null, 'Waiting for dependencies');
              continue;
            }
          }

          const result = await this.executeTask(task, context);
          
          if (result.success) {
            completedTasks.push(task);
            broadcastTaskUpdate(task, 'completed', result.data);
            context.logger.info(`Task completed: ${task.title}`);
          } else {
            failedTasks.push(task);
            broadcastTaskUpdate(task, 'failed', null, result.error || 'Task execution failed');
            context.logger.error(`Task failed: ${task.title}`, result.error);
          }

        } catch (error) {
          context.logger.error(`Task failed: ${task.title}`, error);
          failedTasks.push(task);
          broadcastTaskUpdate(task, 'failed', null, (error as Error).message);
        }
      }

      // Update plan status
      const finalStatus = failedTasks.length === 0 ? 'completed' : 'failed';
      await db.update(schema.plans)
        .set({ status: finalStatus })
        .where(eq(schema.plans.id, plan.id));

      context.logger.info(`Plan execution ${finalStatus}: ${completedTasks.length}/${totalTasks} tasks completed`);

      return {
        success: failedTasks.length === 0,
        completedTasks,
        failedTasks,
        reflections,
      };

    } catch (error) {
      context.logger.error(`Plan execution failed: ${error}`);

      await db.update(schema.plans)
        .set({ status: 'failed' })
        .where(eq(schema.plans.id, plan.id));

      return {
        success: false,
        completedTasks,
        failedTasks,
        reflections,
        error: (error as Error).message,
      };
    }
  }

  async handleError(error: Error, context: ExecutionContext): Promise<void> {
    context.logger.error(`Handling error: ${error.message}`);

    // Store error analysis
    await db.insert(schema.reflections).values({
      missionId: context.missionId,
      planId: context.planId,
      taskId: context.taskId,
      type: 'error_analysis',
      content: error.message,
      insights: [`Error occurred: ${error.message}`],
      recommendations: ['Review task parameters', 'Check tool availability'],
      confidence: 0.8,
    });
  }

  private sortTasksByPriorityAndDependencies(tasks: Task[]): Task[] {
    // Simple topological sort with priority consideration
    const sorted: Task[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const visit = (task: Task) => {
      if (visiting.has(task.id)) {
        throw new Error(`Circular dependency detected involving task: ${task.title}`);
      }
      if (visited.has(task.id)) return;

      visiting.add(task.id);

      // Visit dependencies first
      if (task.dependencies) {
        for (const depId of task.dependencies) {
          const depTask = tasks.find(t => t.id === depId);
          if (depTask) {
            visit(depTask);
          }
        }
      }

      visiting.delete(task.id);
      visited.add(task.id);
      sorted.push(task);
    };

    // Sort by priority first, then visit
    const prioritySorted = [...tasks].sort((a, b) => a.priority - b.priority);
    for (const task of prioritySorted) {
      visit(task);
    }

    return sorted;
  }
}

export class ReflectiveEngine implements ReflectiveCapability {
  constructor(private deepseek: DeepSeekClient) {}

  async assessProgress(plan: Plan, context: ExecutionContext): Promise<ReflectionResult> {
    context.logger.info(`Assessing progress for plan: ${plan.title}`);

    try {
      // Get current task statuses
      const tasks = await db.select().from(schema.tasks)
        .where(eq(schema.tasks.planId, plan.id));

      const completedTasks = tasks.filter(t => t.status === 'completed');
      const failedTasks = tasks.filter(t => t.status === 'failed');
      const inProgressTasks = tasks.filter(t => t.status === 'in_progress');

      const progressData = {
        plan,
        totalTasks: tasks.length,
        completedTasks: completedTasks.length,
        failedTasks: failedTasks.length,
        inProgressTasks: inProgressTasks.length,
        progressPercentage: tasks.length > 0 ? (completedTasks.length / tasks.length) * 100 : 0,
      };

      const reflectionResponse = await this.deepseek.reflect('progress_assessment', progressData);
      const reflectionData = JSON.parse(reflectionResponse);

      // Store reflection
      await db.insert(schema.reflections).values({
        missionId: context.missionId,
        planId: context.planId,
        type: 'progress_assessment',
        content: reflectionData.reasoning,
        insights: reflectionData.insights,
        recommendations: reflectionData.recommendations,
        confidence: reflectionData.confidence,
      });

      return {
        insights: reflectionData.insights,
        recommendations: reflectionData.recommendations,
        confidence: reflectionData.confidence,
      };

    } catch (error) {
      context.logger.error(`Failed to assess progress: ${error}`);
      throw error;
    }
  }

  async analyzeFailure(task: Task, error: string, context: ExecutionContext): Promise<ReflectionResult> {
    context.logger.info(`Analyzing failure for task: ${task.title}`);

    try {
      const failureData = { task, error };
      const reflectionResponse = await this.deepseek.reflect('error_analysis', failureData);
      const reflectionData = JSON.parse(reflectionResponse);

      // Store reflection
      await db.insert(schema.reflections).values({
        missionId: context.missionId,
        planId: context.planId,
        taskId: task.id,
        type: 'error_analysis',
        content: reflectionData.reasoning,
        insights: reflectionData.insights,
        recommendations: reflectionData.recommendations,
        confidence: reflectionData.confidence,
      });

      return {
        insights: reflectionData.insights,
        recommendations: reflectionData.recommendations,
        confidence: reflectionData.confidence,
      };

    } catch (error) {
      context.logger.error(`Failed to analyze failure: ${error}`);
      throw error;
    }
  }

  async optimizePlan(plan: Plan, context: ExecutionContext): Promise<ReflectionResult> {
    context.logger.info(`Optimizing plan: ${plan.title}`);

    try {
      // Get execution history and performance data
      const tasks = await db.select().from(schema.tasks)
        .where(eq(schema.tasks.planId, plan.id));

      const toolUsage = await db.select().from(schema.toolUsage)
        .where(eq(schema.toolUsage.taskId, tasks[0]?.id || ''));

      const optimizationData = { plan, tasks, toolUsage };
      const reflectionResponse = await this.deepseek.reflect('plan_optimization', optimizationData);
      const reflectionData = JSON.parse(reflectionResponse);

      // Store reflection
      await db.insert(schema.reflections).values({
        missionId: context.missionId,
        planId: context.planId,
        type: 'plan_optimization',
        content: reflectionData.reasoning,
        insights: reflectionData.insights,
        recommendations: reflectionData.recommendations,
        confidence: reflectionData.confidence,
      });

      return {
        insights: reflectionData.insights,
        recommendations: reflectionData.recommendations,
        confidence: reflectionData.confidence,
      };

    } catch (error) {
      context.logger.error(`Failed to optimize plan: ${error}`);
      throw error;
    }
  }
}

export class GoalOrientedEngine implements GoalOrientedCapability {
  constructor(private deepseek: DeepSeekClient) {}

  async trackProgress(mission: Mission, context: ExecutionContext): Promise<number> {
    context.logger.info(`Tracking progress for mission: ${mission.title}`);

    try {
      // Get all plans for this mission
      const plans = await db.select().from(schema.plans)
        .where(eq(schema.plans.missionId, mission.id));

      if (plans.length === 0) return 0;

      // Get all tasks for all plans
      const allTasks = await db.select().from(schema.tasks)
        .where(eq(schema.tasks.planId, plans[0].id)); // Simplified for demo

      const completedTasks = allTasks.filter(t => t.status === 'completed');
      const progress = allTasks.length > 0 ? completedTasks.length / allTasks.length : 0;

      return Math.min(progress, 1.0);

    } catch (error) {
      context.logger.error(`Failed to track progress: ${error}`);
      return 0;
    }
  }

  async validateCompletion(mission: Mission, result: any, context: ExecutionContext): Promise<boolean> {
    context.logger.info(`Validating completion for mission: ${mission.title}`);

    try {
      const assessmentResponse = await this.deepseek.assessCompletion(mission, result);
      const assessmentData = JSON.parse(assessmentResponse);

      return assessmentData.completed && assessmentData.completionPercentage >= 0.9;

    } catch (error) {
      context.logger.error(`Failed to validate completion: ${error}`);
      return false;
    }
  }

  async adjustStrategy(mission: Mission, plan: Plan, context: ExecutionContext): Promise<Plan> {
    context.logger.info(`Adjusting strategy for mission: ${mission.title}`);

    try {
      // Get current progress and performance data
      const progress = await this.trackProgress(mission, context);

      if (progress < 0.5) {
        // If progress is low, consider plan adjustments
        const adjustmentData = { mission, plan, progress };
        const adjustmentResponse = await this.deepseek.generatePlan(
          `Adjust this plan to improve progress:\n\n${JSON.stringify(adjustmentData, null, 2)}`
        );

        const adjustmentPlan = JSON.parse(adjustmentResponse);

        // Create new plan version
        const [newPlan] = await db.insert(schema.plans).values({
          missionId: mission.id,
          version: plan.version + 1,
          title: adjustmentPlan.title,
          description: adjustmentPlan.description,
          estimatedDuration: adjustmentPlan.estimatedDuration,
          status: 'draft',
        }).returning();

        // Mark old plan as superseded
        await db.update(schema.plans)
          .set({ status: 'superseded' })
          .where(eq(schema.plans.id, plan.id));

        return {
          ...newPlan,
          tasks: [], // Tasks would be created separately
          createdAt: new Date(newPlan.createdAt),
          updatedAt: new Date(newPlan.updatedAt),
        };
      }

      return plan; // No adjustment needed

    } catch (error) {
      context.logger.error(`Failed to adjust strategy: ${error}`);
      return plan;
    }
  }
}

export class ServiceManagementAgentImpl implements ServiceManagementAgent {
  public planning: PlanningEngine;
  public toolUse: ToolUseEngine;
  public autonomous: AutonomousCapability;
  public reflective: ReflectiveCapability;
  public goalOriented: GoalOrientedCapability;

  private deepseek: DeepSeekClient;
  private toolRegistry: ToolRegistry;
  private cache: NodeCache;

  constructor(config: AgentConfig) {
    this.deepseek = new DeepSeekClient(config);
    this.toolRegistry = new ToolRegistry();
    this.cache = new NodeCache({ stdTTL: CACHE_CONFIG.DEFAULT_TTL });

    // Initialize capabilities with new modular engines
    this.planning = new PlanningEngine(this.deepseek);
    this.toolUse = new ToolUseEngine(this.deepseek, this.toolRegistry);
    this.autonomous = new AutonomousEngine(this.toolUse, this.deepseek);
    this.reflective = new ReflectiveEngine(this.deepseek);
    this.goalOriented = new GoalOrientedEngine(this.deepseek);

    // Register default tools
    this.registerDefaultTools();
  }

  private registerDefaultTools() {
    // Register Knowledge Base tools (PRIORITY TOOLS - must be registered first)
    this.toolRegistry.register(new KnowledgeBaseQueryTool(this.deepseek));
    this.toolRegistry.register(new IncidentQueryTool(this.deepseek));

    // Register Knowledge-Based Reasoning tools (ONLY use after knowledge retrieval)
    this.toolRegistry.register(new KnowledgeBasedReasoningTool(this.deepseek));
    this.toolRegistry.register(new ResponseGenerationTool(this.deepseek));

    // Register Confluence tools
    this.toolRegistry.register(new ConfluenceLoaderTool(this.deepseek));
    this.toolRegistry.register(new ConfluenceSearchTool(this.deepseek));
  }

  async executeMission(mission: Mission): Promise<ExecutionResult> {
    const logger = new AgentLogger({ missionId: mission.id });
    logger.info(`Starting mission execution: ${mission.title}`);

    // Initialize WebSocket server for real-time updates
    let wsServer: any = null;
    try {
      const { getWebSocketServer } = await import('@/lib/websocket/server');
      wsServer = getWebSocketServer();
    } catch (error) {
      logger.warn('WebSocket server not available, continuing without real-time updates');
    }

    const broadcastMissionUpdate = (status: string, progress?: any, message?: string) => {
      if (wsServer) {
        wsServer.broadcastMissionUpdate({
          missionId: mission.id,
          status,
          progress,
          message,
        });
      }
    };

    try {
      // Update mission status to planning
      await db.update(schema.missions)
        .set({ status: 'planning' })
        .where(eq(schema.missions.id, mission.id));

      broadcastMissionUpdate('planning', { percentage: 10 }, 'Creating execution plan...');

      // Create execution context
      const context: ExecutionContext = {
        missionId: mission.id,
        planId: '',
        tools: new Map(this.toolRegistry.getAll().map(tool => [tool.name, tool])),
        cache: this.cache,
        logger,
      };

      // Create initial plan
      logger.info('Creating execution plan...');
      const planningResult = await this.planning.createPlan(mission, context);
      context.planId = planningResult.plan.id;

      broadcastMissionUpdate('planning', { percentage: 25 }, `Plan created with ${planningResult.plan.tasks.length} tasks`);

      // Update mission status to executing
      await MissionOperations.updateStatus(mission.id, MISSION_STATUS.EXECUTING);

      broadcastMissionUpdate('executing', { 
        percentage: 30,
        currentTask: 'Starting task execution...',
        completedTasks: 0,
        totalTasks: planningResult.plan.tasks.length,
      }, 'Beginning task execution');

      // Execute the plan with progress tracking
      logger.info('Starting plan execution...');
      const executionResult = await this.autonomous.executePlan(planningResult.plan, context);

      broadcastMissionUpdate('executing', { 
        percentage: 80,
        currentTask: 'Validating completion...',
        completedTasks: executionResult.completedTasks.length,
        totalTasks: planningResult.plan.tasks.length,
      }, 'Validating mission completion');

      // Assess final completion
      const isCompleted = await this.goalOriented.validateCompletion(
        mission,
        executionResult,
        context
      );

      // Update mission status
      const finalStatus = isCompleted ? MISSION_STATUS.COMPLETED : MISSION_STATUS.FAILED;
      await MissionOperations.updateStatus(mission.id, finalStatus);

      broadcastMissionUpdate(finalStatus, { 
        percentage: 100,
        completedTasks: executionResult.completedTasks.length,
        totalTasks: planningResult.plan.tasks.length,
      }, isCompleted ? 'Mission completed successfully' : 'Mission failed to complete');

      logger.info(`Mission ${isCompleted ? 'completed' : 'failed'}: ${mission.title}`);

      return {
        ...executionResult,
        success: isCompleted,
      };

    } catch (error) {
      logger.error(`Mission execution failed: ${error}`, undefined, error as Error);

      await MissionOperations.updateStatus(mission.id, MISSION_STATUS.FAILED);

      broadcastMissionUpdate('failed', { percentage: 0 }, `Mission failed: ${(error as Error).message}`);

      return {
        success: false,
        completedTasks: [],
        failedTasks: [],
        reflections: [],
        error: (error as Error).message,
      };
    }
  }

  async pauseMission(missionId: string): Promise<void> {
    // Implementation for pausing mission
    await MissionOperations.updateStatus(missionId, MISSION_STATUS.PAUSED);
  }

  async resumeMission(missionId: string): Promise<void> {
    // Implementation for resuming mission
    const missionData = await MissionOperations.getWithRelations(missionId);

    if (missionData) {
      await this.executeMission({
        ...missionData,
        createdAt: new Date(missionData.createdAt),
        updatedAt: new Date(missionData.updatedAt),
        completedAt: missionData.completedAt ? new Date(missionData.completedAt) : undefined,
      });
    }
  }

  async cancelMission(missionId: string): Promise<void> {
    // Implementation for canceling mission
    await MissionOperations.updateStatus(missionId, MISSION_STATUS.FAILED);
  }
}
