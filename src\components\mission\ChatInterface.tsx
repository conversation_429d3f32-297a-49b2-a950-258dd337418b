"use client"

import { FormEvent } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Send, User, Bo<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChatInput, ChatMessageList } from "@/components/ui/chat-interface"
import { cn } from "@/lib/utils"
import { Message } from "./types"

interface ChatInterfaceProps {
  messages: Message[];
  input: string;
  isLoading: boolean;
  selectedMissionId: number | null;
  onInputChange: (value: string) => void;
  onSubmit: (e: FormEvent) => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
}

export function ChatInterface({
  messages,
  input,
  isLoading,
  selectedMissionId,
  onInputChange,
  onSubmit,
  onKeyDown
}: ChatInterfaceProps) {
  return (
    <div className="flex flex-col h-full">
      {messages.length === 0 && !isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
              <Bot size={32} className="text-primary/60" />
            </div>
            <h2 className="text-xl font-medium text-foreground mb-2">How can I help you today?</h2>
            <p className="text-sm text-muted-foreground">
              {selectedMissionId ? "Continue working on your mission or ask for help." : "Start a conversation to begin your mission."}
            </p>
          </div>
        </div>
      ) : (
        <ChatMessageList>
          {messages.map((message) => (
            <div
              key={message.id}
              className="w-full flex justify-center px-4 md:px-8 lg:px-12"
            >
              <div
                className={cn(
                  "flex gap-3 w-full max-w-4xl",
                  message.sender === "user" ? "flex-row-reverse" : ""
                )}
              >
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
                  {message.sender === "user" ? (
                    <User className="h-4 w-4" />
                  ) : (
                    <Bot className="h-4 w-4" />
                  )}
                </div>
                <div
                  className={cn(
                    "rounded-lg px-4 py-3 flex-1 max-w-[75%]",
                    message.sender === "user"
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted"
                  )}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-70 mt-1">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </p>
                </div>
              </div>
            </div>
          ))}

          {isLoading && (
            <div className="w-full flex justify-center px-4 md:px-8 lg:px-12">
              <div className="flex gap-3 w-full max-w-4xl">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
                  <Bot className="h-4 w-4" />
                </div>
                <div className="rounded-lg px-4 py-3 bg-muted">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                    <div className="w-2 h-2 bg-current rounded-full animate-pulse delay-100" />
                    <div className="w-2 h-2 bg-current rounded-full animate-pulse delay-200" />
                  </div>
                </div>
              </div>
            </div>
          )}
        </ChatMessageList>
      )}

      {/* Input Form */}
      <div className="p-4 bg-background">
        <form
          onSubmit={onSubmit}
          className="relative rounded-xl border border-border/50 bg-background focus-within:border-border focus-within:shadow-sm transition-all duration-200 max-w-4xl mx-auto"
        >
          <ChatInput
            value={input}
            onChange={(e) => onInputChange(e.target.value)}
            onKeyDown={onKeyDown}
            placeholder="Describe your mission or ask for help..."
            minRows={1}
            maxRows={6}
            className="resize-none rounded-xl bg-transparent border-0 p-4 shadow-none focus-visible:ring-0 focus-visible:outline-none"
          />
          <div className="flex items-center px-4 pb-3 justify-between">
            <div className="flex">
              <Button
                variant="ghost"
                size="icon"
                type="button"
                className="h-8 w-8 hover:bg-muted hover:text-muted-foreground"
              >
                <Paperclip className="size-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                type="button"
                className="h-8 w-8 hover:bg-muted hover:text-muted-foreground"
              >
                <Mic className="size-4" />
              </Button>
            </div>
            <Button
              type="submit"
              size="icon"
              className="ml-auto h-8 w-8 rounded-lg"
              disabled={!input.trim() || isLoading}
              title="Send Mission"
            >
              <Send className="size-4" />
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}