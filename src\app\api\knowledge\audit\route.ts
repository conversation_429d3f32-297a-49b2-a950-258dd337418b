import { NextRequest, NextResponse } from 'next/server';
import { KnowledgeBaseAuditSystem } from '@/lib/agent/audit/knowledge-audit';

/**
 * GET /api/knowledge/audit - Get knowledge base audit information
 * Query parameters:
 * - missionId: Get audit trail for specific mission
 * - type: 'trail' | 'compliance' | 'metrics'
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const missionId = searchParams.get('missionId');
    const type = searchParams.get('type') || 'trail';

    const auditSystem = KnowledgeBaseAuditSystem.getInstance();

    switch (type) {
      case 'trail':
        if (!missionId) {
          return NextResponse.json(
            { success: false, error: 'missionId is required for audit trail' },
            { status: 400 }
          );
        }
        
        const auditTrail = auditSystem.getAuditTrail(missionId);
        return NextResponse.json({
          success: true,
          data: {
            missionId,
            auditTrail,
            totalEntries: auditTrail.length
          }
        });

      case 'compliance':
        if (!missionId) {
          return NextResponse.json(
            { success: false, error: 'missionId is required for compliance report' },
            { status: 400 }
          );
        }
        
        const complianceReport = await auditSystem.generateComplianceReport(missionId);
        return NextResponse.json({
          success: true,
          data: complianceReport
        });

      case 'metrics':
        const systemMetrics = auditSystem.getSystemMetrics();
        return NextResponse.json({
          success: true,
          data: systemMetrics
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid type. Use: trail, compliance, or metrics' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Knowledge audit API error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve audit information' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/knowledge/audit/validate - Validate knowledge-base-first compliance for a mission
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { missionId, planData } = body;

    if (!missionId) {
      return NextResponse.json(
        { success: false, error: 'missionId is required' },
        { status: 400 }
      );
    }

    const auditSystem = KnowledgeBaseAuditSystem.getInstance();
    
    // Generate compliance report
    const complianceReport = await auditSystem.generateComplianceReport(missionId);
    
    // Get audit trail
    const auditTrail = auditSystem.getAuditTrail(missionId);
    
    // Analyze compliance
    const analysis = {
      isCompliant: complianceReport.isCompliant,
      complianceScore: calculateComplianceScore(complianceReport),
      criticalIssues: complianceReport.details.violations.filter(v => v.type === 'reasoning_violation'),
      warnings: complianceReport.details.violations.filter(v => v.type === 'validation_failure'),
      knowledgeCoverage: {
        totalQueries: complianceReport.summary.totalKnowledgeQueries,
        successfulQueries: complianceReport.summary.successfulQueries,
        successRate: complianceReport.summary.querySuccessRate
      },
      recommendations: complianceReport.recommendations
    };

    return NextResponse.json({
      success: true,
      data: {
        missionId,
        compliance: complianceReport,
        analysis,
        auditTrail: auditTrail.slice(-10) // Last 10 entries
      }
    });

  } catch (error) {
    console.error('Knowledge audit validation error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to validate compliance' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/knowledge/audit - Clear audit logs (admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    // In a real implementation, you'd check admin permissions here
    const { searchParams } = new URL(request.url);
    const confirm = searchParams.get('confirm');

    if (confirm !== 'true') {
      return NextResponse.json(
        { success: false, error: 'Must confirm deletion with ?confirm=true' },
        { status: 400 }
      );
    }

    const auditSystem = KnowledgeBaseAuditSystem.getInstance();
    auditSystem.clearAuditLogs();

    return NextResponse.json({
      success: true,
      message: 'Audit logs cleared successfully'
    });

  } catch (error) {
    console.error('Knowledge audit clear error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to clear audit logs' },
      { status: 500 }
    );
  }
}

/**
 * Calculate compliance score based on various factors
 */
function calculateComplianceScore(report: any): number {
  let score = 100;

  // Deduct points for violations
  score -= report.summary.reasoningViolations * 25; // Critical violations
  score -= report.summary.validationFailures * 10; // Validation failures

  // Deduct points for low knowledge retrieval success rate
  if (report.summary.querySuccessRate < 50) {
    score -= 20;
  } else if (report.summary.querySuccessRate < 75) {
    score -= 10;
  }

  // Deduct points if no knowledge queries at all
  if (report.summary.totalKnowledgeQueries === 0) {
    score -= 50;
  }

  return Math.max(0, score);
}
