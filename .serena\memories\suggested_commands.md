# Suggested Commands

## Development
```bash
# Start development server with Turbopack
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Lint code
npm run lint
```

## Database Operations
```bash
# Initialize database (run once)
npm run db:init

# Generate new migration
npm run db:generate

# Run pending migrations  
npm run db:migrate

# Open Drizzle Studio (database GUI)
npm run db:studio
```

## Windows-Specific Commands
```bash
# Directory listing
dir
ls  # if using Git Bash

# Change directory
cd <path>

# Find files
findstr /s /i "pattern" *.ts
grep -r "pattern" src/  # if using Git Bash

# File operations
copy <source> <dest>
move <source> <dest>
del <file>

# Process management
tasklist | findstr node
taskkill /F /PID <pid>
```

## Git Operations
```bash
# Check status
git status

# Add changes
git add .

# Commit changes
git commit -m "message"

# Push changes
git push

# Check logs
git log --oneline -10
```

## Package Management
```bash
# Install dependencies
npm install

# Add new dependency
npm install <package>

# Add dev dependency
npm install -D <package>

# Check for updates
npm outdated

# Update packages
npm update
```