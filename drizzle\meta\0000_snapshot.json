{"version": "6", "dialect": "sqlite", "id": "1b76a839-ed9a-43a1-9e97-e3f963cd0adb", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"execution_logs": {"name": "execution_logs", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "mission_id": {"name": "mission_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "plan_id": {"name": "plan_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "level": {"name": "level", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'info'"}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"execution_logs_mission_id_missions_id_fk": {"name": "execution_logs_mission_id_missions_id_fk", "tableFrom": "execution_logs", "tableTo": "missions", "columnsFrom": ["mission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "execution_logs_plan_id_plans_id_fk": {"name": "execution_logs_plan_id_plans_id_fk", "tableFrom": "execution_logs", "tableTo": "plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "execution_logs_task_id_tasks_id_fk": {"name": "execution_logs_task_id_tasks_id_fk", "tableFrom": "execution_logs", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "knowledge_base": {"name": "knowledge_base", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "source_type": {"name": "source_type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "embedding": {"name": "embedding", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_indexed": {"name": "last_indexed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "missions": {"name": "missions", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'medium'"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "completed_at": {"name": "completed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "plans": {"name": "plans", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "mission_id": {"name": "mission_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "version": {"name": "version", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'draft'"}, "estimated_duration": {"name": "estimated_duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "actual_duration": {"name": "actual_duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"plans_mission_id_missions_id_fk": {"name": "plans_mission_id_missions_id_fk", "tableFrom": "plans", "tableTo": "missions", "columnsFrom": ["mission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "reflections": {"name": "reflections", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "mission_id": {"name": "mission_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "plan_id": {"name": "plan_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "insights": {"name": "insights", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "recommendations": {"name": "recommendations", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "confidence": {"name": "confidence", "type": "real", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"reflections_mission_id_missions_id_fk": {"name": "reflections_mission_id_missions_id_fk", "tableFrom": "reflections", "tableTo": "missions", "columnsFrom": ["mission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reflections_plan_id_plans_id_fk": {"name": "reflections_plan_id_plans_id_fk", "tableFrom": "reflections", "tableTo": "plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reflections_task_id_tasks_id_fk": {"name": "reflections_task_id_tasks_id_fk", "tableFrom": "reflections", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tasks": {"name": "tasks", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "plan_id": {"name": "plan_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "parent_task_id": {"name": "parent_task_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "tool_name": {"name": "tool_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_params": {"name": "tool_params", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "dependencies": {"name": "dependencies", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "estimated_duration": {"name": "estimated_duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "actual_duration": {"name": "actual_duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "started_at": {"name": "started_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "completed_at": {"name": "completed_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"tasks_plan_id_plans_id_fk": {"name": "tasks_plan_id_plans_id_fk", "tableFrom": "tasks", "tableTo": "plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tasks_parent_task_id_tasks_id_fk": {"name": "tasks_parent_task_id_tasks_id_fk", "tableFrom": "tasks", "tableTo": "tasks", "columnsFrom": ["parent_task_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tool_usage": {"name": "tool_usage", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "task_id": {"name": "task_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tool_name": {"name": "tool_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "parameters": {"name": "parameters", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "success": {"name": "success", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"tool_usage_task_id_tasks_id_fk": {"name": "tool_usage_task_id_tasks_id_fk", "tableFrom": "tool_usage", "tableTo": "tasks", "columnsFrom": ["task_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}