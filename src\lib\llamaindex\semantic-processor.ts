import { Document } from 'llamaindex';
import { DeepSeekClient } from '@/lib/agent/deepseek';
import { cacheManager } from '@/lib/cache/cache-manager';
import { SemanticChunker, SemanticChunkingConfig, SemanticChunk } from './semantic-chunker';
import db from '@/lib/db';
import * as schema from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

/**
 * Enhanced Document Processor with Semantic Chunking
 * Replaces fixed-length chunking with intelligent content-aware chunking
 */
export class SemanticDocumentProcessor {
  private deepseek: DeepSeekClient;
  private semanticChunker: SemanticChunker;
  private chunkingConfig: SemanticChunkingConfig;

  constructor(
    deepseekApiKey: string,
    chunkingConfig: Partial<SemanticChunkingConfig> = {}
  ) {
    this.deepseek = new DeepSeekClient({
      deepseekApiKey,
      deepseekBaseUrl: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
    });

    this.chunkingConfig = {
      strategy: 'hybrid',
      maxChunkSize: 1000,
      minChunkSize: 100,
      overlapSize: 50,
      overlapPercentage: 10,
      preserveStructure: true,
      respectSentenceBoundaries: true,
      respectParagraphBoundaries: true,
      useHeadingHierarchy: true,
      enableMetadataFiltering: true,
      ...chunkingConfig
    };

    this.semanticChunker = new SemanticChunker(this.chunkingConfig);
  }

  /**
   * Process document with semantic chunking
   */
  async processDocument(
    content: string,
    metadata: DocumentMetadata,
    options: ProcessingOptions = {}
  ): Promise<ProcessedDocument> {
    try {
      // Generate document summary and concepts
      const [summary, concepts] = await Promise.all([
        this.generateSummary(content),
        this.extractConcepts(content)
      ]);

      // Enhanced metadata
      const enhancedMetadata: DocumentMetadata = {
        ...metadata,
        summary,
        concepts,
        lastIndexed: new Date()
      };

      // Semantic chunking
      const chunks = await this.semanticChunker.chunkDocument(content, {
        documentId: metadata.title,
        sourceType: metadata.sourceType,
        sourceUrl: metadata.sourceUrl,
        tags: metadata.tags || []
      });

      // Convert to DocumentChunk format
      const documentChunks: DocumentChunk[] = chunks.map((chunk, index) => ({
        ...chunk,
        id: `${metadata.title}_chunk_${index}`,
        metadata: {
          ...chunk.metadata,
          documentTitle: metadata.title,
          sourceType: metadata.sourceType,
          sourceUrl: metadata.sourceUrl,
          chunkIndex: index,
          totalChunks: chunks.length
        }
      }));

      const processedDoc: ProcessedDocument = {
        id: metadata.title,
        content,
        metadata: enhancedMetadata,
        chunks: documentChunks
      };

      // Store in database if enabled
      if (options.storeInDatabase !== false) {
        await this.storeDocument(processedDoc);
      }

      // Cache the processed document
      await cacheManager.set(
        `processed_doc_${metadata.title}`,
        processedDoc,
        3600 // 1 hour cache
      );

      return processedDoc;

    } catch (error) {
      console.error('Error processing document:', error);
      throw new Error(`Failed to process document: ${error}`);
    }
  }

  /**
   * Search documents using semantic chunking
   */
  async searchDocuments(
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    const {
      limit = 10,
      threshold = 0.7,
      sourceType,
      useChunks = true,
      collections = []
    } = options;

    try {
      // Check cache first
      const cacheKey = `search_${query}_${JSON.stringify(options)}`;
      const cachedResults = await cacheManager.get<SearchResult[]>(cacheKey);
      if (cachedResults) {
        return cachedResults;
      }

      let results: SearchResult[] = [];

      if (useChunks) {
        // Search using semantic chunks for better precision
        results = await this.searchSemanticChunks(query, {
          limit,
          threshold,
          sourceType
        });
      } else {
        // Search full documents
        results = await this.searchFullDocuments(query, {
          limit,
          threshold,
          sourceType
        });
      }

      // Filter by relevance score
      results = results.filter(result => result.score >= threshold);

      // Sort by relevance score
      results.sort((a, b) => b.score - a.score);

      // Limit results
      results = results.slice(0, limit);

      // Cache results
      await cacheManager.set(cacheKey, results, 1800); // 30 minutes cache

      return results;

    } catch (error) {
      console.error('Error searching documents:', error);
      throw new Error(`Search failed: ${error}`);
    }
  }

  /**
   * Search semantic chunks for precise results
   */
  private async searchSemanticChunks(
    query: string,
    options: { limit: number; threshold: number; sourceType?: string }
  ): Promise<SearchResult[]> {
    // Get all documents from database
    let dbQuery = db.select().from(schema.documents);
    
    if (options.sourceType) {
      dbQuery = dbQuery.where(eq(schema.documents.sourceType, options.sourceType));
    }

    const documents = await dbQuery;
    const results: SearchResult[] = [];

    for (const doc of documents) {
      try {
        // Get processed document from cache or reprocess
        let processedDoc = await cacheManager.get<ProcessedDocument>(`processed_doc_${doc.title}`);
        
        if (!processedDoc) {
          processedDoc = await this.processDocument(
            doc.content || '',
            {
              title: doc.title,
              sourceUrl: doc.sourceUrl || '',
              sourceType: doc.sourceType as any,
              tags: doc.tags ? JSON.parse(doc.tags) : [],
              lastIndexed: doc.lastIndexed
            },
            { storeInDatabase: false }
          );
        }

        // Search chunks using semantic similarity
        for (const chunk of processedDoc.chunks) {
          const similarity = await this.calculateSemanticSimilarity(query, chunk.content);
          
          if (similarity >= options.threshold) {
            results.push({
              document: processedDoc,
              chunk,
              score: similarity,
              snippet: this.generateSnippet(chunk.content, query)
            });
          }
        }

      } catch (error) {
        console.error(`Error processing document ${doc.title}:`, error);
        continue;
      }
    }

    return results;
  }

  /**
   * Search full documents
   */
  private async searchFullDocuments(
    query: string,
    options: { limit: number; threshold: number; sourceType?: string }
  ): Promise<SearchResult[]> {
    // Implementation for full document search
    // This would use the existing vector store or database search
    return [];
  }

  /**
   * Calculate semantic similarity between query and content
   */
  private async calculateSemanticSimilarity(query: string, content: string): Promise<number> {
    try {
      // Use DeepSeek or other embedding model for similarity calculation
      // For now, use simple text similarity as fallback
      return this.calculateTextSimilarity(query, content);
    } catch (error) {
      console.error('Error calculating semantic similarity:', error);
      return 0;
    }
  }

  /**
   * Simple text similarity calculation (fallback)
   */
  private calculateTextSimilarity(query: string, content: string): number {
    const queryWords = query.toLowerCase().split(/\s+/);
    const contentWords = content.toLowerCase().split(/\s+/);
    
    const querySet = new Set(queryWords);
    const contentSet = new Set(contentWords);
    
    const intersection = new Set([...querySet].filter(word => contentSet.has(word)));
    const union = new Set([...querySet, ...contentSet]);
    
    return intersection.size / union.size;
  }

  /**
   * Generate snippet from content highlighting query terms
   */
  private generateSnippet(content: string, query: string, maxLength: number = 200): string {
    const queryWords = query.toLowerCase().split(/\s+/);
    const sentences = content.split(/[.!?]+/);
    
    // Find sentence with most query words
    let bestSentence = '';
    let maxMatches = 0;
    
    for (const sentence of sentences) {
      const sentenceLower = sentence.toLowerCase();
      const matches = queryWords.filter(word => sentenceLower.includes(word)).length;
      
      if (matches > maxMatches) {
        maxMatches = matches;
        bestSentence = sentence.trim();
      }
    }
    
    // Truncate if too long
    if (bestSentence.length > maxLength) {
      bestSentence = bestSentence.substring(0, maxLength) + '...';
    }
    
    return bestSentence || content.substring(0, maxLength) + '...';
  }

  /**
   * Generate document summary
   */
  private async generateSummary(content: string): Promise<string> {
    try {
      const response = await this.deepseek.chat([
        {
          role: 'system',
          content: 'Generate a concise summary (2-3 sentences) of the following document content.'
        },
        {
          role: 'user',
          content: content.substring(0, 2000) // Limit content for summary
        }
      ], { temperature: 0.3, maxTokens: 150 });

      return response.choices[0].message.content.trim();
    } catch (error) {
      console.error('Error generating summary:', error);
      return 'Summary generation failed';
    }
  }

  /**
   * Extract key concepts from content
   */
  private async extractConcepts(content: string): Promise<string[]> {
    try {
      const response = await this.deepseek.chat([
        {
          role: 'system',
          content: 'Extract 5-10 key concepts or topics from the following content. Return as a JSON array of strings.'
        },
        {
          role: 'user',
          content: content.substring(0, 2000)
        }
      ], { temperature: 0.2, maxTokens: 200 });

      const concepts = JSON.parse(response.choices[0].message.content);
      return Array.isArray(concepts) ? concepts : [];
    } catch (error) {
      console.error('Error extracting concepts:', error);
      return [];
    }
  }

  /**
   * Store processed document in database
   */
  private async storeDocument(doc: ProcessedDocument): Promise<void> {
    try {
      await db.insert(schema.documents).values({
        title: doc.id,
        content: doc.content,
        sourceUrl: doc.metadata.sourceUrl,
        sourceType: doc.metadata.sourceType,
        tags: JSON.stringify(doc.metadata.tags || []),
        lastIndexed: doc.metadata.lastIndexed,
        summary: doc.metadata.summary,
        concepts: JSON.stringify(doc.metadata.concepts || [])
      }).onConflictDoUpdate({
        target: schema.documents.title,
        set: {
          content: doc.content,
          sourceUrl: doc.metadata.sourceUrl,
          sourceType: doc.metadata.sourceType,
          tags: JSON.stringify(doc.metadata.tags || []),
          lastIndexed: doc.metadata.lastIndexed,
          summary: doc.metadata.summary,
          concepts: JSON.stringify(doc.metadata.concepts || [])
        }
      });
    } catch (error) {
      console.error('Error storing document:', error);
      throw error;
    }
  }

  /**
   * Update chunking configuration
   */
  updateChunkingConfig(config: Partial<SemanticChunkingConfig>): void {
    this.chunkingConfig = { ...this.chunkingConfig, ...config };
    this.semanticChunker = new SemanticChunker(this.chunkingConfig);
  }

  /**
   * Get current chunking configuration
   */
  getChunkingConfig(): SemanticChunkingConfig {
    return { ...this.chunkingConfig };
  }
}

// Type definitions
export interface DocumentMetadata {
  title: string;
  sourceUrl: string;
  sourceType: 'confluence' | 'file' | 'web';
  tags?: string[];
  lastIndexed: Date;
  summary?: string;
  concepts?: string[];
}

export interface ProcessedDocument {
  id: string;
  content: string;
  metadata: DocumentMetadata;
  chunks: DocumentChunk[];
}

export interface DocumentChunk extends SemanticChunk {
  // Extends SemanticChunk with document-specific metadata
}

export interface SearchResult {
  document: ProcessedDocument;
  chunk: DocumentChunk;
  score: number;
  snippet: string;
}

export interface SearchOptions {
  limit?: number;
  threshold?: number;
  sourceType?: string;
  useChunks?: boolean;
  collections?: string[];
}

export interface ProcessingOptions {
  storeInDatabase?: boolean;
  generateSummary?: boolean;
  extractConcepts?: boolean;
}
