import { Document } from 'llamaindex';

/**
 * Semantic Chunking Configuration
 */
export interface SemanticChunkingConfig {
  strategy: 'semantic' | 'hybrid' | 'fixed';
  maxChunkSize: number;
  minChunkSize: number;
  overlapSize: number;
  overlapPercentage: number;
  preserveStructure: boolean;
  respectSentenceBoundaries: boolean;
  respectParagraphBoundaries: boolean;
  useHeadingHierarchy: boolean;
  enableMetadataFiltering: boolean;
}

/**
 * Chunk with enhanced metadata and relevance scoring
 */
export interface SemanticChunk {
  id: string;
  content: string;
  startIndex: number;
  endIndex: number;
  metadata: {
    chunkType: 'heading' | 'paragraph' | 'list' | 'table' | 'code' | 'mixed';
    headingLevel?: number;
    headingText?: string;
    parentHeading?: string;
    structuralDepth: number;
    wordCount: number;
    sentenceCount: number;
    hasCode: boolean;
    hasTables: boolean;
    hasLists: boolean;
    documentSection?: string;
    relevanceScore?: number;
    semanticDensity: number;
    [key: string]: any;
  };
  overlap?: {
    previousChunk?: string;
    nextChunk?: string;
    overlapSize: number;
  };
}

/**
 * Advanced Semantic Chunking Engine
 * Implements intelligent content-aware chunking with structure preservation
 */
export class SemanticChunker {
  private config: SemanticChunkingConfig;

  constructor(config: Partial<SemanticChunkingConfig> = {}) {
    this.config = {
      strategy: 'semantic',
      maxChunkSize: 1000,
      minChunkSize: 100,
      overlapSize: 50,
      overlapPercentage: 10,
      preserveStructure: true,
      respectSentenceBoundaries: true,
      respectParagraphBoundaries: true,
      useHeadingHierarchy: true,
      enableMetadataFiltering: true,
      ...config
    };
  }

  /**
   * Main chunking method - routes to appropriate strategy
   */
  async chunkDocument(
    content: string,
    metadata: Record<string, any> = {}
  ): Promise<SemanticChunk[]> {
    switch (this.config.strategy) {
      case 'semantic':
        return this.semanticChunking(content, metadata);
      case 'hybrid':
        return this.hybridChunking(content, metadata);
      case 'fixed':
        return this.fixedSizeChunking(content, metadata);
      default:
        throw new Error(`Unknown chunking strategy: ${this.config.strategy}`);
    }
  }

  /**
   * Semantic chunking based on content structure
   */
  private async semanticChunking(
    content: string,
    metadata: Record<string, any>
  ): Promise<SemanticChunk[]> {
    const chunks: SemanticChunk[] = [];
    
    // Parse document structure
    const structure = this.parseDocumentStructure(content);
    
    // Process each structural element
    for (const element of structure.elements) {
      const elementChunks = await this.chunkStructuralElement(element, metadata);
      chunks.push(...elementChunks);
    }

    // Add overlaps between chunks
    this.addChunkOverlaps(chunks, content);

    // Calculate relevance scores
    this.calculateRelevanceScores(chunks);

    return chunks;
  }

  /**
   * Hybrid chunking: semantic + fixed-size fallback
   */
  private async hybridChunking(
    content: string,
    metadata: Record<string, any>
  ): Promise<SemanticChunk[]> {
    // First try semantic chunking
    const semanticChunks = await this.semanticChunking(content, metadata);
    
    // Check if any chunks are too large
    const refinedChunks: SemanticChunk[] = [];
    
    for (const chunk of semanticChunks) {
      if (chunk.content.length <= this.config.maxChunkSize) {
        refinedChunks.push(chunk);
      } else {
        // Split large semantic chunks using fixed-size strategy
        const subChunks = this.splitLargeChunk(chunk);
        refinedChunks.push(...subChunks);
      }
    }

    return refinedChunks;
  }

  /**
   * Fixed-size chunking with sentence boundary respect
   */
  private fixedSizeChunking(
    content: string,
    metadata: Record<string, any>
  ): Promise<SemanticChunk[]> {
    const chunks: SemanticChunk[] = [];
    const sentences = this.splitIntoSentences(content);
    
    let currentChunk = '';
    let currentIndex = 0;
    let chunkStartIndex = 0;

    for (const sentence of sentences) {
      const potentialChunk = currentChunk + (currentChunk ? ' ' : '') + sentence;
      
      if (potentialChunk.length > this.config.maxChunkSize && currentChunk.length > 0) {
        // Create chunk from current content
        chunks.push(this.createChunk(
          currentChunk,
          chunkStartIndex,
          currentIndex,
          'paragraph',
          metadata
        ));
        
        // Start new chunk
        currentChunk = sentence;
        chunkStartIndex = currentIndex;
      } else {
        currentChunk = potentialChunk;
      }
      
      currentIndex += sentence.length + 1;
    }

    // Add final chunk
    if (currentChunk.length > 0) {
      chunks.push(this.createChunk(
        currentChunk,
        chunkStartIndex,
        currentIndex,
        'paragraph',
        metadata
      ));
    }

    return Promise.resolve(chunks);
  }

  /**
   * Parse document structure to identify headings, paragraphs, lists, etc.
   */
  private parseDocumentStructure(content: string): DocumentStructure {
    const lines = content.split('\n');
    const elements: StructuralElement[] = [];
    
    let currentElement: StructuralElement | null = null;
    let lineIndex = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();
      
      if (!trimmedLine) {
        lineIndex += line.length + 1;
        continue;
      }

      const elementType = this.identifyLineType(trimmedLine);
      
      if (elementType === 'heading') {
        // Finish current element
        if (currentElement) {
          elements.push(currentElement);
        }
        
        // Start new heading element
        currentElement = {
          type: 'heading',
          content: trimmedLine,
          startIndex: lineIndex,
          endIndex: lineIndex + line.length,
          metadata: {
            headingLevel: this.getHeadingLevel(trimmedLine),
            headingText: this.extractHeadingText(trimmedLine)
          }
        };
      } else {
        if (!currentElement || currentElement.type === 'heading') {
          // Start new content element
          if (currentElement) {
            elements.push(currentElement);
          }
          
          currentElement = {
            type: elementType,
            content: trimmedLine,
            startIndex: lineIndex,
            endIndex: lineIndex + line.length,
            metadata: {}
          };
        } else {
          // Continue current element
          currentElement.content += '\n' + trimmedLine;
          currentElement.endIndex = lineIndex + line.length;
        }
      }
      
      lineIndex += line.length + 1;
    }

    // Add final element
    if (currentElement) {
      elements.push(currentElement);
    }

    return { elements };
  }

  /**
   * Identify the type of a line (heading, paragraph, list, etc.)
   */
  private identifyLineType(line: string): StructuralElementType {
    // Markdown headings
    if (/^#{1,6}\s/.test(line)) {
      return 'heading';
    }
    
    // List items
    if (/^[-*+]\s/.test(line) || /^\d+\.\s/.test(line)) {
      return 'list';
    }
    
    // Code blocks
    if (/^```/.test(line) || /^    /.test(line)) {
      return 'code';
    }
    
    // Tables (simple detection)
    if (/\|.*\|/.test(line)) {
      return 'table';
    }
    
    return 'paragraph';
  }

  /**
   * Get heading level from markdown heading
   */
  private getHeadingLevel(line: string): number {
    const match = line.match(/^(#{1,6})/);
    return match ? match[1].length : 0;
  }

  /**
   * Extract heading text without markdown syntax
   */
  private extractHeadingText(line: string): string {
    return line.replace(/^#{1,6}\s*/, '').trim();
  }

  /**
   * Chunk a structural element while respecting its boundaries
   */
  private async chunkStructuralElement(
    element: StructuralElement,
    metadata: Record<string, any>
  ): Promise<SemanticChunk[]> {
    const chunks: SemanticChunk[] = [];
    
    if (element.content.length <= this.config.maxChunkSize) {
      // Element fits in one chunk
      chunks.push(this.createChunk(
        element.content,
        element.startIndex,
        element.endIndex,
        element.type,
        { ...metadata, ...element.metadata }
      ));
    } else {
      // Split element while preserving structure
      const subChunks = this.splitStructuralElement(element, metadata);
      chunks.push(...subChunks);
    }

    return chunks;
  }

  /**
   * Split a large structural element into smaller chunks
   */
  private splitStructuralElement(
    element: StructuralElement,
    metadata: Record<string, any>
  ): SemanticChunk[] {
    const chunks: SemanticChunk[] = [];
    
    if (element.type === 'paragraph') {
      // Split by sentences
      const sentences = this.splitIntoSentences(element.content);
      let currentChunk = '';
      let currentStart = element.startIndex;
      
      for (const sentence of sentences) {
        const potentialChunk = currentChunk + (currentChunk ? ' ' : '') + sentence;
        
        if (potentialChunk.length > this.config.maxChunkSize && currentChunk.length > 0) {
          chunks.push(this.createChunk(
            currentChunk,
            currentStart,
            currentStart + currentChunk.length,
            'paragraph',
            { ...metadata, ...element.metadata }
          ));
          
          currentChunk = sentence;
          currentStart += currentChunk.length + 1;
        } else {
          currentChunk = potentialChunk;
        }
      }
      
      if (currentChunk.length > 0) {
        chunks.push(this.createChunk(
          currentChunk,
          currentStart,
          currentStart + currentChunk.length,
          'paragraph',
          { ...metadata, ...element.metadata }
        ));
      }
    } else {
      // For other types, use fixed-size splitting as fallback
      const fixedChunks = this.splitByFixedSize(element.content, element.startIndex);
      chunks.push(...fixedChunks.map(chunk => ({
        ...chunk,
        metadata: {
          ...chunk.metadata,
          chunkType: element.type,
          ...element.metadata
        }
      })));
    }

    return chunks;
  }

  /**
   * Split content by fixed size with sentence boundary respect
   */
  private splitByFixedSize(content: string, startIndex: number): SemanticChunk[] {
    const chunks: SemanticChunk[] = [];
    const sentences = this.splitIntoSentences(content);
    
    let currentChunk = '';
    let currentStart = startIndex;
    let chunkIndex = 0;

    for (const sentence of sentences) {
      const potentialChunk = currentChunk + (currentChunk ? ' ' : '') + sentence;
      
      if (potentialChunk.length > this.config.maxChunkSize && currentChunk.length > 0) {
        chunks.push(this.createChunk(
          currentChunk,
          currentStart,
          currentStart + currentChunk.length,
          'mixed',
          {}
        ));
        
        currentChunk = sentence;
        currentStart += currentChunk.length + 1;
        chunkIndex++;
      } else {
        currentChunk = potentialChunk;
      }
    }

    if (currentChunk.length > 0) {
      chunks.push(this.createChunk(
        currentChunk,
        currentStart,
        currentStart + currentChunk.length,
        'mixed',
        {}
      ));
    }

    return chunks;
  }

  /**
   * Split large chunk into smaller ones
   */
  private splitLargeChunk(chunk: SemanticChunk): SemanticChunk[] {
    const subChunks = this.splitByFixedSize(chunk.content, chunk.startIndex);
    
    return subChunks.map((subChunk, index) => ({
      ...subChunk,
      id: `${chunk.id}_${index}`,
      metadata: {
        ...chunk.metadata,
        parentChunkId: chunk.id,
        subChunkIndex: index
      }
    }));
  }

  /**
   * Add overlaps between adjacent chunks
   */
  private addChunkOverlaps(chunks: SemanticChunk[], originalContent: string): void {
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const overlapSize = Math.min(
        this.config.overlapSize,
        Math.floor(chunk.content.length * this.config.overlapPercentage / 100)
      );

      if (i > 0) {
        // Add overlap with previous chunk
        const prevChunk = chunks[i - 1];
        const overlapStart = Math.max(0, prevChunk.endIndex - overlapSize);
        const overlapContent = originalContent.slice(overlapStart, prevChunk.endIndex);
        
        if (!chunk.overlap) chunk.overlap = { overlapSize: 0 };
        chunk.overlap.previousChunk = overlapContent;
        chunk.overlap.overlapSize += overlapContent.length;
      }

      if (i < chunks.length - 1) {
        // Add overlap with next chunk
        const nextChunk = chunks[i + 1];
        const overlapEnd = Math.min(originalContent.length, nextChunk.startIndex + overlapSize);
        const overlapContent = originalContent.slice(nextChunk.startIndex, overlapEnd);
        
        if (!chunk.overlap) chunk.overlap = { overlapSize: 0 };
        chunk.overlap.nextChunk = overlapContent;
        chunk.overlap.overlapSize += overlapContent.length;
      }
    }
  }

  /**
   * Calculate relevance scores for chunks
   */
  private calculateRelevanceScores(chunks: SemanticChunk[]): void {
    for (const chunk of chunks) {
      chunk.metadata.relevanceScore = this.calculateChunkRelevance(chunk);
    }
  }

  /**
   * Calculate relevance score for a single chunk
   */
  private calculateChunkRelevance(chunk: SemanticChunk): number {
    let score = 0.5; // Base score

    // Boost score for headings
    if (chunk.metadata.chunkType === 'heading') {
      score += 0.3;
    }

    // Boost score for structured content
    if (chunk.metadata.hasLists || chunk.metadata.hasTables) {
      score += 0.2;
    }

    // Penalize very short chunks
    if (chunk.metadata.wordCount < this.config.minChunkSize / 5) {
      score -= 0.2;
    }

    // Boost score for semantic density
    score += chunk.metadata.semanticDensity * 0.1;

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Split text into sentences
   */
  private splitIntoSentences(text: string): string[] {
    // Simple sentence splitting - can be enhanced with NLP libraries
    return text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  }

  /**
   * Create a semantic chunk with metadata
   */
  private createChunk(
    content: string,
    startIndex: number,
    endIndex: number,
    type: StructuralElementType,
    metadata: Record<string, any>
  ): SemanticChunk {
    const words = content.split(/\s+/).filter(w => w.length > 0);
    const sentences = this.splitIntoSentences(content);

    return {
      id: `chunk_${startIndex}_${endIndex}`,
      content: content.trim(),
      startIndex,
      endIndex,
      metadata: {
        chunkType: type,
        structuralDepth: metadata.headingLevel || 0,
        wordCount: words.length,
        sentenceCount: sentences.length,
        hasCode: /```|`/.test(content),
        hasTables: /\|.*\|/.test(content),
        hasLists: /^[-*+]\s|^\d+\.\s/m.test(content),
        semanticDensity: this.calculateSemanticDensity(content),
        ...metadata
      }
    };
  }

  /**
   * Calculate semantic density of content
   */
  private calculateSemanticDensity(content: string): number {
    const words = content.split(/\s+/).filter(w => w.length > 0);
    const uniqueWords = new Set(words.map(w => w.toLowerCase()));
    
    // Simple semantic density calculation
    return words.length > 0 ? uniqueWords.size / words.length : 0;
  }
}

// Type definitions
interface DocumentStructure {
  elements: StructuralElement[];
}

interface StructuralElement {
  type: StructuralElementType;
  content: string;
  startIndex: number;
  endIndex: number;
  metadata: Record<string, any>;
}

type StructuralElementType = 'heading' | 'paragraph' | 'list' | 'table' | 'code' | 'mixed';

export { SemanticChunkingConfig, SemanticChunk };
