"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, HelpCircle, LogOut } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface UserProfileMenuProps {
  onCustomizeAssistant: () => void;
  onSettings: () => void;
  onHelp: () => void;
  onLogout: () => void;
}

export function UserProfileMenu({
  onCustomizeAssistant,
  onSettings,
  onHelp,
  onLogout
}: UserProfileMenuProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="icon" 
          title="User Profile"
          className="hover:bg-muted hover:text-muted-foreground"
        >
          <User className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuItem onClick={onCustomizeAssistant}>
          <Palette className="h-4 w-4 mr-2" />
          Customize Assistant
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onSettings}>
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onHelp}>
          <HelpCircle className="h-4 w-4 mr-2" />
          Help
        </DropdownMenuItem>
        <DropdownMenuItem onClick={onLogout} className="text-destructive focus:text-destructive">
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}