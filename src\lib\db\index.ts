import { drizzle } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import * as schema from './schema';
import { migrate } from 'drizzle-orm/libsql/migrator';
import path from 'path';
import fs from 'fs';

// Ensure data directory exists
const dataDir = path.join(process.cwd(), 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Create database connection using libsql
const client = createClient({
  url: `file:${path.join(dataDir, 'agent.db')}`
});

export const db = drizzle(client, { schema });

// Initialize database with migrations
export async function initializeDatabase() {
  try {
    console.log('🚀 Initializing database...');
    
    // Run migrations
    await migrate(db, { migrationsFolder: path.join(process.cwd(), 'drizzle') });
    console.log('✅ Database migrations completed');
    
    // Initialize performance optimizations
    try {
      const { initializeDatabaseOptimizations } = await import('./indexes');
      await initializeDatabaseOptimizations();
      console.log('✅ Database optimizations applied');
    } catch (optimizationError) {
      // Don't fail initialization if optimizations fail
      const errorMessage = optimizationError instanceof Error ? optimizationError.message : String(optimizationError);
      console.warn('⚠️  Database optimizations failed (non-critical):', errorMessage);
    }
    
    console.log('✅ Database initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize database:', error);
    throw error;
  }
}

export { schema };
export default db;
