"use client"

import { Play, Pause, Square } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface MissionControlsProps {
  status: string;
  onPause: () => void;
  onResume: () => void;
  onCancel: () => void;
}

export function MissionControls({ 
  status, 
  onPause, 
  onResume, 
  onCancel 
}: MissionControlsProps) {
  return (
    <div className="flex items-center space-x-2">
      {status === 'executing' && (
        <Button
          variant="outline"
          size="sm"
          onClick={onPause}
        >
          <Pause className="h-4 w-4 mr-1" />
          Pause
        </Button>
      )}
      {status === 'pending' && (
        <Button
          variant="outline"
          size="sm"
          onClick={onResume}
        >
          <Play className="h-4 w-4 mr-1" />
          Resume
        </Button>
      )}
      {(status === 'executing' || status === 'pending') && (
        <Button
          variant="destructive"
          size="sm"
          onClick={onCancel}
        >
          <Square className="h-4 w-4 mr-1" />
          Cancel
        </Button>
      )}
    </div>
  );
}