import { NextRequest, NextResponse } from 'next/server';
import { createDocumentProcessor } from '@/lib/llamaindex';
import { DeepSeekClient } from '@/lib/agent/deepseek';

// Initialize DeepSeek client
const deepseek = new DeepSeekClient({
  apiKey: process.env.DEEPSEEK_API_KEY || '',
});

// Initialize document processor
const documentProcessor = createDocumentProcessor(deepseek);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      query, 
      limit = 10, 
      sourceType,
      threshold = 0.3,
      useChunks = true,
      includeConcepts = false,
    } = body;

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Query is required' },
        { status: 400 }
      );
    }

    // Perform advanced search using DocumentProcessor
    const searchResults = await documentProcessor.searchDocuments(query, {
      limit,
      sourceType,
      threshold,
      useChunks,
    });

    // Enhanced results with additional metadata
    const enhancedResults = await Promise.all(
      searchResults.map(async (result) => {
        const enhanced = {
          id: result.document.id,
          title: result.document.metadata.title,
          summary: result.document.metadata.title,
          content: useChunks ? result.chunk.content : result.document.content.substring(0, 500),
          sourceUrl: result.document.metadata.sourceUrl,
          sourceType: result.document.metadata.sourceType,
          score: Math.round(result.score * 100) / 100,
          snippet: result.snippet,
          tags: result.document.metadata.tags || [],
          lastIndexed: result.document.metadata.lastIndexed,
          chunkInfo: useChunks ? {
            chunkId: result.chunk.id,
            startIndex: result.chunk.startIndex,
            endIndex: result.chunk.endIndex,
            totalChunks: result.document.chunks.length,
          } : null,
          concepts: includeConcepts 
            ? await documentProcessor.extractConcepts(result.chunk.content || result.document.content)
            : undefined,
        };

        return enhanced;
      })
    );

    // Generate search insights
    const searchInsights = {
      totalResults: enhancedResults.length,
      averageScore: enhancedResults.length > 0 
        ? enhancedResults.reduce((sum, r) => sum + r.score, 0) / enhancedResults.length 
        : 0,
      sourceTypes: [...new Set(enhancedResults.map(r => r.sourceType))],
      hasHighConfidenceResults: enhancedResults.some(r => r.score > 0.8),
      searchType: useChunks ? 'chunk-level' : 'document-level',
    };

    return NextResponse.json({
      success: true,
      data: {
        query,
        results: enhancedResults,
        insights: searchInsights,
        searchParams: {
          limit,
          sourceType: sourceType || 'all',
          threshold,
          useChunks,
          includeConcepts,
        },
      },
    });

  } catch (error) {
    console.error('Knowledge search error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to search knowledge base',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '5');
    const sourceType = searchParams.get('sourceType');
    const threshold = parseFloat(searchParams.get('threshold') || '0.3');

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Query parameter "q" is required' },
        { status: 400 }
      );
    }

    // Simple search for GET requests
    const searchResults = await documentProcessor.searchDocuments(query, {
      limit,
      sourceType: sourceType || undefined,
      threshold,
      useChunks: false, // Use document-level for simple GET requests
    });

    const simpleResults = searchResults.map(result => ({
      id: result.document.id,
      title: result.document.metadata.title,
      snippet: result.snippet,
      sourceUrl: result.document.metadata.sourceUrl,
      score: Math.round(result.score * 100) / 100,
      sourceType: result.document.metadata.sourceType,
    }));

    return NextResponse.json({
      success: true,
      data: {
        query,
        results: simpleResults,
        total: simpleResults.length,
      },
    });

  } catch (error) {
    console.error('Knowledge search error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to search knowledge base' },
      { status: 500 }
    );
  }
}