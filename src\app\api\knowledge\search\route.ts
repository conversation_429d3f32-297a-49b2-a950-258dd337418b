import { NextRequest, NextResponse } from 'next/server';
import { createLlamaIndexService } from '@/lib/llamaindex';
import { createChromaVectorStore } from '@/lib/vector-store/chroma';
import { DeepSeekClient } from '@/lib/agent/deepseek';

// Initialize services
const deepseek = new DeepSeekClient({
  deepseekApiKey: process.env.DEEPSEEK_API_KEY || '',
  deepseekBaseUrl: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
});

const llamaIndexService = createLlamaIndexService(process.env.OPENAI_API_KEY || '');
const vectorStore = createChromaVectorStore(
  process.env.CHROMA_HOST || 'localhost',
  parseInt(process.env.CHROMA_PORT || '8000')
);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      query,
      filters = {},
      limit = 10,
      threshold = 0.7,
      searchMode = 'hybrid',
    } = body;

    if (!query || typeof query !== 'string') {
      return NextResponse.json(
        { success: false, error: 'Query is required and must be a string' },
        { status: 400 }
      );
    }

    const results = [];
    const sources = new Set();

    // LlamaIndex semantic search
    if (searchMode === 'semantic' || searchMode === 'hybrid') {
      try {
        const semanticResults = await llamaIndexService.query(query, {
          topK: limit,
          similarityThreshold: threshold,
        });

        for (const result of semanticResults) {
          results.push({
            id: result.sourceId,
            content: result.content,
            metadata: result.metadata,
            score: result.score,
            source: 'llamaindex',
          });
          sources.add(result.sourceId);
        }
      } catch (error) {
        console.error('LlamaIndex search error:', error);
      }
    }

    // ChromaDB vector search
    if (searchMode === 'vector' || searchMode === 'hybrid') {
      try {
        const vectorResults = await vectorStore.queryDocuments(
          'default',
          query,
          { nResults: limit }
        );

        for (const result of vectorResults) {
          if (!sources.has(result.id)) {
            results.push({
              id: result.id,
              content: result.content,
              metadata: result.metadata,
              score: result.similarity,
              source: 'chromadb',
            });
            sources.add(result.id);
          }
        }
      } catch (error) {
        console.error('ChromaDB search error:', error);
      }
    }

    // Sort by relevance score and limit results
    const sortedResults = results
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .filter(result => result.score >= threshold);

    // Generate insights from aggregated results
    let insights = [];
    if (sortedResults.length > 0) {
      try {
        const aggregatedContent = sortedResults
          .slice(0, 5) // Use top 5 results for insights
          .map((result: any) => result.content)
          .join('\n\n');

        const summaryResponse = await deepseek.chat([
          {
            role: 'system',
            content: 'You are an expert at analyzing and summarizing search results. Extract key insights and themes from the provided content.',
          },
          {
            role: 'user',
            content: `Based on the search query "${query}" and the following content, provide key insights:\n\n${aggregatedContent}`,
          },
        ], { maxTokens: 500 });

        insights = summaryResponse.choices[0]?.message?.content
          ?.split('\n')
          .filter((insight: string) => insight.trim().length > 0) || [];
      } catch (error) {
        console.error('Insights generation error:', error);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        query,
        results: sortedResults,
        totalResults: sortedResults.length,
        searchMode,
        insights: insights.slice(0, 5), // Limit to 5 insights
        sources: Array.from(sources),
      },
    });
  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal search error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '10');
    const threshold = parseFloat(searchParams.get('threshold') || '0.7');

    if (!query) {
      return NextResponse.json(
        { success: false, error: 'Query parameter "q" is required' },
        { status: 400 }
      );
    }

    // Simple search for GET requests (LlamaIndex only)
    const results = await llamaIndexService.query(query, {
      topK: limit,
      similarityThreshold: threshold,
    });

    return NextResponse.json({
      success: true,
      data: {
        query,
        results: results.map(result => ({
          id: result.sourceId,
          content: result.content,
          metadata: result.metadata,
          score: result.score,
        })),
        totalResults: results.length,
      },
    });
  } catch (error) {
    console.error('GET search error:', error);
    return NextResponse.json(
      { success: false, error: 'Search failed' },
      { status: 500 }
    );
  }
}