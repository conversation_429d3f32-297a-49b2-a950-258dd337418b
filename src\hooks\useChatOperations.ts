import { useState, useCallback, FormEvent } from 'react';
import { Message } from '@/components/mission/types';
import { UI_CONFIG } from '@/lib/constants';

/**
 * Custom hook to manage chat operations and message handling
 */
export function useChatOperations() {
  const [isLoading, setIsLoading] = useState(false);

  // Submit message handler
  const handleSubmit = useCallback(async (
    e: FormEvent,
    input: string,
    selectedMissionId: number | null,
    messages: Message[],
    setMessages: (messages: Message[]) => void,
    setInput: (input: string) => void
  ) => {
    e.preventDefault();
    
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: messages.length + 1,
      content: input.trim(),
      sender: "user",
      timestamp: new Date(),
    };

    setMessages([...messages, userMessage]);
    setInput("");
    setIsLoading(true);

    try {
      let response;
      
      if (selectedMissionId) {
        // Send message with mission context
        response = await fetch('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message: input.trim(),
            missionId: selectedMissionId,
            history: messages.slice(-10) // Send last 10 messages for context
          }),
        });
      } else {
        // Create new mission from message
        response = await fetch('/api/missions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            title: input.trim().slice(0, 50) + (input.trim().length > 50 ? '...' : ''),
            description: input.trim(),
            priority: 'medium'
          }),
        });
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      const aiMessage: Message = {
        id: messages.length + 2,
        content: data.response || data.message || "I've received your request and will work on it.",
        sender: "ai",
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      console.error('Error sending message:', error);
      
      const errorMessage: Message = {
        id: messages.length + 2,
        content: "I apologize, but I encountered an error processing your request. Please try again.",
        sender: "ai",
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [isLoading]);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((
    e: React.KeyboardEvent<HTMLTextAreaElement>,
    handleSubmitCallback: () => void
  ) => {
    if (e.key === "Enter" && e.ctrlKey) {
      e.preventDefault();
      handleSubmitCallback();
    }
  }, []);

  // Mission action handler
  const handleMissionAction = useCallback(async (
    action: 'start' | 'pause' | 'resume' | 'cancel',
    missionId: string
  ) => {
    try {
      const response = await fetch(`/api/missions/${missionId}/${action}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to ${action} mission`);
      }

      const result = await response.json();
      console.log(`Mission ${action} result:`, result);

    } catch (error) {
      console.error(`Error ${action}ing mission:`, error);
    }
  }, []);

  // Format message content for display
  const formatMessageContent = useCallback((content: string): string => {
    // Basic formatting - can be extended
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>');
  }, []);

  // Get welcome message for new missions
  const getWelcomeMessage = useCallback((selectedMissionId: number | null): Message[] => {
    if (selectedMissionId) {
      return [];
    }

    return [
      {
        id: 0,
        content: "Hello! I'm your AI assistant. I can help you create and manage missions, search knowledge bases, and execute complex tasks. What would you like to work on today?",
        sender: "ai",
        timestamp: new Date(),
      },
    ];
  }, []);

  // Validate message input
  const validateInput = useCallback((input: string): { isValid: boolean; error?: string } => {
    if (!input.trim()) {
      return { isValid: false, error: 'Message cannot be empty' };
    }

    if (input.length > 2000) {
      return { isValid: false, error: 'Message is too long (max 2000 characters)' };
    }

    return { isValid: true };
  }, []);

  // Auto-save draft messages
  const saveDraft = useCallback((input: string, missionId: number | null) => {
    if (input.trim()) {
      const key = missionId ? `draft_mission_${missionId}` : 'draft_new_mission';
      localStorage.setItem(key, input);
    }
  }, []);

  // Load draft messages
  const loadDraft = useCallback((missionId: number | null): string => {
    const key = missionId ? `draft_mission_${missionId}` : 'draft_new_mission';
    return localStorage.getItem(key) || '';
  }, []);

  // Clear draft messages
  const clearDraft = useCallback((missionId: number | null) => {
    const key = missionId ? `draft_mission_${missionId}` : 'draft_new_mission';
    localStorage.removeItem(key);
  }, []);

  // Message search functionality
  const searchMessages = useCallback((messages: Message[], query: string): Message[] => {
    if (!query.trim()) return messages;

    const searchTerm = query.toLowerCase();
    return messages.filter(message =>
      message.content.toLowerCase().includes(searchTerm)
    );
  }, []);

  // Export chat history
  const exportChatHistory = useCallback((messages: Message[], missionId: number | null) => {
    const chatData = {
      missionId,
      exportDate: new Date().toISOString(),
      messageCount: messages.length,
      messages: messages.map(msg => ({
        id: msg.id,
        content: msg.content,
        sender: msg.sender,
        timestamp: msg.timestamp.toISOString()
      }))
    };

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-history-${missionId || 'new'}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, []);

  return {
    isLoading,
    handleSubmit,
    handleKeyDown,
    handleMissionAction,
    formatMessageContent,
    getWelcomeMessage,
    validateInput,
    saveDraft,
    loadDraft,
    clearDraft,
    searchMessages,
    exportChatHistory,
  };
}
