# Project Purpose

**Service Management Assistant AI Agent**

This is a Next.js application for building a service management assistant AI agent with five core capabilities:

1. **Planning**: Decompose user missions into executable step-by-step plans
2. **Tool Use**: Access and utilize a predefined set of tools to execute tasks  
3. **Autonomous Execution**: Execute plans independently without requiring constant user intervention
4. **Reflective Learning**: Continuously assess progress, identify issues, and dynamically improve plans during execution
5. **Goal-Oriented Behavior**: Maintain focus on the original mission objective and ensure all actions contribute to goal completion

## Key Features

- **Confluence Integration**: Load Confluence pages via URLs, index content using embeddings
- **Natural Language Retrieval**: Query Confluence content using natural language
- **Source Attribution**: Display retrieved content with clear source references  
- **Real-time Updates**: Show live progress of plan execution and task completion
- **Plan Visualization**: Display current plan, completed steps, and upcoming tasks in an intuitive interface
- **Mission Management**: Create, execute, pause, resume, and cancel missions through a web interface