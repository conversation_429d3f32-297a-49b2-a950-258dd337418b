import { NextRequest, NextResponse } from 'next/server';
import { vectorStoreService } from '@/lib/vector-store/service-manager';

/**
 * POST /api/knowledge/enhanced-search - Enhanced vector search with multiple strategies
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      query,
      limit = 10,
      threshold = 0.7,
      sourceType,
      useChunks = true,
      searchType = 'semantic', // 'semantic' | 'concept' | 'similar'
      documentId, // For similarity search
      concepts, // For concept search
    } = body;

    if (!query && searchType === 'semantic') {
      return NextResponse.json(
        {
          success: false,
          error: 'Query is required for semantic search',
        },
        { status: 400 }
      );
    }

    const processor = vectorStoreService.getDocumentProcessor();
    let results;

    switch (searchType) {
      case 'semantic':
        results = await vectorStoreService.searchDocuments(query, {
          limit,
          threshold,
          sourceType,
          useChunks,
        });
        break;

      case 'concept':
        if (!concepts || !Array.isArray(concepts)) {
          return NextResponse.json(
            {
              success: false,
              error: 'Concepts array is required for concept search',
            },
            { status: 400 }
          );
        }
        
        if (vectorStoreService.isEnhanced()) {
          const enhancedProcessor = processor as any;
          results = await enhancedProcessor.searchByConcepts(concepts, {
            limit,
            threshold,
            sourceType,
            useChunks,
          });
        } else {
          // Fallback to semantic search with concepts joined
          results = await vectorStoreService.searchDocuments(concepts.join(' '), {
            limit,
            threshold,
            sourceType,
            useChunks,
          });
        }
        break;

      case 'similar':
        if (!documentId) {
          return NextResponse.json(
            {
              success: false,
              error: 'Document ID is required for similarity search',
            },
            { status: 400 }
          );
        }

        if (vectorStoreService.isEnhanced()) {
          const enhancedProcessor = processor as any;
          results = await enhancedProcessor.findSimilarDocuments(documentId, limit);
        } else {
          return NextResponse.json(
            {
              success: false,
              error: 'Similarity search requires enhanced vector store (Chroma)',
            },
            { status: 501 }
          );
        }
        break;

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid search type',
            validTypes: ['semantic', 'concept', 'similar'],
          },
          { status: 400 }
        );
    }

    // Add metadata about the search
    const searchMetadata = {
      query,
      searchType,
      limit,
      threshold,
      sourceType,
      useChunks,
      enhanced: vectorStoreService.isEnhanced(),
      resultCount: results.length,
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json({
      success: true,
      data: {
        results,
        metadata: searchMetadata,
      },
    });

  } catch (error) {
    console.error('Enhanced search error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Enhanced search failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/knowledge/enhanced-search - Get search capabilities and status
 */
export async function GET() {
  try {
    const status = vectorStoreService.getStatus();
    const health = await vectorStoreService.healthCheck();

    return NextResponse.json({
      success: true,
      data: {
        capabilities: {
          semanticSearch: true,
          conceptSearch: status.enhanced,
          similaritySearch: status.enhanced,
          vectorStore: status.chromaAvailable,
          enhanced: status.enhanced,
        },
        searchTypes: [
          {
            type: 'semantic',
            description: 'Vector similarity search using embeddings',
            available: true,
          },
          {
            type: 'concept',
            description: 'Search by extracted concepts and keywords',
            available: status.enhanced,
          },
          {
            type: 'similar',
            description: 'Find documents similar to a given document',
            available: status.enhanced,
          },
        ],
        health,
        status,
      },
    });
  } catch (error) {
    console.error('Error getting search capabilities:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get search capabilities',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}