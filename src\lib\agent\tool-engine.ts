import { ToolUseCapability, Tool, Task, ExecutionContext, ToolResult } from './types';
import { DeepSeekClient } from './deepseek';
import { ToolRegistry } from './tools/base';
import { ToolUsageOperations } from '../db/operations';
import { RETRY_CONFIG, ERROR_CODES } from '../constants';
import { AgentLogger } from './logger';
import { KnowledgeBaseValidator, KnowledgeAuditLogger } from './knowledge-validator';

/**
 * Tool execution engine with knowledge-base-first enforcement
 */
export class ToolUseEngine implements ToolUseCapability {
  private static readonly KNOWLEDGE_TOOLS = [
    'knowledge_base_query',
    'incident_query',
    'confluence_search',
    'document_search',
    'vector_search'
  ];

  constructor(
    private deepseek: DeepSeekClient,
    private toolRegistry: ToolRegistry
  ) {}

  /**
   * Get all available tools
   */
  getAvailableTools(): Tool[] {
    return this.toolRegistry.getAll();
  }

  /**
   * Select the most appropriate tool for a task with knowledge-base-first priority
   */
  async selectTool(task: Task, context: ExecutionContext): Promise<Tool | null> {
    const logger = context.logger;

    // KNOWLEDGE-BASE-FIRST ENFORCEMENT
    // If tool is explicitly specified, validate it follows knowledge-first principle
    if (task.toolName) {
      const tool = this.toolRegistry.get(task.toolName);
      if (tool) {
        // If this is not a knowledge tool, ensure we have knowledge context
        if (!ToolUseEngine.KNOWLEDGE_TOOLS.includes(task.toolName)) {
          const hasKnowledgeContext = await this.validateKnowledgeContext(context);
          if (!hasKnowledgeContext) {
            logger.error(`Tool ${task.toolName} requires knowledge context but none found`);
            throw new Error(`Cannot execute ${task.toolName} without prior knowledge retrieval. Knowledge-base-first principle violated.`);
          }
        }

        logger.info(`Using specified tool: ${task.toolName}`);
        return tool;
      } else {
        logger.warn(`Specified tool not found: ${task.toolName}`);
      }
    }

    try {
      // PRIORITIZE KNOWLEDGE TOOLS
      // If this looks like a knowledge retrieval task, prioritize knowledge tools
      const knowledgeTool = this.findKnowledgeTool(task, context);
      if (knowledgeTool) {
        logger.info(`Prioritized knowledge tool: ${knowledgeTool.name}`);
        return knowledgeTool;
      }

      // Use AI to select the best tool with knowledge-first instructions
      logger.info(`Selecting tool for task: ${task.title}`);

      const knowledgeFirstInstructions = this.buildKnowledgeFirstInstructions(task);
      const selectionResponse = await this.deepseek.selectTool(
        { ...task, description: `${task.description}\n\n${knowledgeFirstInstructions}` },
        this.toolRegistry.getNames()
      );

      const selectionData = JSON.parse(selectionResponse);
      const selectedTool = this.toolRegistry.get(selectionData.selectedTool);

      if (selectedTool) {
        // Validate knowledge-first compliance
        if (!ToolUseEngine.KNOWLEDGE_TOOLS.includes(selectedTool.name)) {
          const hasKnowledgeContext = await this.validateKnowledgeContext(context);
          if (!hasKnowledgeContext) {
            logger.error(`AI selected non-knowledge tool ${selectedTool.name} without knowledge context`);
            throw new Error(`Cannot use ${selectedTool.name} without prior knowledge retrieval`);
          }
        }

        logger.info(`AI selected tool: ${selectionData.selectedTool}`);
        return selectedTool;
      } else {
        logger.warn(`AI selected unknown tool: ${selectionData.selectedTool}`);
      }

    } catch (error) {
      logger.error(`Failed to select tool: ${error}`, undefined, error as Error);
    }

    // Fallback: try to find a suitable tool based on task type
    return this.findFallbackTool(task, context);
  }

  /**
   * Execute a tool with knowledge-base-first validation and retry logic
   */
  async executeTool(
    tool: Tool,
    params: Record<string, any>,
    context: ExecutionContext
  ): Promise<ToolResult> {
    const logger = context.logger;
    logger.info(`Executing tool: ${tool.name}`);

    // KNOWLEDGE-BASE-FIRST VALIDATION
    if (!ToolUseEngine.KNOWLEDGE_TOOLS.includes(tool.name)) {
      const hasKnowledgeContext = await this.validateKnowledgeContext(context);
      if (!hasKnowledgeContext) {
        const error = `Cannot execute ${tool.name} without prior knowledge retrieval. Knowledge-base-first principle violated.`;
        logger.error(error);
        KnowledgeAuditLogger.logValidationFailure(
          { isValid: false, issues: [{ type: 'error', code: 'NO_KNOWLEDGE_CONTEXT', message: error, taskId: context.taskId }] },
          context
        );
        return {
          success: false,
          error,
          data: null,
          metadata: { toolName: tool.name, violatesKnowledgeFirst: true }
        };
      }
    }

    const startTime = Date.now();
    let lastError: Error | null = null;

    // Retry logic
    for (let attempt = 1; attempt <= RETRY_CONFIG.MAX_ATTEMPTS; attempt++) {
      try {
        // Validate parameters
        this.validateToolParameters(tool, params);

        // Execute the tool
        const result = await this.executeToolWithTimeout(tool, params, context);
        const duration = Date.now() - startTime;

        // Log successful execution
        await this.logToolUsage({
          taskId: context.taskId,
          toolName: tool.name,
          parameters: params,
          result: result.data,
          success: result.success,
          duration,
          errorMessage: result.error,
        });

        logger.info(`Tool executed successfully in ${duration}ms`);
        return result;

      } catch (error) {
        lastError = error as Error;
        const duration = Date.now() - startTime;

        logger.warn(`Tool execution attempt ${attempt} failed: ${lastError.message}`);

        // Log failed attempt
        await this.logToolUsage({
          taskId: context.taskId,
          toolName: tool.name,
          parameters: params,
          result: null,
          success: false,
          duration,
          errorMessage: lastError.message,
        });

        // Check if we should retry
        if (attempt < RETRY_CONFIG.MAX_ATTEMPTS && this.shouldRetry(lastError)) {
          const delay = this.calculateRetryDelay(attempt);
          logger.info(`Retrying in ${delay}ms...`);
          await this.sleep(delay);
          continue;
        }

        break;
      }
    }

    // All attempts failed
    const totalDuration = Date.now() - startTime;
    logger.error(`Tool execution failed after ${RETRY_CONFIG.MAX_ATTEMPTS} attempts`);

    return {
      success: false,
      error: lastError?.message || 'Unknown error',
      data: null,
      metadata: {
        attempts: RETRY_CONFIG.MAX_ATTEMPTS,
        totalDuration,
        toolName: tool.name,
      }
    };
  }

  /**
   * Execute tool with timeout protection
   */
  private async executeToolWithTimeout(
    tool: Tool,
    params: Record<string, any>,
    context: ExecutionContext
  ): Promise<ToolResult> {
    return new Promise(async (resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Tool execution timeout after ${RETRY_CONFIG.MAX_DELAY}ms`));
      }, RETRY_CONFIG.MAX_DELAY);

      try {
        const result = await tool.execute(params, context);
        clearTimeout(timeout);
        resolve(result);
      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  /**
   * Validate tool parameters against tool schema
   */
  private validateToolParameters(tool: Tool, params: Record<string, any>): void {
    // Basic validation - can be extended with JSON schema validation
    if (!params || typeof params !== 'object') {
      throw new Error(`Invalid parameters for tool ${tool.name}: must be an object`);
    }

    // Tool-specific validation would go here
    // This could be enhanced with JSON Schema validation
  }

  /**
   * Find a fallback tool when AI selection fails
   */
  private findFallbackTool(task: Task, context: ExecutionContext): Tool | null {
    const logger = context.logger;
    
    // Simple heuristic-based tool selection
    const availableTools = this.getAvailableTools();
    
    // Look for tools that match task keywords
    const taskText = `${task.title} ${task.description}`.toLowerCase();
    
    for (const tool of availableTools) {
      const toolName = tool.name.toLowerCase();
      
      // Simple keyword matching
      if (taskText.includes('confluence') && toolName.includes('confluence')) {
        logger.info(`Fallback tool selected: ${tool.name}`);
        return tool;
      }
      
      if (taskText.includes('search') && toolName.includes('search')) {
        logger.info(`Fallback tool selected: ${tool.name}`);
        return tool;
      }
      
      if (taskText.includes('load') && toolName.includes('load')) {
        logger.info(`Fallback tool selected: ${tool.name}`);
        return tool;
      }
    }

    logger.warn('No suitable fallback tool found');
    return null;
  }

  /**
   * Determine if an error is retryable
   */
  private shouldRetry(error: Error): boolean {
    const message = error.message.toLowerCase();
    
    // Retry on network errors, timeouts, and temporary failures
    const retryablePatterns = [
      'network',
      'timeout',
      'connection',
      'temporary',
      'rate limit',
      'service unavailable',
      'internal server error'
    ];

    return retryablePatterns.some(pattern => message.includes(pattern));
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private calculateRetryDelay(attempt: number): number {
    const baseDelay = RETRY_CONFIG.BASE_DELAY;
    const backoffFactor = RETRY_CONFIG.BACKOFF_FACTOR;
    const maxDelay = RETRY_CONFIG.MAX_DELAY;

    const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt - 1), maxDelay);
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    
    return Math.round(delay + jitter);
  }

  /**
   * Sleep for specified duration
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Log tool usage for analytics and debugging
   */
  private async logToolUsage(data: {
    taskId?: string;
    toolName: string;
    parameters: Record<string, any>;
    result: any;
    success: boolean;
    duration: number;
    errorMessage?: string;
  }): Promise<void> {
    try {
      await ToolUsageOperations.log(data);
    } catch (error) {
      // Don't fail the main operation if logging fails
      console.warn('Failed to log tool usage:', error);
    }
  }

  /**
   * Get tool usage statistics
   */
  async getToolStats(toolName?: string): Promise<any[]> {
    try {
      return await ToolUsageOperations.getStats(toolName);
    } catch (error) {
      console.error('Failed to get tool stats:', error);
      return [];
    }
  }

  /**
   * Register a new tool
   */
  registerTool(tool: Tool): void {
    this.toolRegistry.register(tool);
  }

  /**
   * Unregister a tool
   */
  unregisterTool(toolName: string): void {
    this.toolRegistry.unregister(toolName);
  }

  /**
   * Find appropriate knowledge tool for a task
   */
  private findKnowledgeTool(task: Task, context: ExecutionContext): Tool | null {
    const taskText = `${task.title} ${task.description}`.toLowerCase();

    // Check for incident-related queries
    if (taskText.includes('incident') || taskText.includes('outage') || taskText.includes('failure')) {
      const incidentTool = this.toolRegistry.get('incident_query');
      if (incidentTool) {
        context.logger.info('Detected incident query, using incident_query tool');
        return incidentTool;
      }
    }

    // Check for Confluence-specific queries
    if (taskText.includes('confluence') || taskText.includes('wiki')) {
      const confluenceTool = this.toolRegistry.get('confluence_search');
      if (confluenceTool) {
        context.logger.info('Detected Confluence query, using confluence_search tool');
        return confluenceTool;
      }
    }

    // Default to general knowledge base query
    const knowledgeTool = this.toolRegistry.get('knowledge_base_query');
    if (knowledgeTool && this.isKnowledgeQuery(taskText)) {
      context.logger.info('Detected knowledge query, using knowledge_base_query tool');
      return knowledgeTool;
    }

    return null;
  }

  /**
   * Check if task requires knowledge retrieval
   */
  private isKnowledgeQuery(taskText: string): boolean {
    const knowledgeKeywords = [
      'search', 'find', 'query', 'retrieve', 'get', 'fetch', 'look up',
      'how many', 'what is', 'when did', 'where is', 'who is',
      'incidents', 'documents', 'information', 'data'
    ];

    return knowledgeKeywords.some(keyword => taskText.includes(keyword));
  }

  /**
   * Build knowledge-first instructions for AI tool selection
   */
  private buildKnowledgeFirstInstructions(task: Task): string {
    return `
CRITICAL: This task must follow the knowledge-base-first principle.

If this task requires any information, data, or facts:
1. MUST use a knowledge retrieval tool first (knowledge_base_query, incident_query, confluence_search)
2. CANNOT proceed with reasoning or response generation without retrieved data
3. If no relevant data is found, respond with "I don't have enough information in the knowledge base"

Available knowledge tools: ${ToolUseEngine.KNOWLEDGE_TOOLS.join(', ')}

Only select non-knowledge tools if:
- Knowledge has already been retrieved in previous tasks
- The task is purely operational (no information needed)
`;
  }

  /**
   * Validate that knowledge context exists for non-knowledge tools
   */
  private async validateKnowledgeContext(context: ExecutionContext): Promise<boolean> {
    // Check if we have knowledge results in the current execution context
    // This would typically be stored in the context or retrieved from previous task results

    // For now, we'll check if this is the first task (which should always be knowledge retrieval)
    // In a full implementation, you'd check the task execution history

    if (context.taskId) {
      // Check if there are previous completed knowledge retrieval tasks
      // This is a simplified check - in production you'd query the database
      return true; // Placeholder - implement actual validation
    }

    return false;
  }
}
