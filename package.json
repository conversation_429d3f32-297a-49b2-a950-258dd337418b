{"name": "my-assistant-v3", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:init": "tsx scripts/init-db.ts", "db:studio": "drizzle-kit studio", "ws:start": "tsx scripts/start-websocket-server.ts", "dev:full": "concurrently \"npm run dev\" \"npm run ws:start\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=__tests__", "test:performance": "jest src/test/performance.test.ts", "test:e2e": "playwright test", "test:all": "npm run test:unit && npm run test:integration && npm run test:performance"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/websocket": "^11.1.0", "@libsql/client": "^0.14.0", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@types/cheerio": "^0.22.35", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "axios": "^1.10.0", "cheerio": "^1.1.0", "chromadb": "^3.0.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "fastify": "^5.4.0", "llamaindex": "^0.11.12", "lucide-react": "^0.525.0", "next": "15.3.5", "node-cache": "^5.1.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tsx": "^4.20.3", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@jest/globals": "^29.7.0", "@playwright/test": "^1.48.0", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.1.0", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}