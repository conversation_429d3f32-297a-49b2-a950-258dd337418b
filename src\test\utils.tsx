import React, { ReactElement } from 'react';
import { render, RenderOptions, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { jest } from '@jest/globals';

// Mock data for testing
export const mockMission = {
  id: 'test-mission-1',
  title: 'Test Mission',
  description: 'This is a test mission for testing purposes',
  status: 'pending' as const,
  priority: 'medium' as const,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  completedAt: null,
  metadata: {}
};

export const mockPlan = {
  id: 'test-plan-1',
  missionId: 'test-mission-1',
  version: 1,
  title: 'Test Plan',
  description: 'This is a test plan',
  status: 'active' as const,
  estimatedDuration: 60,
  actualDuration: null,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  metadata: {}
};

export const mockTask = {
  id: 'test-task-1',
  planId: 'test-plan-1',
  parentTaskId: null,
  title: 'Test Task',
  description: 'This is a test task',
  status: 'pending' as const,
  priority: 0,
  toolName: 'test-tool',
  toolParams: {},
  dependencies: [],
  estimatedDuration: 30,
  actualDuration: null,
  startedAt: null,
  completedAt: null,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
  result: null,
  metadata: {}
};

export const mockUser = {
  id: 'test-user-1',
  name: 'Test User',
  email: '<EMAIL>',
  preferences: {}
};

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  user?: typeof mockUser;
}

function AllTheProviders({ children }: { children: React.ReactNode }) {
  return (
    <div>
      {/* Add any global providers here if needed */}
      {children}
    </div>
  );
}

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  return render(ui, { wrapper: AllTheProviders, ...options });
};

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };

// User event utility
export const user = userEvent.setup();

// Common test utilities
export const TestUtils = {
  // Wait for element to appear
  async waitForElement(testId: string, timeout = 5000) {
    return waitFor(
      () => {
        const element = screen.getByTestId(testId);
        expect(element).toBeInTheDocument();
        return element;
      },
      { timeout }
    );
  },

  // Wait for element to disappear
  async waitForElementToBeRemoved(testId: string, timeout = 5000) {
    return waitFor(
      () => {
        expect(screen.queryByTestId(testId)).not.toBeInTheDocument();
      },
      { timeout }
    );
  },

  // Check accessibility
  async checkAccessibility(element: HTMLElement) {
    // Basic accessibility checks
    const hasAriaLabel = element.getAttribute('aria-label');
    const hasAriaLabelledBy = element.getAttribute('aria-labelledby');
    const hasRole = element.getAttribute('role');
    const isInteractive = ['button', 'a', 'input', 'select', 'textarea'].includes(
      element.tagName.toLowerCase()
    );

    if (isInteractive) {
      expect(hasAriaLabel || hasAriaLabelledBy || hasRole).toBeTruthy();
    }

    // Check focus management
    if (element.tabIndex >= 0) {
      element.focus();
      expect(document.activeElement).toBe(element);
    }
  },

  // Simulate keyboard navigation
  async navigateWithKeyboard(keys: string[]) {
    for (const key of keys) {
      await user.keyboard(key);
    }
  },

  // Mock API responses
  mockApiResponse(data: any, status = 200) {
    return {
      ok: status >= 200 && status < 300,
      status,
      json: async () => data,
      text: async () => JSON.stringify(data),
    };
  },

  // Create mock fetch
  createMockFetch(responses: Record<string, any>) {
    return jest.fn().mockImplementation((url: string) => {
      const response = responses[url];
      if (response) {
        return Promise.resolve(this.mockApiResponse(response));
      }
      return Promise.reject(new Error(`Unexpected request to ${url}`));
    });
  },

  // Simulate loading states
  async simulateLoading(element: HTMLElement, duration = 1000) {
    expect(element).toHaveAttribute('aria-busy', 'true');
    await waitFor(
      () => {
        expect(element).not.toHaveAttribute('aria-busy', 'true');
      },
      { timeout: duration + 1000 }
    );
  },

  // Test responsive behavior
  async testResponsive(breakpoints: Record<string, number>) {
    for (const [name, width] of Object.entries(breakpoints)) {
      // Mock window.innerWidth
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: width,
      });

      // Trigger resize event
      window.dispatchEvent(new Event('resize'));
      
      // Wait for any responsive changes
      await waitFor(() => {
        // Add specific assertions for each breakpoint
      });
    }
  }
};

// Mock WebSocket for testing real-time features
export class MockWebSocket {
  url: string;
  readyState: number = WebSocket.CONNECTING;
  onopen: ((event: Event) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;

  constructor(url: string) {
    this.url = url;
    
    // Simulate connection opening
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      this.onopen?.(new Event('open'));
    }, 100);
  }

  send(data: string) {
    if (this.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not open');
    }
    // In tests, we can manually trigger message events
  }

  close() {
    this.readyState = WebSocket.CLOSED;
    this.onclose?.(new CloseEvent('close'));
  }

  // Helper method for tests
  simulateMessage(data: any) {
    if (this.readyState === WebSocket.OPEN) {
      this.onmessage?.(new MessageEvent('message', { data: JSON.stringify(data) }));
    }
  }

  simulateError() {
    this.onerror?.(new Event('error'));
  }
}

// Agent testing utilities
export const AgentTestUtils = {
  // Mock DeepSeek client
  createMockDeepSeekClient() {
    return {
      chat: jest.fn().mockResolvedValue({
        choices: [
          {
            message: {
              content: JSON.stringify({
                title: 'Test Plan',
                description: 'Test plan description',
                tasks: [mockTask],
                confidence: 0.9
              })
            }
          }
        ]
      }),
      generateEmbedding: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
      generatePlan: jest.fn().mockResolvedValue(JSON.stringify(mockPlan)),
      reflect: jest.fn().mockResolvedValue(JSON.stringify({
        insights: ['Test insight'],
        recommendations: ['Test recommendation'],
        confidence: 0.8
      })),
      selectTool: jest.fn().mockResolvedValue(JSON.stringify({
        selectedTool: 'test-tool',
        reasoning: 'Test reasoning',
        confidence: 0.9
      })),
      assessCompletion: jest.fn().mockResolvedValue(JSON.stringify({
        completed: true,
        completionPercentage: 1.0,
        reasoning: 'Task completed successfully'
      }))
    };
  },

  // Mock tool registry
  createMockToolRegistry() {
    return {
      register: jest.fn(),
      unregister: jest.fn(),
      get: jest.fn().mockReturnValue({
        name: 'test-tool',
        description: 'Test tool',
        execute: jest.fn().mockResolvedValue({ success: true, data: 'test result' })
      }),
      list: jest.fn().mockReturnValue(['test-tool']),
      listNames: jest.fn().mockReturnValue(['test-tool'])
    };
  },

  // Mock mission lifecycle manager
  createMockLifecycleManager() {
    return {
      createMission: jest.fn().mockResolvedValue(mockMission),
      updateMissionStatus: jest.fn(),
      getMissionState: jest.fn().mockReturnValue({
        mission: mockMission,
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0.5,
        isRunning: true,
        isPaused: false,
        cancellationRequested: false
      }),
      requestCancellation: jest.fn(),
      requestPause: jest.fn(),
      requestResume: jest.fn(),
      cleanup: jest.fn(),
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn()
    };
  }
};

// Database testing utilities
export const DbTestUtils = {
  // Mock database
  createMockDb() {
    return {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue([]),
      get: jest.fn().mockResolvedValue(null),
      all: jest.fn().mockResolvedValue([]),
      run: jest.fn().mockResolvedValue({ changes: 1, lastInsertRowid: 1 })
    };
  },

  // Reset database for testing
  async resetDatabase(db: any) {
    // In tests, we would clear all tables
    const tables = ['missions', 'plans', 'tasks', 'execution_logs', 'reflections', 'tool_usage', 'knowledge_base'];
    for (const table of tables) {
      await db.delete().from(table).execute();
    }
  }
};

// Performance testing utilities
export const PerformanceTestUtils = {
  // Measure render time
  async measureRenderTime(component: ReactElement) {
    const start = performance.now();
    render(component);
    const end = performance.now();
    return end - start;
  },

  // Test memory leaks
  async testMemoryLeaks(component: ReactElement, iterations = 100) {
    const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    for (let i = 0; i < iterations; i++) {
      const { unmount } = render(component);
      unmount();
    }
    
    // Force garbage collection if available
    if ((global as any).gc) {
      (global as any).gc();
    }
    
    const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
    const memoryIncrease = finalMemory - initialMemory;
    
    // Memory increase should be minimal
    expect(memoryIncrease).toBeLessThan(1024 * 1024); // Less than 1MB
  }
};