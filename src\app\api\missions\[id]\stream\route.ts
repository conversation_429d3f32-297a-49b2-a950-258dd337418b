import { NextRequest, NextResponse } from 'next/server';
import { getWebSocketServer } from '@/lib/websocket/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const missionId = params.id;
  
  // Validate mission ID
  if (!missionId || missionId === 'undefined') {
    return new NextResponse('Mission ID is required', { status: 400 });
  }

  // TODO: Add mission existence validation
  // const mission = await getMission(missionId);
  // if (!mission) {
  //   return new NextResponse('Mission not found', { status: 404 });
  // }

  // Set up SSE headers
  const headers = new Headers({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache, no-transform',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
  });

  const encoder = new TextEncoder();
  
  const stream = new ReadableStream({
    start(controller) {
      console.log(`[SSE] Starting stream for mission: ${missionId}`);
      
      // Send initial connection event
      const connectionEvent = `data: ${JSON.stringify({
        type: 'connection.established',
        payload: { 
          missionId,
          clientId: `sse-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          timestamp: new Date().toISOString()
        },
        timestamp: new Date().toISOString(),
        missionId
      })}\n\n`;
      
      try {
        controller.enqueue(encoder.encode(connectionEvent));
      } catch (error) {
        console.error(`[SSE] Failed to send connection event:`, error);
        controller.error(error);
        return;
      }
      
      // Get WebSocket server instance to tap into existing events
      let wsServer: any = null;
      try {
        wsServer = getWebSocketServer();
      } catch (error) {
        console.warn(`[SSE] WebSocket server not available for mission ${missionId}:`, error);
        // Continue without WebSocket integration
      }
      
      // Helper function to send SSE message
      let sendSSEMessage = (type: string, payload: any, timestamp?: string) => {
        const message = {
          type,
          payload,
          timestamp: timestamp || new Date().toISOString(),
          missionId
        };
        
        const eventData = `data: ${JSON.stringify(message)}\n\n`;
        
        try {
          controller.enqueue(encoder.encode(eventData));
        } catch (error) {
          console.error(`[SSE] Failed to send ${type} message:`, error);
          // Connection likely closed, cleanup will be handled by abort
        }
      };
      
      // Mission update handler
      const handleMissionUpdate = (update: any) => {
        if (update.missionId === missionId) {
          console.log(`[SSE] Broadcasting mission update for ${missionId}`);
          sendSSEMessage('mission.update', update);
        }
      };
      
      // Task update handler
      const handleTaskUpdate = (update: any) => {
        if (update.missionId === missionId) {
          console.log(`[SSE] Broadcasting task update for ${missionId}`);
          sendSSEMessage('task.update', update);
        }
      };
      
      // Log update handler
      const handleLogUpdate = (update: any) => {
        if (update.missionId === missionId) {
          console.log(`[SSE] Broadcasting log update for ${missionId}`);
          sendSSEMessage('log.update', update);
        }
      };
      
      // Reflection update handler
      const handleReflectionUpdate = (update: any) => {
        if (update.missionId === missionId) {
          console.log(`[SSE] Broadcasting reflection update for ${missionId}`);
          sendSSEMessage('reflection.update', update);
        }
      };
      
      // Error handler
      const handleError = (error: any) => {
        console.error(`[SSE] Server error for mission ${missionId}:`, error);
        sendSSEMessage('error', { 
          message: error.message || 'Unknown server error',
          code: error.code || 'UNKNOWN_ERROR'
        });
      };
      
      // Subscribe to WebSocket events if available
      if (wsServer) {
        try {
          wsServer.on('mission.update', handleMissionUpdate);
          wsServer.on('task.update', handleTaskUpdate);
          wsServer.on('log.update', handleLogUpdate);
          wsServer.on('reflection.update', handleReflectionUpdate);
          wsServer.on('error', handleError);
          
          console.log(`[SSE] Subscribed to WebSocket events for mission ${missionId}`);
        } catch (error) {
          console.warn(`[SSE] Failed to subscribe to WebSocket events:`, error);
        }
      }
      
      // Cleanup function
      const cleanup = () => {
        console.log(`[SSE] Cleaning up stream for mission: ${missionId}`);
        
        if (wsServer) {
          try {
            wsServer.off('mission.update', handleMissionUpdate);
            wsServer.off('task.update', handleTaskUpdate);
            wsServer.off('log.update', handleLogUpdate);
            wsServer.off('reflection.update', handleReflectionUpdate);
            wsServer.off('error', handleError);
          } catch (error) {
            console.warn(`[SSE] Error during cleanup:`, error);
          }
        }
        
        if (keepAliveInterval) {
          clearInterval(keepAliveInterval);
        }
        
        if (heartbeatTimeout) {
          clearTimeout(heartbeatTimeout);
        }
      };
      
      // Store cleanup function for later use
      (controller as any).cleanup = cleanup;
      
      // Keep-alive ping every 30 seconds
      const keepAliveInterval = setInterval(() => {
        try {
          sendSSEMessage('ping', { timestamp: new Date().toISOString() });
        } catch (error) {
          console.error(`[SSE] Failed to send keep-alive ping:`, error);
          cleanup();
        }
      }, 30000);
      
      // Heartbeat timeout (if no activity for 2 minutes, consider connection stale)
      let heartbeatTimeout = setTimeout(() => {
        console.warn(`[SSE] No activity for 2 minutes, closing connection for mission ${missionId}`);
        try {
          sendSSEMessage('error', { 
            message: 'Connection timeout due to inactivity',
            code: 'HEARTBEAT_TIMEOUT'
          });
        } catch (error) {
          // Ignore send errors during cleanup
        }
        cleanup();
        controller.close();
      }, 120000); // 2 minutes
      
      // Reset heartbeat on any message
      const resetHeartbeat = () => {
        if (heartbeatTimeout) {
          clearTimeout(heartbeatTimeout);
        }
        heartbeatTimeout = setTimeout(() => {
          console.warn(`[SSE] No activity for 2 minutes, closing connection for mission ${missionId}`);
          cleanup();
          controller.close();
        }, 120000);
      };
      
      // Reset heartbeat when sending messages
      const originalSendSSEMessage = sendSSEMessage;
      sendSSEMessage = (type: string, payload: any, timestamp?: string) => {
        originalSendSSEMessage(type, payload, timestamp);
        resetHeartbeat();
      };
      
      // Store intervals for cleanup
      (controller as any).keepAliveInterval = keepAliveInterval;
      (controller as any).heartbeatTimeout = heartbeatTimeout;
      
      // Send initial heartbeat
      resetHeartbeat();
      
      console.log(`[SSE] Stream initialized for mission: ${missionId}`);
    },
    
    cancel(reason) {
      console.log(`[SSE] Stream cancelled for mission: ${missionId}`, reason);
      
      // Cleanup when client disconnects
      if ((this as any).cleanup) {
        (this as any).cleanup();
      }
    }
  });

  return new NextResponse(stream, { headers });
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Cache-Control',
    },
  });
} 