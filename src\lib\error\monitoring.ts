import { EventEmitter } from 'events';
import { errorHandler } from './error-handler';
import { ErrorSeverity, ErrorCategory, ServiceError, HealthCheckResult } from './types';

/**
 * Error monitoring and alerting system
 */
export class ErrorMonitor extends EventEmitter {
  private static instance: ErrorMonitor;
  private healthChecks: Map<string, HealthCheckResult> = new Map();
  private alertRules: AlertRule[] = [];
  private lastAlerts: Map<string, Date> = new Map();

  private constructor() {
    super();
    this.setupErrorListener();
    this.setupPeriodicHealthChecks();
  }

  static getInstance(): ErrorMonitor {
    if (!ErrorMonitor.instance) {
      ErrorMonitor.instance = new ErrorMonitor();
    }
    return ErrorMonitor.instance;
  }

  /**
   * Add health check for a service
   */
  addHealthCheck(
    serviceName: string, 
    checkFunction: () => Promise<any>,
    interval: number = 60000 // Default 1 minute
  ): void {
    // Perform initial health check
    this.performHealthCheck(serviceName, checkFunction);

    // Set up periodic health checks
    setInterval(() => {
      this.performHealthCheck(serviceName, checkFunction);
    }, interval);
  }

  /**
   * Add alert rule
   */
  addAlertRule(rule: AlertRule): void {
    this.alertRules.push(rule);
  }

  /**
   * Get current system health status
   */
  getSystemHealth(): SystemHealthStatus {
    const healthChecks = Array.from(this.healthChecks.values());
    const errorMetrics = errorHandler.getErrorMetrics();
    const circuitBreakers = errorHandler.getCircuitBreakerStates();

    const unhealthyServices = healthChecks.filter(hc => hc.status === 'unhealthy').length;
    const degradedServices = healthChecks.filter(hc => hc.status === 'degraded').length;
    const openCircuitBreakers = Array.from(circuitBreakers.values()).filter(cb => cb.state === 'open').length;
    
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy';
    
    if (unhealthyServices > 0 || openCircuitBreakers > 0) {
      overallStatus = 'unhealthy';
    } else if (degradedServices > 0) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'healthy';
    }

    const criticalErrors = Array.from(errorMetrics.values())
      .filter(metric => metric.severity === ErrorSeverity.CRITICAL)
      .reduce((sum, metric) => sum + metric.count, 0);

    const recentErrors = Array.from(errorMetrics.values())
      .filter(metric => {
        const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
        return metric.lastOccurrence > tenMinutesAgo;
      })
      .reduce((sum, metric) => sum + metric.count, 0);

    return {
      status: overallStatus,
      timestamp: new Date(),
      services: healthChecks,
      errorCounts: {
        critical: criticalErrors,
        recent: recentErrors,
        total: Array.from(errorMetrics.values()).reduce((sum, metric) => sum + metric.count, 0)
      },
      circuitBreakers: {
        open: openCircuitBreakers,
        total: circuitBreakers.size
      }
    };
  }

  /**
   * Get error trends for dashboard
   */
  getErrorTrends(): ErrorTrend[] {
    const errorMetrics = errorHandler.getErrorMetrics();
    const trends: ErrorTrend[] = [];

    for (const [key, metrics] of errorMetrics.entries()) {
      trends.push({
        category: metrics.category,
        severity: metrics.severity,
        count: metrics.count,
        lastOccurrence: metrics.lastOccurrence,
        trend: this.calculateTrend(key, metrics.count)
      });
    }

    return trends.sort((a, b) => b.count - a.count);
  }

  /**
   * Generate error report
   */
  generateErrorReport(timeRange: { start: Date; end: Date }): ErrorReport {
    const errorMetrics = errorHandler.getErrorMetrics();
    const healthChecks = Array.from(this.healthChecks.values());
    
    const errorsInRange = Array.from(errorMetrics.values())
      .filter(metric => 
        metric.lastOccurrence >= timeRange.start && 
        metric.lastOccurrence <= timeRange.end
      );

    const errorsByCategory = this.groupErrorsByCategory(errorsInRange);
    const errorsBySeverity = this.groupErrorsBySeverity(errorsInRange);
    
    const totalErrors = errorsInRange.reduce((sum, metric) => sum + metric.count, 0);
    const criticalErrors = errorsInRange
      .filter(metric => metric.severity === ErrorSeverity.CRITICAL)
      .reduce((sum, metric) => sum + metric.count, 0);

    return {
      timeRange,
      summary: {
        totalErrors,
        criticalErrors,
        affectedServices: new Set(healthChecks.filter(hc => hc.status !== 'healthy').map(hc => hc.service)).size,
        uptime: this.calculateUptime(timeRange)
      },
      errorsByCategory,
      errorsBySeverity,
      topErrors: errorsInRange
        .sort((a, b) => b.count - a.count)
        .slice(0, 10),
      recommendations: this.generateRecommendations(errorsInRange)
    };
  }

  // Private methods

  private setupErrorListener(): void {
    errorHandler.on('error', (error: ServiceError) => {
      this.processError(error);
    });

    errorHandler.on('fallback-success', (event: any) => {
      console.log('🔄 Fallback successful:', event);
    });

    errorHandler.on('fallback-failed', (event: any) => {
      console.error('❌ Fallback failed:', event);
      this.checkAlertRules({
        type: 'fallback_failed',
        serviceName: event.serviceName,
        timestamp: event.timestamp
      });
    });
  }

  private setupPeriodicHealthChecks(): void {
    // Add default health checks
    this.addHealthCheck('database', async () => {
      // Simple database health check
      const db = (await import('../db')).default;
      return db.run('SELECT 1');
    });

    this.addHealthCheck('cache', async () => {
      const { cacheManager } = await import('../cache/cache-manager');
      const stats = cacheManager.getStatistics();
      if (!stats.totalKeys) {
        throw new Error('Cache not initialized');
      }
      return stats;
    });

    this.addHealthCheck('websocket', async () => {
      const { getWebSocketServer } = await import('../websocket/server');
      const server = getWebSocketServer();
      return { connected: true };
    });
  }

  private async performHealthCheck(serviceName: string, checkFunction: () => Promise<any>): Promise<void> {
    try {
      const result = await errorHandler.healthCheck(serviceName, checkFunction);
      this.healthChecks.set(serviceName, result);
      
      if (result.status !== 'healthy') {
        this.checkAlertRules({
          type: 'health_check_failed',
          serviceName,
          status: result.status,
          error: result.error,
          timestamp: result.timestamp
        });
      }
    } catch (error) {
      console.error(`Health check failed for ${serviceName}:`, error);
    }
  }

  private processError(error: ServiceError): void {
    // Check if this error should trigger alerts
    this.checkAlertRules({
      type: 'error_occurred',
      error,
      timestamp: error.context.timestamp
    });

    // Log error based on severity
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('🚨 CRITICAL ERROR:', error.technicalMessage, error.context);
        break;
      case ErrorSeverity.HIGH:
        console.error('⚠️  HIGH SEVERITY ERROR:', error.technicalMessage);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('⚠️  WARNING:', error.technicalMessage);
        break;
      case ErrorSeverity.LOW:
        console.log('ℹ️  INFO:', error.technicalMessage);
        break;
    }
  }

  private checkAlertRules(event: AlertEvent): void {
    for (const rule of this.alertRules) {
      if (this.shouldTriggerAlert(rule, event)) {
        this.triggerAlert(rule, event);
      }
    }
  }

  private shouldTriggerAlert(rule: AlertRule, event: AlertEvent): boolean {
    // Check rate limiting
    const lastAlert = this.lastAlerts.get(rule.id);
    if (lastAlert && (Date.now() - lastAlert.getTime()) < rule.cooldownMs) {
      return false;
    }

    // Check conditions
    return rule.condition(event);
  }

  private triggerAlert(rule: AlertRule, event: AlertEvent): void {
    console.log(`🚨 ALERT: ${rule.name} - ${rule.description}`);
    
    this.lastAlerts.set(rule.id, new Date());
    
    this.emit('alert', {
      rule,
      event,
      timestamp: new Date()
    });
  }

  private calculateTrend(key: string, currentCount: number): 'increasing' | 'decreasing' | 'stable' {
    // Simple trend calculation - in a real implementation, you'd store historical data
    return 'stable';
  }

  private groupErrorsByCategory(errors: any[]): Record<string, number> {
    const grouped: Record<string, number> = {};
    
    for (const error of errors) {
      grouped[error.category] = (grouped[error.category] || 0) + error.count;
    }
    
    return grouped;
  }

  private groupErrorsBySeverity(errors: any[]): Record<string, number> {
    const grouped: Record<string, number> = {};
    
    for (const error of errors) {
      grouped[error.severity] = (grouped[error.severity] || 0) + error.count;
    }
    
    return grouped;
  }

  private calculateUptime(timeRange: { start: Date; end: Date }): number {
    // Simple uptime calculation - would be more sophisticated in production
    const totalTime = timeRange.end.getTime() - timeRange.start.getTime();
    const downtime = 0; // Calculate based on error logs
    return ((totalTime - downtime) / totalTime) * 100;
  }

  private generateRecommendations(errors: any[]): string[] {
    const recommendations: string[] = [];
    
    const criticalErrors = errors.filter(e => e.severity === ErrorSeverity.CRITICAL);
    if (criticalErrors.length > 0) {
      recommendations.push('Address critical errors immediately to prevent system instability');
    }
    
    const networkErrors = errors.filter(e => e.category === ErrorCategory.NETWORK);
    if (networkErrors.length > 5) {
      recommendations.push('High number of network errors detected - check network infrastructure');
    }
    
    const apiErrors = errors.filter(e => e.category === ErrorCategory.API);
    if (apiErrors.length > 10) {
      recommendations.push('Consider implementing additional API resilience patterns');
    }
    
    return recommendations;
  }
}

// Types for monitoring
interface AlertRule {
  id: string;
  name: string;
  description: string;
  condition: (event: AlertEvent) => boolean;
  cooldownMs: number;
}

interface AlertEvent {
  type: string;
  timestamp: Date;
  [key: string]: any;
}

interface SystemHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  services: HealthCheckResult[];
  errorCounts: {
    critical: number;
    recent: number;
    total: number;
  };
  circuitBreakers: {
    open: number;
    total: number;
  };
}

interface ErrorTrend {
  category: ErrorCategory;
  severity: ErrorSeverity;
  count: number;
  lastOccurrence: Date;
  trend: 'increasing' | 'decreasing' | 'stable';
}

interface ErrorReport {
  timeRange: { start: Date; end: Date };
  summary: {
    totalErrors: number;
    criticalErrors: number;
    affectedServices: number;
    uptime: number;
  };
  errorsByCategory: Record<string, number>;
  errorsBySeverity: Record<string, number>;
  topErrors: any[];
  recommendations: string[];
}

// Export singleton instance
export const errorMonitor = ErrorMonitor.getInstance();

// Export default alert rules
export const defaultAlertRules: AlertRule[] = [
  {
    id: 'critical-error',
    name: 'Critical Error Alert',
    description: 'Triggered when a critical error occurs',
    condition: (event) => event.type === 'error_occurred' && event.error?.severity === ErrorSeverity.CRITICAL,
    cooldownMs: 60000 // 1 minute
  },
  {
    id: 'service-down',
    name: 'Service Down Alert',
    description: 'Triggered when a service health check fails',
    condition: (event) => event.type === 'health_check_failed' && event.status === 'unhealthy',
    cooldownMs: 300000 // 5 minutes
  },
  {
    id: 'fallback-failed',
    name: 'Fallback Failed Alert',
    description: 'Triggered when both primary and fallback operations fail',
    condition: (event) => event.type === 'fallback_failed',
    cooldownMs: 600000 // 10 minutes
  }
];