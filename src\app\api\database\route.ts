import { NextRequest, NextResponse } from 'next/server';
import { databaseOptimizer, analyzePerformance, optimizeDatabase } from '@/lib/db/indexes';
import { errorHandler } from '@/lib/error';

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action') || 'status';

    switch (action) {
      case 'status':
        const analysis = await analyzePerformance();
        
        return NextResponse.json({
          status: 'healthy',
          timestamp: new Date(),
          analysis: {
            tableStats: Array.from(analysis.tableStats.entries()).map(([table, stats]) => ({
              table,
              ...stats
            })),
            recommendations: analysis.recommendations,
            slowQueries: analysis.slowQueries
          }
        });

      case 'health':
        // Perform a simple health check
        const db = (await import('@/lib/db')).default;
        const healthCheck = await db.get('SELECT 1 as test');
        
        return NextResponse.json({
          status: healthCheck ? 'healthy' : 'unhealthy',
          timestamp: new Date(),
          connection: healthCheck ? 'connected' : 'disconnected'
        });

      default:
        return NextResponse.json({ error: 'Invalid action parameter' }, { status: 400 });
    }
  } catch (error) {
    console.error('Database status endpoint error:', error);
    return NextResponse.json(
      { error: 'Failed to get database status' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'optimize':
        await optimizeDatabase();
        return NextResponse.json({ 
          message: 'Database optimization completed',
          timestamp: new Date()
        });

      case 'create-indexes':
        await databaseOptimizer.createIndexes();
        return NextResponse.json({ 
          message: 'Database indexes created successfully',
          timestamp: new Date()
        });

      case 'analyze':
        const analysis = await analyzePerformance();
        return NextResponse.json({
          message: 'Database analysis completed',
          analysis: {
            tableStats: Array.from(analysis.tableStats.entries()).map(([table, stats]) => ({
              table,
              ...stats
            })),
            recommendations: analysis.recommendations,
            slowQueries: analysis.slowQueries
          },
          timestamp: new Date()
        });

      case 'vacuum':
        const db = (await import('@/lib/db')).default;
        await errorHandler.withRetry(
          async () => {
            await db.run('VACUUM');
            await db.run('PRAGMA optimize');
          },
          'database-vacuum',
          {
            maxAttempts: 2,
            retryCondition: (error) => error.message.includes('database is locked')
          }
        );
        
        return NextResponse.json({ 
          message: 'Database vacuum completed',
          timestamp: new Date()
        });

      case 'update-fts':
        // Update full-text search indexes
        const database = (await import('@/lib/db')).default;
        
        await errorHandler.withRetry(
          async () => {
            // Update knowledge base FTS
            await database.run(`
              INSERT OR REPLACE INTO knowledge_base_fts(id, title, content, summary)
              SELECT id, title, content, summary FROM knowledge_base
            `);

            // Update missions FTS
            await database.run(`
              INSERT OR REPLACE INTO missions_fts(id, title, description)
              SELECT id, title, description FROM missions
            `);
          },
          'database-fts-update',
          { maxAttempts: 2 }
        );
        
        return NextResponse.json({ 
          message: 'Full-text search indexes updated',
          timestamp: new Date()
        });

      case 'settings':
        await databaseOptimizer.optimizeSettings();
        return NextResponse.json({ 
          message: 'Database settings optimized',
          timestamp: new Date()
        });

      case 'maintenance':
        // Comprehensive maintenance routine
        console.log('🔧 Starting database maintenance...');
        
        // 1. Analyze current state
        const preAnalysis = await analyzePerformance();
        
        // 2. Optimize settings
        await databaseOptimizer.optimizeSettings();
        
        // 3. Update FTS indexes
        const db2 = (await import('@/lib/db')).default;
        await db2.run(`
          INSERT OR REPLACE INTO knowledge_base_fts(id, title, content, summary)
          SELECT id, title, content, summary FROM knowledge_base
        `);
        await db2.run(`
          INSERT OR REPLACE INTO missions_fts(id, title, description)
          SELECT id, title, description FROM missions
        `);
        
        // 4. Vacuum if needed
        const totalRows = Array.from(preAnalysis.tableStats.values())
          .reduce((sum, stats) => sum + stats.rowCount, 0);
        
        if (totalRows > 10000) {
          await optimizeDatabase();
        }
        
        // 5. Final analysis
        const postAnalysis = await analyzePerformance();
        
        return NextResponse.json({
          message: 'Database maintenance completed',
          maintenance: {
            before: {
              totalRows,
              recommendations: preAnalysis.recommendations.length
            },
            after: {
              totalRows: Array.from(postAnalysis.tableStats.values())
                .reduce((sum, stats) => sum + stats.rowCount, 0),
              recommendations: postAnalysis.recommendations.length
            },
            actions: [
              'Settings optimized',
              'FTS indexes updated',
              totalRows > 10000 ? 'Database vacuumed' : 'Vacuum skipped (not needed)',
              'Analysis completed'
            ]
          },
          timestamp: new Date()
        });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Database operation error:', error);
    const serviceError = errorHandler.createError(
      `Database operation failed: ${error.message}`,
      'database',
      'high',
      'DATABASE_OPERATION_FAILED',
      { action: body?.action },
      error as Error
    );
    
    return NextResponse.json(
      { 
        error: serviceError.userMessage,
        technical: serviceError.technicalMessage,
        code: serviceError.code
      },
      { status: 500 }
    );
  }
}