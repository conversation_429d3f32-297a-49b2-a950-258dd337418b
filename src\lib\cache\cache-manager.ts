import NodeCache from 'node-cache';
import crypto from 'crypto';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  checkPeriod?: number; // Check period for expired keys in seconds
  useClones?: boolean; // Whether to clone objects
}

export interface CacheStatistics {
  hits: number;
  misses: number;
  keys: number;
  ksize: number;
  vsize: number;
}

/**
 * Comprehensive caching service for the AI assistant
 * Provides multiple cache categories with different TTL and optimization strategies
 */
export class CacheManager {
  private static instance: CacheManager | null = null;

  // Different cache stores for different types of data
  private embeddingCache: NodeCache;      // Long-term cache for embeddings
  private searchCache: NodeCache;         // Medium-term cache for search results
  private sessionCache: NodeCache;       // Short-term cache for session data
  private apiCache: NodeCache;           // Medium-term cache for API responses
  private documentCache: NodeCache;      // Long-term cache for processed documents
  private completionCache: NodeCache;    // Medium-term cache for AI completions

  private isInitialized = false;

  private constructor() {
    // Initialize cache stores with different configurations
    this.embeddingCache = new NodeCache({
      stdTTL: 24 * 60 * 60, // 24 hours
      checkperiod: 60 * 60, // Check every hour
      useClones: false,     // Don't clone for performance
      maxKeys: 10000,       // Limit for memory management
    });

    this.searchCache = new NodeCache({
      stdTTL: 30 * 60,      // 30 minutes
      checkperiod: 5 * 60,  // Check every 5 minutes
      useClones: true,      // Clone to prevent mutations
      maxKeys: 1000,
    });

    this.sessionCache = new NodeCache({
      stdTTL: 60 * 60,      // 1 hour
      checkperiod: 10 * 60, // Check every 10 minutes
      useClones: true,
      maxKeys: 5000,
    });

    this.apiCache = new NodeCache({
      stdTTL: 15 * 60,      // 15 minutes
      checkperiod: 5 * 60,  // Check every 5 minutes
      useClones: true,
      maxKeys: 2000,
    });

    this.documentCache = new NodeCache({
      stdTTL: 12 * 60 * 60, // 12 hours
      checkperiod: 60 * 60, // Check every hour
      useClones: false,     // Documents are large, don't clone
      maxKeys: 1000,
    });

    this.completionCache = new NodeCache({
      stdTTL: 2 * 60 * 60,  // 2 hours
      checkperiod: 30 * 60, // Check every 30 minutes
      useClones: true,
      maxKeys: 2000,
    });

    this.setupEventListeners();
  }

  /**
   * Get singleton instance
   */
  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Initialize the cache manager
   */
  initialize(): void {
    if (this.isInitialized) return;

    console.log('🚀 Cache Manager initialized with multiple stores');
    this.isInitialized = true;
  }

  /**
   * Generate a cache key from input parameters
   */
  private generateKey(prefix: string, ...params: any[]): string {
    const data = JSON.stringify(params);
    const hash = crypto.createHash('md5').update(data).digest('hex');
    return `${prefix}:${hash}`;
  }

  // ============ EMBEDDING CACHE ============
  
  /**
   * Cache embedding for text
   */
  setEmbedding(text: string, embedding: number[], model: string = 'default'): void {
    const key = this.generateKey('embedding', text, model);
    this.embeddingCache.set(key, embedding);
  }

  /**
   * Get cached embedding for text
   */
  getEmbedding(text: string, model: string = 'default'): number[] | null {
    const key = this.generateKey('embedding', text, model);
    return this.embeddingCache.get<number[]>(key) || null;
  }

  // ============ SEARCH CACHE ============

  /**
   * Cache search results
   */
  setSearchResults(query: string, options: any, results: any[]): void {
    const key = this.generateKey('search', query, options);
    this.searchCache.set(key, {
      results,
      timestamp: Date.now(),
      query,
      options,
    });
  }

  /**
   * Get cached search results
   */
  getSearchResults(query: string, options: any): any[] | null {
    const key = this.generateKey('search', query, options);
    const cached = this.searchCache.get<{
      results: any[];
      timestamp: number;
      query: string;
      options: any;
    }>(key);
    
    return cached ? cached.results : null;
  }

  // ============ SESSION CACHE ============

  /**
   * Cache session data
   */
  setSessionData(sessionId: string, key: string, data: any, ttl?: number): void {
    const cacheKey = this.generateKey('session', sessionId, key);
    this.sessionCache.set(cacheKey, data, ttl);
  }

  /**
   * Get cached session data
   */
  getSessionData<T>(sessionId: string, key: string): T | null {
    const cacheKey = this.generateKey('session', sessionId, key);
    return this.sessionCache.get<T>(cacheKey) || null;
  }

  /**
   * Delete session data
   */
  deleteSessionData(sessionId: string, key?: string): void {
    if (key) {
      const cacheKey = this.generateKey('session', sessionId, key);
      this.sessionCache.del(cacheKey);
    } else {
      // Delete all session data
      const sessionPrefix = this.generateKey('session', sessionId, '').slice(0, -33); // Remove hash part
      const keys = this.sessionCache.keys().filter(k => k.startsWith(sessionPrefix));
      this.sessionCache.del(keys);
    }
  }

  // ============ API CACHE ============

  /**
   * Cache API response
   */
  setApiResponse(endpoint: string, params: any, response: any, ttl?: number): void {
    const key = this.generateKey('api', endpoint, params);
    this.apiCache.set(key, {
      response,
      timestamp: Date.now(),
      endpoint,
      params,
    }, ttl);
  }

  /**
   * Get cached API response
   */
  getApiResponse<T>(endpoint: string, params: any): T | null {
    const key = this.generateKey('api', endpoint, params);
    const cached = this.apiCache.get<{
      response: T;
      timestamp: number;
      endpoint: string;
      params: any;
    }>(key);
    
    return cached ? cached.response : null;
  }

  // ============ DOCUMENT CACHE ============

  /**
   * Cache processed document
   */
  setDocument(documentId: string, document: any, ttl?: number): void {
    const key = this.generateKey('document', documentId);
    this.documentCache.set(key, {
      document,
      timestamp: Date.now(),
      documentId,
    }, ttl);
  }

  /**
   * Get cached document
   */
  getDocument<T>(documentId: string): T | null {
    const key = this.generateKey('document', documentId);
    const cached = this.documentCache.get<{
      document: T;
      timestamp: number;
      documentId: string;
    }>(key);
    
    return cached ? cached.document : null;
  }

  // ============ COMPLETION CACHE ============

  /**
   * Cache AI completion
   */
  setCompletion(prompt: string, model: string, completion: any, ttl?: number): void {
    const key = this.generateKey('completion', prompt, model);
    this.completionCache.set(key, {
      completion,
      timestamp: Date.now(),
      prompt,
      model,
    }, ttl);
  }

  /**
   * Get cached completion
   */
  getCompletion<T>(prompt: string, model: string): T | null {
    const key = this.generateKey('completion', prompt, model);
    const cached = this.completionCache.get<{
      completion: T;
      timestamp: number;
      prompt: string;
      model: string;
    }>(key);
    
    return cached ? cached.completion : null;
  }

  // ============ CACHE MANAGEMENT ============

  /**
   * Get cache statistics
   */
  getStatistics(): {
    embedding: CacheStatistics;
    search: CacheStatistics;
    session: CacheStatistics;
    api: CacheStatistics;
    document: CacheStatistics;
    completion: CacheStatistics;
    total: {
      keys: number;
      memoryUsage: string;
    };
  } {
    const getCacheStats = (cache: NodeCache): CacheStatistics => {
      const stats = cache.getStats();
      return {
        hits: stats.hits,
        misses: stats.misses,
        keys: stats.keys,
        ksize: stats.ksize,
        vsize: stats.vsize,
      };
    };

    const embeddingStats = getCacheStats(this.embeddingCache);
    const searchStats = getCacheStats(this.searchCache);
    const sessionStats = getCacheStats(this.sessionCache);
    const apiStats = getCacheStats(this.apiCache);
    const documentStats = getCacheStats(this.documentCache);
    const completionStats = getCacheStats(this.completionCache);

    const totalKeys = embeddingStats.keys + searchStats.keys + sessionStats.keys + 
                     apiStats.keys + documentStats.keys + completionStats.keys;
    
    const totalMemory = embeddingStats.vsize + searchStats.vsize + sessionStats.vsize + 
                       apiStats.vsize + documentStats.vsize + completionStats.vsize;

    return {
      embedding: embeddingStats,
      search: searchStats,
      session: sessionStats,
      api: apiStats,
      document: documentStats,
      completion: completionStats,
      total: {
        keys: totalKeys,
        memoryUsage: this.formatBytes(totalMemory),
      },
    };
  }

  /**
   * Clear specific cache
   */
  clearCache(cacheType: 'embedding' | 'search' | 'session' | 'api' | 'document' | 'completion' | 'all'): void {
    switch (cacheType) {
      case 'embedding':
        this.embeddingCache.flushAll();
        break;
      case 'search':
        this.searchCache.flushAll();
        break;
      case 'session':
        this.sessionCache.flushAll();
        break;
      case 'api':
        this.apiCache.flushAll();
        break;
      case 'document':
        this.documentCache.flushAll();
        break;
      case 'completion':
        this.completionCache.flushAll();
        break;
      case 'all':
        this.embeddingCache.flushAll();
        this.searchCache.flushAll();
        this.sessionCache.flushAll();
        this.apiCache.flushAll();
        this.documentCache.flushAll();
        this.completionCache.flushAll();
        break;
    }
    
    console.log(`🗑️ Cleared ${cacheType} cache`);
  }

  /**
   * Get cache hit rate
   */
  getHitRate(): {
    overall: number;
    byCache: Record<string, number>;
  } {
    const stats = this.getStatistics();
    
    const calculateHitRate = (cache: CacheStatistics): number => {
      const total = cache.hits + cache.misses;
      return total > 0 ? (cache.hits / total) * 100 : 0;
    };

    return {
      overall: (
        (stats.embedding.hits + stats.search.hits + stats.session.hits + 
         stats.api.hits + stats.document.hits + stats.completion.hits) /
        (stats.embedding.hits + stats.embedding.misses + 
         stats.search.hits + stats.search.misses +
         stats.session.hits + stats.session.misses +
         stats.api.hits + stats.api.misses +
         stats.document.hits + stats.document.misses +
         stats.completion.hits + stats.completion.misses)
      ) * 100,
      byCache: {
        embedding: calculateHitRate(stats.embedding),
        search: calculateHitRate(stats.search),
        session: calculateHitRate(stats.session),
        api: calculateHitRate(stats.api),
        document: calculateHitRate(stats.document),
        completion: calculateHitRate(stats.completion),
      },
    };
  }

  /**
   * Cache cleanup and optimization
   */
  async optimizeCache(): Promise<{
    cleared: number;
    optimized: string[];
  }> {
    let totalCleared = 0;
    const optimized: string[] = [];

    // Get stats before optimization
    const statsBefore = this.getStatistics();

    // Clear expired keys and optimize memory usage
    const caches = [
      { name: 'embedding', cache: this.embeddingCache },
      { name: 'search', cache: this.searchCache },
      { name: 'session', cache: this.sessionCache },
      { name: 'api', cache: this.apiCache },
      { name: 'document', cache: this.documentCache },
      { name: 'completion', cache: this.completionCache },
    ];

    for (const { name, cache } of caches) {
      const keysBefore = cache.keys().length;
      
      // Force garbage collection of expired keys
      cache.keys().forEach(key => {
        if (!cache.get(key)) {
          cache.del(key);
        }
      });

      const keysAfter = cache.keys().length;
      const cleared = keysBefore - keysAfter;
      
      if (cleared > 0) {
        totalCleared += cleared;
        optimized.push(`${name}: ${cleared} expired keys removed`);
      }
    }

    console.log(`🧹 Cache optimization completed: ${totalCleared} keys cleared`);
    
    return {
      cleared: totalCleared,
      optimized,
    };
  }

  /**
   * Setup event listeners for cache events
   */
  private setupEventListeners(): void {
    const setupCacheEvents = (cacheName: string, cache: NodeCache) => {
      cache.on('set', (key: string, value: any) => {
        console.log(`📦 Cache SET [${cacheName}]: ${key}`);
      });

      cache.on('del', (key: string, value: any) => {
        console.log(`🗑️ Cache DEL [${cacheName}]: ${key}`);
      });

      cache.on('expired', (key: string, value: any) => {
        console.log(`⏰ Cache EXPIRED [${cacheName}]: ${key}`);
      });

      cache.on('flush', () => {
        console.log(`🚿 Cache FLUSH [${cacheName}]`);
      });
    };

    // Only enable verbose logging in development
    if (process.env.NODE_ENV === 'development') {
      setupCacheEvents('embedding', this.embeddingCache);
      setupCacheEvents('search', this.searchCache);
      setupCacheEvents('session', this.sessionCache);
      setupCacheEvents('api', this.apiCache);
      setupCacheEvents('document', this.documentCache);
      setupCacheEvents('completion', this.completionCache);
    }
  }

  /**
   * Format bytes to human-readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Health check for cache system
   */
  healthCheck(): {
    healthy: boolean;
    issues: string[];
    stats: any;
  } {
    const issues: string[] = [];
    const stats = this.getStatistics();
    
    // Check for potential issues
    if (stats.total.keys > 20000) {
      issues.push('High number of cached keys, consider cleanup');
    }

    // Check hit rates
    const hitRates = this.getHitRate();
    if (hitRates.overall < 30) {
      issues.push('Low cache hit rate, check caching strategy');
    }

    // Check memory usage (simplified check)
    const totalMemoryKB = (stats.embedding.vsize + stats.search.vsize + 
                          stats.session.vsize + stats.api.vsize + 
                          stats.document.vsize + stats.completion.vsize) / 1024;
    
    if (totalMemoryKB > 100 * 1024) { // 100MB
      issues.push('High memory usage, consider reducing cache size');
    }

    return {
      healthy: issues.length === 0,
      issues,
      stats: {
        ...stats,
        hitRates,
      },
    };
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance();