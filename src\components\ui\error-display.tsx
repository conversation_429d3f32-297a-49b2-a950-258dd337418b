import * as React from 'react';
import { cn } from '@/lib/utils';
import { Button } from './enhanced-button';
import { useErrorAnnouncements } from '@/lib/ui/accessibility';
import { AlertTriangle, RefreshCw, X, Info, AlertCircle, CheckCircle } from 'lucide-react';

interface ErrorDisplayProps {
  error?: string | Error | null;
  title?: string;
  description?: string;
  variant?: 'error' | 'warning' | 'info' | 'success';
  size?: 'sm' | 'md' | 'lg';
  onRetry?: () => void;
  onDismiss?: () => void;
  retryText?: string;
  dismissText?: string;
  className?: string;
  showIcon?: boolean;
  actions?: React.ReactNode;
}

export function ErrorDisplay({
  error,
  title,
  description,
  variant = 'error',
  size = 'md',
  onRetry,
  onDismiss,
  retryText = 'Try again',
  dismissText = 'Dismiss',
  className,
  showIcon = true,
  actions
}: ErrorDisplayProps) {
  const { announceError } = useErrorAnnouncements();
  
  // Announce error to screen readers when it changes
  React.useEffect(() => {
    if (error && variant === 'error') {
      const message = error instanceof Error ? error.message : String(error);
      announceError(message);
    }
  }, [error, variant, announceError]);
  
  if (!error && !title) return null;
  
  const errorMessage = error instanceof Error ? error.message : String(error);
  
  const icons = {
    error: AlertCircle,
    warning: AlertTriangle,
    info: Info,
    success: CheckCircle
  };
  
  const Icon = icons[variant];
  
  const variantStyles = {
    error: 'border-destructive/50 bg-destructive/10 text-destructive',
    warning: 'border-yellow-500/50 bg-yellow-50 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200',
    info: 'border-blue-500/50 bg-blue-50 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200',
    success: 'border-green-500/50 bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-200'
  };
  
  const iconStyles = {
    error: 'text-destructive',
    warning: 'text-yellow-600 dark:text-yellow-400',
    info: 'text-blue-600 dark:text-blue-400',
    success: 'text-green-600 dark:text-green-400'
  };
  
  const sizeStyles = {
    sm: 'p-3 text-sm',
    md: 'p-4',
    lg: 'p-6 text-lg'
  };
  
  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };
  
  return (
    <div
      role={variant === 'error' ? 'alert' : 'status'}
      aria-live={variant === 'error' ? 'assertive' : 'polite'}
      className={cn(
        'rounded-lg border',
        variantStyles[variant],
        sizeStyles[size],
        className
      )}
    >
      <div className="flex items-start gap-3">
        {showIcon && Icon && (
          <Icon className={cn(iconSizes[size], iconStyles[variant], 'flex-shrink-0 mt-0.5')} />
        )}
        
        <div className="flex-1 min-w-0">
          {title && (
            <h3 className={cn(
              'font-semibold',
              size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base'
            )}>
              {title}
            </h3>
          )}
          
          {(errorMessage || description) && (
            <div className={cn(
              'mt-1',
              size === 'sm' ? 'text-xs' : 'text-sm',
              title && 'opacity-90'
            )}>
              {description || errorMessage}
            </div>
          )}
          
          {(onRetry || onDismiss || actions) && (
            <div className="flex items-center gap-2 mt-3">
              {onRetry && (
                <Button
                  size={size === 'sm' ? 'sm' : 'default'}
                  variant="outline"
                  onClick={onRetry}
                  announceOnClick="Retrying action"
                  className="h-8"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  {retryText}
                </Button>
              )}
              
              {onDismiss && (
                <Button
                  size={size === 'sm' ? 'sm' : 'default'}
                  variant="ghost"
                  onClick={onDismiss}
                  announceOnClick="Error dismissed"
                  className="h-8"
                >
                  {dismissText}
                </Button>
              )}
              
              {actions}
            </div>
          )}
        </div>
        
        {onDismiss && (
          <Button
            size="icon-sm"
            variant="ghost"
            onClick={onDismiss}
            className="flex-shrink-0 opacity-70 hover:opacity-100"
            aria-label="Close"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </div>
  );
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundaryClass extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.props.onError?.(error, errorInfo);
    console.error('Error caught by boundary:', error, errorInfo);
  }
  
  resetError = () => {
    this.setState({ hasError: false, error: null });
  };
  
  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      
      if (FallbackComponent) {
        return <FallbackComponent error={this.state.error!} resetError={this.resetError} />;
      }
      
      return (
        <ErrorDisplay
          error={this.state.error}
          title="Something went wrong"
          variant="error"
          onRetry={this.resetError}
          retryText="Try again"
          className="m-4"
        />
      );
    }
    
    return this.props.children;
  }
}

export function ErrorBoundary(props: ErrorBoundaryProps) {
  return <ErrorBoundaryClass {...props} />;
}

interface InlineErrorProps {
  error?: string | null;
  className?: string;
}

export function InlineError({ error, className }: InlineErrorProps) {
  if (!error) return null;
  
  return (
    <div
      role="alert"
      aria-live="polite"
      className={cn(
        'flex items-center gap-2 text-sm text-destructive',
        className
      )}
    >
      <AlertCircle className="h-4 w-4 flex-shrink-0" />
      <span>{error}</span>
    </div>
  );
}

interface ToastErrorProps {
  error?: string | Error | null;
  title?: string;
  onDismiss?: () => void;
  duration?: number;
}

export function ToastError({ error, title, onDismiss, duration = 5000 }: ToastErrorProps) {
  React.useEffect(() => {
    if (error && duration > 0) {
      const timer = setTimeout(() => {
        onDismiss?.();
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [error, duration, onDismiss]);
  
  if (!error) return null;
  
  const errorMessage = error instanceof Error ? error.message : String(error);
  
  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <ErrorDisplay
        error={errorMessage}
        title={title}
        variant="error"
        size="sm"
        onDismiss={onDismiss}
        className="shadow-lg border-destructive"
      />
    </div>
  );
}