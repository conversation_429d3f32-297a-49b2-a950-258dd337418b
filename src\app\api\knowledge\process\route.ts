import { NextRequest, NextResponse } from 'next/server';
import { createDocumentProcessor } from '@/lib/llamaindex';
import { DeepSeekClient } from '@/lib/agent/deepseek';
import db from '@/lib/db';
import * as schema from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// Initialize DeepSeek client
const deepseek = new DeepSeekClient({
  apiKey: process.env.DEEPSEEK_API_KEY || '',
});

// Initialize document processor
const documentProcessor = createDocumentProcessor(deepseek);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      action,
      content,
      metadata,
      urls,
      reprocessExisting = false,
    } = body;

    if (!action) {
      return NextResponse.json(
        { success: false, error: 'Action is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'process_content': {
        if (!content || !metadata) {
          return NextResponse.json(
            { success: false, error: 'Content and metadata are required' },
            { status: 400 }
          );
        }

        const processedDoc = await documentProcessor.processDocument(content, {
          title: metadata.title,
          sourceUrl: metadata.sourceUrl,
          sourceType: metadata.sourceType || 'manual',
          tags: metadata.tags || [],
          lastIndexed: new Date(),
        });

        return NextResponse.json({
          success: true,
          data: {
            id: processedDoc.id,
            title: processedDoc.metadata.title,
            chunkCount: processedDoc.chunks.length,
            concepts: await documentProcessor.extractConcepts(content),
            summary: await documentProcessor.summarizeDocument(content),
            wordCount: content.split(' ').length,
          },
        });
      }

      case 'bulk_process': {
        if (!urls || !Array.isArray(urls)) {
          return NextResponse.json(
            { success: false, error: 'URLs array is required' },
            { status: 400 }
          );
        }

        const results = [];
        const errors = [];

        for (const url of urls) {
          try {
            // Check if already processed and skip if not reprocessing
            if (!reprocessExisting) {
              const existing = await db.select()
                .from(schema.knowledgeBase)
                .where(eq(schema.knowledgeBase.sourceUrl, url))
                .limit(1);
              
              if (existing.length > 0) {
                results.push({
                  url,
                  status: 'skipped',
                  reason: 'already_processed',
                  id: existing[0].id,
                });
                continue;
              }
            }

            // Use the existing Confluence loader (which now uses LlamaIndex)
            const { ConfluenceLoaderTool } = await import('@/lib/agent/tools/confluence');
            const confluenceLoader = new ConfluenceLoaderTool(deepseek);
            
            const result = await confluenceLoader.execute({ url });
            
            if (result.success) {
              results.push({
                url,
                status: 'success',
                data: result.data,
              });
            } else {
              errors.push({
                url,
                error: result.error || 'Unknown error',
              });
            }
          } catch (error) {
            errors.push({
              url,
              error: (error as Error).message,
            });
          }
        }

        return NextResponse.json({
          success: true,
          data: {
            processed: results.length,
            errors: errors.length,
            results,
            errors: errors.length > 0 ? errors : undefined,
          },
        });
      }

      case 'reindex_all': {
        // Get all documents in knowledge base
        const allDocs = await db.select().from(schema.knowledgeBase);
        
        const reindexed = [];
        const failed = [];

        for (const doc of allDocs) {
          try {
            if (!doc.content) continue;

            const processedDoc = await documentProcessor.processDocument(doc.content, {
              title: doc.title,
              sourceUrl: doc.sourceUrl,
              sourceType: doc.sourceType as any,
              tags: doc.tags ? JSON.parse(doc.tags) : [],
              lastIndexed: new Date(),
            });

            reindexed.push({
              id: doc.id,
              title: doc.title,
              chunkCount: processedDoc.chunks.length,
            });
          } catch (error) {
            failed.push({
              id: doc.id,
              title: doc.title,
              error: (error as Error).message,
            });
          }
        }

        return NextResponse.json({
          success: true,
          data: {
            reindexed: reindexed.length,
            failed: failed.length,
            results: reindexed,
            errors: failed.length > 0 ? failed : undefined,
          },
        });
      }

      case 'extract_concepts': {
        if (!content) {
          return NextResponse.json(
            { success: false, error: 'Content is required' },
            { status: 400 }
          );
        }

        const concepts = await documentProcessor.extractConcepts(content);
        const summary = await documentProcessor.summarizeDocument(content);

        return NextResponse.json({
          success: true,
          data: {
            concepts,
            summary,
            wordCount: content.split(' ').length,
          },
        });
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Knowledge processing error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process knowledge',
        details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get statistics about the knowledge base
    const stats = await db.select({
      sourceType: schema.knowledgeBase.sourceType,
      count: schema.knowledgeBase.id,
    }).from(schema.knowledgeBase);

    const totalDocs = stats.length;
    const sourceTypes = stats.reduce((acc, stat) => {
      acc[stat.sourceType] = (acc[stat.sourceType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Get recent documents
    const recentDocs = await db.select({
      id: schema.knowledgeBase.id,
      title: schema.knowledgeBase.title,
      sourceType: schema.knowledgeBase.sourceType,
      lastIndexed: schema.knowledgeBase.lastIndexed,
    }).from(schema.knowledgeBase)
      .orderBy(schema.knowledgeBase.lastIndexed)
      .limit(10);

    return NextResponse.json({
      success: true,
      data: {
        totalDocuments: totalDocs,
        sourceTypes,
        recentDocuments: recentDocs.map(doc => ({
          ...doc,
          lastIndexed: doc.lastIndexed.toISOString(),
        })),
      },
    });

  } catch (error) {
    console.error('Knowledge stats error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get knowledge base stats' },
      { status: 500 }
    );
  }
}