import { NextRequest, NextResponse } from 'next/server';
import { createLlamaIndexService } from '@/lib/llamaindex';
import { DeepSeekClient } from '@/lib/agent/deepseek';
import db from '@/lib/db';
import * as schema from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// Initialize DeepSeek client
const deepseek = new DeepSeekClient({
  deepseekApiKey: process.env.DEEPSEEK_API_KEY || '',
  deepseekBaseUrl: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
});

// Initialize LlamaIndex service
const llamaIndexService = createLlamaIndexService(process.env.OPENAI_API_KEY || '');

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      action,
      content,
      metadata,
      urls,
      reprocessExisting = false,
    } = body;

    if (!action) {
      return NextResponse.json(
        { success: false, error: 'Action is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'process_content': {
        if (!content || !metadata) {
          return NextResponse.json(
            { success: false, error: 'Content and metadata are required' },
            { status: 400 }
          );
        }

        const processedDoc = await llamaIndexService.processDocument(content, {
          title: metadata.title,
          sourceUrl: metadata.sourceUrl,
          sourceType: metadata.sourceType || 'manual',
          tags: metadata.tags || [],
          lastIndexed: new Date(),
        });

        return NextResponse.json({
          success: true,
          data: {
            id: processedDoc.id,
            title: processedDoc.metadata.title,
            chunkCount: processedDoc.chunks.length,
            concepts: await llamaIndexService.generateConcepts(content),
            summary: await llamaIndexService.generateSummary(content),
            wordCount: content.split(' ').length,
          },
        });
      }

      case 'bulk_process': {
        if (!urls || !Array.isArray(urls)) {
          return NextResponse.json(
            { success: false, error: 'URLs array is required' },
            { status: 400 }
          );
        }

        const results = [];
        const errors = [];

        for (const url of urls) {
          try {
            // Check if already processed and skip if not reprocessing
            if (!reprocessExisting) {
              const existing = await db.select()
                .from(schema.knowledgeBase)
                .where(eq(schema.knowledgeBase.sourceUrl, url))
                .limit(1);
              
              if (existing.length > 0) {
                results.push({
                  url,
                  status: 'skipped',
                  reason: 'already_processed',
                  id: existing[0].id,
                });
                continue;
              }
            }

            // Use the existing Confluence loader (which now uses LlamaIndex)
            const { ConfluenceLoaderTool } = await import('@/lib/agent/tools/confluence');
            const confluenceLoader = new ConfluenceLoaderTool(deepseek);
            
            const result = await confluenceLoader.execute({ url });
            
            if (result.success) {
              results.push({
                url,
                status: 'success',
                data: result.data,
              });
            } else {
              errors.push({
                url,
                error: result.error || 'Unknown error',
              });
            }
          } catch (error) {
            errors.push({
              url,
              error: (error as Error).message,
            });
          }
        }

        return NextResponse.json({
          success: true,
          data: {
            processed: results.length,
            errorCount: errors.length,
            results,
            errors: errors.length > 0 ? errors : undefined,
          },
        });
      }

      case 'reindex_all': {
        // Get all documents in knowledge base
        const allDocs = await db.select().from(schema.knowledgeBase);
        
        const reindexed = [];
        const failed = [];

        for (const doc of allDocs) {
          try {
            if (!doc.content) continue;

            const processedDoc = await llamaIndexService.processDocument(doc.content, {
              title: doc.title,
              sourceUrl: doc.sourceUrl,
              sourceType: doc.sourceType as any,
              tags: doc.tags ? JSON.parse(JSON.stringify(doc.tags)) : [],
              lastIndexed: new Date(),
            });

            reindexed.push({
              id: doc.id,
              title: doc.title,
              chunkCount: processedDoc.chunks.length,
            });
          } catch (error) {
            failed.push({
              id: doc.id,
              title: doc.title,
              error: (error as Error).message,
            });
          }
        }

        return NextResponse.json({
          success: true,
          data: {
            reindexed: reindexed.length,
            failed: failed.length,
            results: reindexed,
            errors: failed.length > 0 ? failed : undefined,
          },
        });
      }

      case 'extract_concepts': {
        if (!content) {
          return NextResponse.json(
            { success: false, error: 'Content is required' },
            { status: 400 }
          );
        }

        const concepts = await llamaIndexService.generateConcepts(content);
        const summary = await llamaIndexService.generateSummary(content);

        return NextResponse.json({
          success: true,
          data: {
            concepts,
            summary,
            wordCount: content.split(' ').length,
          },
        });
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Knowledge processing error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get index statistics
    const stats = await llamaIndexService.getIndexStats();
    
    // Get document count by source type
    const docsBySourceType = await db.select()
      .from(schema.knowledgeBase)
      .groupBy(schema.knowledgeBase.sourceType);

    return NextResponse.json({
      success: true,
      data: {
        indexStats: stats,
        documentStats: docsBySourceType.length,
        lastUpdated: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Failed to get processing stats:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get processing stats' },
      { status: 500 }
    );
  }
}