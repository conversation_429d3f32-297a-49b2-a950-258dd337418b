"use client"

import * as React from "react"
import { useState, FormEvent } from "react"
import { <PERSON><PERSON>lip, Mic, CornerDownLeft, PanelLeft, Plus, User, History, FileText, MoreVertical, Edit2, Trash2, Bot, Send, Library, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChatInput, ChatMessageList, Message, Mission } from "@/components/ui/chat-interface"
import KnowledgeBase from "@/components/KnowledgeBase"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

// Main Mission Workspace Component
const MissionWorkspace: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([])

  const [missions, setMissions] = useState<Mission[]>([
    {
      id: 1,
      title: "Data Analysis Project",
      timestamp: new Date(Date.now() - 86400000),
      status: "completed",
    },
    {
      id: 2,
      title: "Market Research",
      timestamp: new Date(Date.now() - 172800000),
      status: "completed",
    },
    {
      id: 3,
      title: "Content Strategy",
      timestamp: new Date(Date.now() - 259200000),
      status: "in-progress",
    },
  ])

  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [currentView, setCurrentView] = useState<"chat" | "knowledge">("chat")
  const [selectedMissionId, setSelectedMissionId] = useState<number | null>(null)

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return

    const newMessage: Message = {
      id: messages.length + 1,
      content: input,
      sender: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, newMessage])
    setInput("")
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: messages.length + 2,
        content: "I understand your request. Let me help you with that mission. I'll analyze the requirements and provide you with a comprehensive solution.",
        sender: "ai",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, aiResponse])
      setIsLoading(false)
    }, 1500)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && e.ctrlKey) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  const startNewMission = () => {
    const newMission: Mission = {
      id: missions.length + 1,
      title: "New Mission",
      timestamp: new Date(),
      status: "in-progress",
    }
    setMissions((prev) => [newMission, ...prev])
    setSelectedMissionId(newMission.id)
    setCurrentView("chat")
    setMessages([]) // Start with empty messages to show welcome message
  }

  const selectMission = (missionId: number) => {
    setSelectedMissionId(missionId)
    setCurrentView("chat")
    // Load mission-specific messages here
    setMessages([
      {
        id: 1,
        content: `Loaded mission ${missionId}. How can I help you continue with this mission?`,
        sender: "ai",
        timestamp: new Date(),
      },
    ])
  }

  const selectKnowledgeBase = () => {
    setCurrentView("knowledge")
    setSelectedMissionId(null) // Clear mission selection when switching to knowledge base
  }

  const renameMission = (missionId: number, newTitle: string) => {
    setMissions(prev =>
      prev.map(mission =>
        mission.id === missionId ? { ...mission, title: newTitle } : mission
      )
    )
  }

  const deleteMission = (missionId: number) => {
    setMissions(prev => prev.filter(mission => mission.id !== missionId))
    if (selectedMissionId === missionId) {
      setSelectedMissionId(null)
      setCurrentView("chat")
    }
  }

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Sidebar */}
      <div className={cn(
        "flex flex-col bg-muted/30 border-r border-border transition-all duration-300",
        sidebarOpen ? "w-80" : "w-14"
      )}>
        <div className={cn(
          "p-4 border-b border-border",
          !sidebarOpen && "p-2"
        )}>
          {sidebarOpen ? (
            <>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Bot size={20} className="text-primary" />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  title="Collapse sidebar"
                >
                  <PanelLeft className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-2">
                <Button
                  onClick={startNewMission}
                  className="w-full gap-2"
                  variant="default"
                >
                  <Plus className="h-4 w-4" />
                  New Mission
                </Button>
                <Button
                  variant={currentView === "knowledge" && selectedMissionId === null ? "default" : "ghost"}
                  className="w-full gap-2"
                  onClick={selectKnowledgeBase}
                >
                  <Library className="h-4 w-4" />
                  Knowledge Base
                </Button>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center space-y-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(true)}
                title="Show sidebar"
                className="w-10 h-10 hover:bg-muted/50"
              >
                <PanelLeft className="h-4 w-4" />
              </Button>
              <Button
                onClick={startNewMission}
                size="icon"
                variant="default"
                title="New Mission"
                className="w-10 h-10"
              >
                <Plus className="h-4 w-4" />
              </Button>
              <Button
                variant={currentView === "knowledge" && selectedMissionId === null ? "default" : "ghost"}
                size="icon"
                onClick={selectKnowledgeBase}
                title="Knowledge Base"
                className="w-10 h-10 hover:bg-muted/50"
              >
                <Library className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {sidebarOpen && (
          <div className="flex-1 overflow-y-auto p-4">
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search missions..."
                  className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                />
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Recent Missions</h3>
              {missions.map((mission) => (
                <div
                  key={mission.id}
                  className={cn(
                    "p-3 rounded-lg cursor-pointer border transition-colors group",
                    selectedMissionId === mission.id && currentView === "chat"
                      ? "bg-primary/10 border-primary/30"
                      : "bg-background hover:bg-muted/50 border-border/50"
                  )}
                  onClick={() => selectMission(mission.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium truncate">{mission.title}</h4>
                      <p className="text-xs text-muted-foreground mt-1">
                        {mission.timestamp.toLocaleDateString([], { 
                          month: 'numeric', 
                          day: 'numeric', 
                          year: '2-digit' 
                        })}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        mission.status === "completed" && "bg-green-500",
                        mission.status === "in-progress" && "bg-yellow-500",
                        mission.status === "failed" && "bg-red-500"
                      )} />
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()
                              const newTitle = prompt("Enter new mission title:", mission.title)
                              if (newTitle && newTitle.trim()) {
                                renameMission(mission.id, newTitle.trim())
                              }
                            }}
                          >
                            <Edit2 className="h-4 w-4 mr-2" />
                            Rename
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation()
                              if (confirm("Are you sure you want to delete this mission?")) {
                                deleteMission(mission.id)
                              }
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}


      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Navigation Bar */}
        <div className="p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h1 className="text-lg font-semibold">Service Management Assistant</h1>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" title="History">
                <History className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" title="User Profile">
                <User className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 overflow-hidden">
          {currentView === "chat" ? (
            <div className="flex flex-col h-full">
              {messages.length === 0 && !isLoading ? (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                      <Bot size={32} className="text-primary/60" />
                    </div>
                    <h2 className="text-xl font-medium text-foreground mb-2">How can I help you today?</h2>
                    <p className="text-sm text-muted-foreground">Start a conversation to begin your mission.</p>
                  </div>
                </div>
              ) : (
                <ChatMessageList>
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className="w-full flex justify-center px-4 md:px-8 lg:px-12"
                    >
                      <div
                        className={cn(
                          "flex gap-3 w-full max-w-4xl",
                          message.sender === "user" ? "flex-row-reverse" : ""
                        )}
                      >
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
                          {message.sender === "user" ? (
                            <User className="h-4 w-4" />
                          ) : (
                            <Bot className="h-4 w-4" />
                          )}
                        </div>
                        <div
                          className={cn(
                            "rounded-lg px-4 py-3 flex-1 max-w-[75%]",
                            message.sender === "user"
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted"
                          )}
                        >
                          <p className="text-sm">{message.content}</p>
                          <p className="text-xs opacity-70 mt-1">
                            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}

                  {isLoading && (
                    <div className="w-full flex justify-center px-4 md:px-8 lg:px-12">
                      <div className="flex gap-3 w-full max-w-4xl">
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
                          <Bot className="h-4 w-4" />
                        </div>
                        <div className="rounded-lg px-4 py-3 bg-muted">
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                            <div className="w-2 h-2 bg-current rounded-full animate-pulse delay-100" />
                            <div className="w-2 h-2 bg-current rounded-full animate-pulse delay-200" />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </ChatMessageList>
              )}
            </div>
          ) : (
            <KnowledgeBase />
          )}
        </div>

        {/* Input - Only show for chat view */}
        {currentView === "chat" && (
          <div className="p-4 bg-background">
            <form
              onSubmit={handleSubmit}
              className="relative rounded-xl border border-border/50 bg-background focus-within:border-border focus-within:shadow-sm transition-all duration-200 max-w-4xl mx-auto"
            >
              <ChatInput
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Describe your mission or ask for help..."
                minRows={1}
                maxRows={6}
                className="resize-none rounded-xl bg-transparent border-0 p-4 shadow-none focus-visible:ring-0 focus-visible:outline-none"
              />
              <div className="flex items-center px-4 pb-3 justify-between">
                <div className="flex">
                  <Button
                    variant="ghost"
                    size="icon"
                    type="button"
                    className="h-8 w-8"
                  >
                    <Paperclip className="size-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    type="button"
                    className="h-8 w-8"
                  >
                    <Mic className="size-4" />
                  </Button>
                </div>
                <Button
                  type="submit"
                  size="icon"
                  className="ml-auto h-8 w-8 rounded-lg"
                  disabled={!input.trim() || isLoading}
                  title="Send Mission"
                >
                  <Send className="size-4" />
                </Button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  )
}

export default MissionWorkspace 