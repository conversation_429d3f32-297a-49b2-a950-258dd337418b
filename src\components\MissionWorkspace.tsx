"use client"

import * as React from "react"
import { useState, FormEvent, useEffect, useRef } from "react"
import { useMissionUpdates } from "@/hooks/useMissionUpdates"
import { Paperclip, Mic, CornerDownLeft, PanelLeft, Plus, User, History, FileText, MoreVertical, SquarePen, Trash2, Bot, Send, Library, Search, AlertTriangle, X, Settings, HelpCircle, LogOut, Palette, MessageSquare, Target, Play, Pause, Square, CheckCircle, XCircle, Clock, Brain, Zap } from "lucide-react"
import { Button } from "@/components/ui/button"
import { ChatInput, ChatMessageList, Message, Mission } from "@/components/ui/chat-interface"
import KnowledgeBase from "@/components/KnowledgeBase"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { cn } from "@/lib/utils"

// Types for mission progress functionality
interface MissionDetails {
  mission: {
    id: string;
    title: string;
    description: string;
    status: 'pending' | 'planning' | 'executing' | 'completed' | 'failed';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    createdAt: string;
    updatedAt: string;
    completedAt?: string;
  };
  plans: Array<{
    id: string;
    title: string;
    description: string;
    status: string;
    estimatedDuration: number;
    version: number;
    createdAt: string;
    updatedAt: string;
  }>;
  tasks: Array<{
    id: string;
    title: string;
    description: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
    priority: number;
    toolName?: string;
    estimatedDuration?: number;
    actualDuration?: number;
    startedAt?: string;
    completedAt?: string;
    createdAt: string;
    updatedAt: string;
  }>;
  logs: Array<{
    id: string;
    level: 'debug' | 'info' | 'warn' | 'error';
    message: string;
    timestamp: string;
    data?: any;
  }>;
  reflections: Array<{
    id: string;
    type: 'progress_assessment' | 'plan_optimization' | 'error_analysis' | 'success_analysis';
    content: string;
    insights?: string[];
    recommendations?: string[];
    confidence?: number;
    createdAt: string;
  }>;
}

// Main Mission Workspace Component
const MissionWorkspace: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([])
  const [missionDetails, setMissionDetails] = useState<MissionDetails | null>(null)
  const [loadingMissionDetails, setLoadingMissionDetails] = useState(false)

  const [missions, setMissions] = useState<Mission[]>([
    {
      id: 1,
      title: "Data Analysis Project",
      timestamp: new Date(Date.now() - 86400000),
      status: "completed",
    },
    {
      id: 2,
      title: "Market Research",
      timestamp: new Date(Date.now() - 172800000),
      status: "completed",
    },
    {
      id: 3,
      title: "Content Strategy",
      timestamp: new Date(Date.now() - 259200000),
      status: "in-progress",
    },
  ])

  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [currentView, setCurrentView] = useState<"chat" | "knowledge">("chat")
  const [selectedMissionId, setSelectedMissionId] = useState<number | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [missionToDelete, setMissionToDelete] = useState<Mission | null>(null)
  const [editingMissionId, setEditingMissionId] = useState<number | null>(null)
  const [editingValue, setEditingValue] = useState("")
  const editInputRef = useRef<HTMLInputElement>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const [searchQuery, setSearchQuery] = useState("")

  // WebSocket integration for real-time updates
  const {
    isConnected: wsConnected,
    connectionError,
    subscribeMission,
    unsubscribeMission,
    getMission: getWsMission,
    missions: wsMissions,
    clearMission
  } = useMissionUpdates({
    autoConnect: true,
    maxLogs: 50,
  })

  // Fetch mission details when a mission is selected
  useEffect(() => {
    if (selectedMissionId) {
      fetchMissionDetails();
      
      // Subscribe to WebSocket updates for this mission
      if (wsConnected) {
        subscribeMission(selectedMissionId.toString());
        
        return () => {
          unsubscribeMission(selectedMissionId.toString());
        };
      }
    } else {
      setMissionDetails(null);
    }
  }, [selectedMissionId, wsConnected, subscribeMission, unsubscribeMission]);

  // Update details from WebSocket data when available
  useEffect(() => {
    if (selectedMissionId && missionDetails) {
      const wsMission = getWsMission(selectedMissionId.toString());
      if (wsMission) {
        // Update mission status and other real-time data
        setMissionDetails(prev => prev ? {
          ...prev,
          mission: {
            ...prev.mission,
            status: wsMission.status as 'pending' | 'planning' | 'executing' | 'completed' | 'failed',
            updatedAt: wsMission.lastUpdate
          },
          // Update logs with real-time data
          logs: wsMission.logs ? wsMission.logs.map((log: any) => ({
            id: log.id || Math.random().toString(),
            level: log.level,
            message: log.message,
            timestamp: log.timestamp,
            data: log.data
          })) : prev.logs
        } : prev);
      }
    }
  }, [wsMissions, selectedMissionId, getWsMission, missionDetails]);

  const fetchMissionDetails = async () => {
    if (!selectedMissionId) return;
    
    try {
      setLoadingMissionDetails(true);
      const response = await fetch(`/api/missions/${selectedMissionId}`);
      const result = await response.json();
      if (result.success) {
        setMissionDetails(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch mission details:', error);
    } finally {
      setLoadingMissionDetails(false);
    }
  };

  const handleMissionAction = async (action: 'pause' | 'resume' | 'cancel') => {
    if (!selectedMissionId) return;
    
    try {
      const response = await fetch(`/api/missions/${selectedMissionId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action }),
      });

      if (response.ok) {
        await fetchMissionDetails();
      }
    } catch (error) {
      console.error(`Failed to ${action} mission:`, error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'executing': return <Zap className="h-4 w-4 text-blue-500" />;
      case 'planning': return <Brain className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'executing': return 'bg-blue-100 text-blue-800';
      case 'planning': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateProgress = () => {
    // Use real-time WebSocket progress if available, otherwise fallback to task-based calculation
    if (selectedMissionId) {
      const wsMission = getWsMission(selectedMissionId.toString());
      if (wsMission?.progress?.percentage !== undefined) {
        return wsMission.progress.percentage;
      }
    }
    
    if (!missionDetails?.tasks.length) return 0;
    const completedTasks = missionDetails.tasks.filter(t => t.status === 'completed').length;
    return (completedTasks / missionDetails.tasks.length) * 100;
  };

  const getRealtimeStatus = () => {
    if (selectedMissionId) {
      const wsMission = getWsMission(selectedMissionId.toString());
      if (wsMission) {
        return {
          status: wsMission.status,
          isActive: wsMission.status === 'executing' || wsMission.status === 'planning',
          lastUpdate: new Date(wsMission.lastUpdate),
          currentTask: wsMission.progress?.currentTask || null
        };
      }
    }
    
    return {
      status: missionDetails?.mission.status || 'pending',
      isActive: false,
      lastUpdate: null,
      currentTask: null
    };
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return

    const newMessage: Message = {
      id: messages.length + 1,
      content: input,
      sender: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, newMessage])
    setInput("")
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: messages.length + 2,
        content: "I understand your request. Let me help you with that mission. I'll analyze the requirements and provide you with a comprehensive solution.",
        sender: "ai",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, aiResponse])
      setIsLoading(false)
    }, 1500)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && e.ctrlKey) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  const startNewMission = () => {
    const newMission: Mission = {
      id: missions.length + 1,
      title: "New Mission",
      timestamp: new Date(),
      status: "in-progress",
    }
    setMissions((prev) => [newMission, ...prev])
    setSelectedMissionId(newMission.id)
    setCurrentView("chat")
    setMessages([]) // Start with empty messages to show welcome message
  }

  const selectMission = (missionId: number) => {
    setSelectedMissionId(missionId)
    setCurrentView("chat")
    // Load mission-specific messages here
    setMessages([
      {
        id: 1,
        content: `Loaded mission ${missionId}. How can I help you continue with this mission?`,
        sender: "ai",
        timestamp: new Date(),
      },
    ])
  }

  const selectKnowledgeBase = () => {
    setCurrentView("knowledge")
    setSelectedMissionId(null) // Clear mission selection when switching to knowledge base
  }

  const renameMission = (missionId: number, newTitle: string) => {
    setMissions(prev =>
      prev.map(mission =>
        mission.id === missionId ? { ...mission, title: newTitle } : mission
      )
    )
  }

  const openDeleteDialog = (mission: Mission) => {
    setMissionToDelete(mission)
    setDeleteDialogOpen(true)
  }

  const confirmDeleteMission = () => {
    if (missionToDelete) {
      setMissions(prev => prev.filter(mission => mission.id !== missionToDelete.id))
      if (selectedMissionId === missionToDelete.id) {
        setSelectedMissionId(null)
        setCurrentView("chat")
      }
      setDeleteDialogOpen(false)
      setMissionToDelete(null)
    }
  }

  const cancelDeleteMission = () => {
    setDeleteDialogOpen(false)
    setMissionToDelete(null)
  }

  const startEditingMission = (mission: Mission) => {
    setEditingMissionId(mission.id)
    setEditingValue(mission.title)
  }

  const saveEditingMission = () => {
    if (editingMissionId) {
      const trimmedValue = editingValue.trim()
      
      // Validation
      if (!trimmedValue) {
        // Don't save empty names, just cancel
        cancelEditingMission()
        return
      }
      
      if (trimmedValue.length > 100) {
        // Show error feedback but don't save
        return
      }
      
      renameMission(editingMissionId, trimmedValue)
      setEditingMissionId(null)
      setEditingValue("")
    }
  }

  const cancelEditingMission = () => {
    setEditingMissionId(null)
    setEditingValue("")
  }

  const handleEditKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault()
      saveEditingMission()
    } else if (e.key === "Escape") {
      cancelEditingMission()
    }
  }

  // Add global keyboard shortcut listener
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Focus search on Ctrl+K or Cmd+K
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        if (sidebarOpen && searchInputRef.current) {
          searchInputRef.current.focus()
        }
      }
      
      // Toggle sidebar on Ctrl+B or Cmd+B
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault()
        setSidebarOpen(prev => !prev)
      }
      
      // New mission on Ctrl+N or Cmd+N
      if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault()
        startNewMission()
      }
    }

    document.addEventListener('keydown', handleGlobalKeyDown)
    return () => document.removeEventListener('keydown', handleGlobalKeyDown)
  }, [sidebarOpen])

  // Filter missions based on search query
  const filteredMissions = missions.filter((mission) =>
    mission.title.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Auto-focus on mission name input when editing starts
  useEffect(() => {
    if (editingMissionId && editInputRef.current) {
      editInputRef.current.focus()
      editInputRef.current.select()
    }
  }, [editingMissionId])

  const getRealtimeMissionStatus = (missionId: number) => {
    const wsMission = getWsMission(missionId.toString())
    if (wsMission) {
      return {
        status: wsMission.status,
        isActive: wsMission.status === 'executing' || wsMission.status === 'planning',
        progress: wsMission.progress,
        lastUpdate: new Date(wsMission.lastUpdate),
        currentTask: wsMission.progress?.currentTask || null
      }
    }
    return null
  }

  const clearSearch = () => {
    setSearchQuery("")
    if (searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }

  // User dropdown menu handlers
  const handleCustomizeAssistant = () => {
    console.log("Customize Assistant clicked")
    // TODO: Implement assistant customization
  }

  const handleSettings = () => {
    console.log("Settings clicked")
    // TODO: Implement settings page
  }

  const handleHelp = () => {
    console.log("Help clicked")
    // TODO: Implement help documentation
  }

  const handleLogout = () => {
    console.log("Logout clicked")
    // TODO: Implement logout functionality
  }

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Sidebar */}
      <div className={cn(
        "flex flex-col bg-muted/30 border-r border-border transition-all duration-300",
        sidebarOpen ? "w-80" : "w-14"
      )}>
        <div className={cn(
          "p-4 border-b border-border",
          !sidebarOpen && "p-2"
        )}>
          {sidebarOpen ? (
            <>
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Bot size={20} className="text-primary" />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  title="Collapse sidebar"
                >
                  <PanelLeft className="h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-2">
                <Button
                  onClick={startNewMission}
                  className={cn(
                    "w-full gap-2",
                    currentView === "chat" 
                      ? "" 
                      : "hover:bg-muted hover:text-muted-foreground"
                  )}
                  variant={currentView === "chat" ? "default" : "ghost"}
                >
                  <Plus className="h-4 w-4" />
                  New Mission
                </Button>
                <Button
                  variant={currentView === "knowledge" ? "default" : "ghost"}
                  className={cn(
                    "w-full gap-2",
                    currentView === "knowledge" 
                      ? "" 
                      : "hover:bg-muted hover:text-muted-foreground"
                  )}
                  onClick={selectKnowledgeBase}
                >
                  <Library className="h-4 w-4" />
                  Knowledge Base
                </Button>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center space-y-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(true)}
                title="Show sidebar"
                className="w-10 h-10 hover:bg-muted hover:text-muted-foreground"
              >
                <PanelLeft className="h-4 w-4" />
              </Button>
              <Button
                onClick={startNewMission}
                size="icon"
                variant={currentView === "chat" ? "default" : "ghost"}
                title="New Mission"
                className={cn(
                  "w-10 h-10",
                  currentView === "chat" 
                    ? "" 
                    : "hover:bg-muted hover:text-muted-foreground [&:hover]:bg-muted [&:hover]:text-muted-foreground"
                )}
              >
                <Plus className="h-4 w-4" />
              </Button>
              <Button
                variant={currentView === "knowledge" ? "default" : "ghost"}
                size="icon"
                onClick={selectKnowledgeBase}
                title="Knowledge Base"
                className={cn(
                  "w-10 h-10",
                  currentView === "knowledge" 
                    ? "" 
                    : "hover:bg-muted hover:text-muted-foreground [&:hover]:bg-muted [&:hover]:text-muted-foreground"
                )}
              >
                <Library className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {sidebarOpen && (
          <div className="flex-1 overflow-y-auto p-4">
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search missions..."
                  className="w-full pl-10 pr-10 py-2 bg-background border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring transition-all"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={clearSearch}
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 hover:bg-muted/50"
                    title="Clear search"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-muted-foreground">
                  {searchQuery ? `Search Results` : "Recent Missions"}
                </h3>
                {searchQuery && (
                  <span className="text-xs text-muted-foreground">
                    {filteredMissions.length} result{filteredMissions.length !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
              
              {filteredMissions.length === 0 ? (
                <div className="text-center py-8">
                  {searchQuery ? (
                    <div>
                      <Search className="h-8 w-8 text-muted-foreground/50 mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground mb-1">No missions found</p>
                      <p className="text-xs text-muted-foreground/70">
                        Try adjusting your search terms
                      </p>
                      <Button
                        variant="link"
                        size="sm"
                        onClick={clearSearch}
                        className="mt-2 h-auto p-0 text-xs"
                      >
                        Clear search
                      </Button>
                    </div>
                  ) : (
                    <div>
                      <FileText className="h-8 w-8 text-muted-foreground/50 mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">No missions yet</p>
                      <p className="text-xs text-muted-foreground/70">Start a new mission to get going</p>
                    </div>
                  )}
                </div>
              ) : (
                filteredMissions.map((mission) => (
                <div
                  key={mission.id}
                  className={cn(
                    "p-3 rounded-lg border transition-colors group",
                    editingMissionId === mission.id 
                      ? "bg-muted/50 border-primary/50 ring-2 ring-primary/20"
                      : cn(
                          "cursor-pointer hover:bg-accent/50 hover:border-accent",
                          selectedMissionId === mission.id && currentView === "chat"
                            ? "bg-primary/10 border-primary/30"
                            : "bg-background border-border/50"
                        )
                  )}
                  onClick={(e) => {
                    // Don't trigger selection if we're editing this mission
                    if (editingMissionId === mission.id) {
                      return
                    }
                    
                    // Don't trigger selection if clicking on dropdown trigger or input elements
                    const target = e.target as HTMLElement
                    if (target.closest('button[role="combobox"]') || 
                        target.closest('[role="menuitem"]') || 
                        target.tagName === 'INPUT') {
                      return
                    }
                    
                    selectMission(mission.id)
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0 mr-2">
                      {editingMissionId === mission.id ? (
                        <div>
                          <input
                            ref={editInputRef}
                            type="text"
                            value={editingValue}
                            onChange={(e) => setEditingValue(e.target.value)}
                            onKeyDown={handleEditKeyDown}
                            onBlur={saveEditingMission}
                            className={cn(
                              "w-full text-sm font-medium bg-background border rounded px-2 py-1 focus:outline-none focus:ring-2 transition-colors",
                              editingValue.trim().length === 0 
                                ? "border-destructive/50 focus:border-destructive focus:ring-destructive/20"
                                : editingValue.length > 100
                                ? "border-yellow-500/50 focus:border-yellow-500 focus:ring-yellow-500/20"
                                : "border-border focus:border-primary focus:ring-primary/20"
                            )}
                            autoFocus
                            maxLength={150} // Allow typing beyond 100 to show validation
                            placeholder="Enter mission name..."
                          />
                          {editingValue.length > 100 && (
                            <p className="text-xs text-yellow-600 mt-1">
                              {editingValue.length}/100 characters (max length exceeded)
                            </p>
                          )}
                        </div>
                      ) : (
                        <h4 
                          className="text-sm font-medium truncate group-hover:text-primary transition-colors"
                          title="Click mission to view details"
                        >
                          {mission.title}
                        </h4>
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        {mission.timestamp.toLocaleDateString([], { 
                          month: 'numeric', 
                          day: 'numeric', 
                          year: '2-digit' 
                        })}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {(() => {
                        const realtime = getRealtimeMissionStatus(mission.id)
                        const status = realtime?.status || mission.status
                        const isActive = realtime?.isActive || false
                        
                        return (
                          <div className="flex items-center gap-1">
                            <div className={cn(
                              "w-2 h-2 rounded-full",
                              status === "completed" && "bg-green-500",
                              status === "executing" && "bg-blue-500",
                              status === "planning" && "bg-orange-500",
                              status === "in-progress" && "bg-yellow-500",
                              status === "failed" && "bg-red-500",
                              status === "pending" && "bg-gray-400",
                              isActive && "animate-pulse"
                            )} />
                            {realtime?.progress && realtime.progress.percentage > 0 && (
                              <span className="text-xs text-muted-foreground">
                                {realtime.progress.percentage}%
                              </span>
                            )}
                          </div>
                        )
                      })()}
                      {editingMissionId !== mission.id && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-muted hover:text-muted-foreground"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <MoreVertical className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation()
                                startEditingMission(mission)
                              }}
                            >
                              <SquarePen className="h-4 w-4 mr-2" />
                              Rename
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation()
                                openDeleteDialog(mission)
                              }}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                </div>
                ))
              )}
            </div>
          </div>
        )}


      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Navigation Bar */}
        <div className="p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h1 className="text-lg font-semibold">
                {currentView === "knowledge" ? "Service Management Knowledge Base" : "Service Management Assistant"}
              </h1>
              
              {/* WebSocket Connection Status */}
              <div className="flex items-center gap-2 text-xs">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  wsConnected ? "bg-green-500" : "bg-red-500"
                )} />
                <span className="text-muted-foreground">
                  {wsConnected ? "Real-time updates active" : connectionError || "Connecting..."}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="icon" 
                title="History"
                className="hover:bg-muted hover:text-muted-foreground"
              >
                <History className="h-4 w-4" />
              </Button>
              
              {/* User Profile Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    title="User Profile"
                    className="hover:bg-muted hover:text-muted-foreground"
                  >
                    <User className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem onClick={handleCustomizeAssistant}>
                    <Palette className="h-4 w-4 mr-2" />
                    Customize Assistant
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleSettings}>
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleHelp}>
                    <HelpCircle className="h-4 w-4 mr-2" />
                    Help
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleLogout} className="text-destructive focus:text-destructive">
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 overflow-hidden">
          {currentView === "chat" ? (
            <div className="flex flex-col h-full">
              {/* Mission Progress Header - Only show when mission is selected */}
              {selectedMissionId && missionDetails && (
                <div className="border-b border-border bg-muted/30 p-4">
                  <div className="max-w-4xl mx-auto">
                    {loadingMissionDetails ? (
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Target className="h-4 w-4 animate-pulse" />
                        <span>Loading mission details...</span>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {/* Mission Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-3">
                            {getStatusIcon(getRealtimeStatus().status)}
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="font-medium">{missionDetails.mission.title}</h3>
                                <Badge className={`${getStatusColor(getRealtimeStatus().status)} ${getRealtimeStatus().isActive ? 'animate-pulse' : ''}`}>
                                  {getRealtimeStatus().status}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground mt-1">
                                {missionDetails.mission.description}
                              </p>
                              {getRealtimeStatus().currentTask && (
                                <div className="mt-2 flex items-center gap-1 text-sm text-blue-600">
                                  <Zap className="h-3 w-3" />
                                  <span>Current: {getRealtimeStatus().currentTask}</span>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getRealtimeStatus().status === 'executing' && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleMissionAction('pause')}
                              >
                                <Pause className="h-4 w-4 mr-1" />
                                Pause
                              </Button>
                            )}
                            {getRealtimeStatus().status === 'pending' && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleMissionAction('resume')}
                              >
                                <Play className="h-4 w-4 mr-1" />
                                Resume
                              </Button>
                            )}
                            {(getRealtimeStatus().status === 'executing' || getRealtimeStatus().status === 'pending') && (
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleMissionAction('cancel')}
                              >
                                <Square className="h-4 w-4 mr-1" />
                                Cancel
                              </Button>
                            )}
                          </div>
                        </div>

                        {/* Progress Bar */}
                        <div>
                          <div className="flex justify-between text-sm mb-2">
                            <span>Progress</span>
                            <div className="flex items-center gap-2">
                              <span>{Math.round(calculateProgress())}%</span>
                              {getRealtimeStatus().isActive && (
                                <div className="flex items-center gap-1 text-xs text-blue-600">
                                  <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" />
                                  <span>Active</span>
                                </div>
                              )}
                            </div>
                          </div>
                          <Progress value={calculateProgress()} className="h-2" />
                          {getRealtimeStatus().lastUpdate && (
                            <div className="text-xs text-muted-foreground mt-1">
                              Last updated: {getRealtimeStatus().lastUpdate?.toLocaleTimeString()}
                            </div>
                          )}
                        </div>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Total Tasks</p>
                            <p className="font-semibold">{missionDetails.tasks.length}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Completed</p>
                            <p className="font-semibold text-green-600">
                              {missionDetails.tasks.filter(t => t.status === 'completed').length}
                            </p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">In Progress</p>
                            <p className="font-semibold text-blue-600">
                              {missionDetails.tasks.filter(t => t.status === 'in_progress').length}
                            </p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Failed</p>
                            <p className="font-semibold text-red-600">
                              {missionDetails.tasks.filter(t => t.status === 'failed').length}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {messages.length === 0 && !isLoading ? (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
                      <Bot size={32} className="text-primary/60" />
                    </div>
                    <h2 className="text-xl font-medium text-foreground mb-2">How can I help you today?</h2>
                    <p className="text-sm text-muted-foreground">
                      {selectedMissionId ? "Continue working on your mission or ask for help." : "Start a conversation to begin your mission."}
                    </p>
                  </div>
                </div>
              ) : (
                <ChatMessageList>
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className="w-full flex justify-center px-4 md:px-8 lg:px-12"
                    >
                      <div
                        className={cn(
                          "flex gap-3 w-full max-w-4xl",
                          message.sender === "user" ? "flex-row-reverse" : ""
                        )}
                      >
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
                          {message.sender === "user" ? (
                            <User className="h-4 w-4" />
                          ) : (
                            <Bot className="h-4 w-4" />
                          )}
                        </div>
                        <div
                          className={cn(
                            "rounded-lg px-4 py-3 flex-1 max-w-[75%]",
                            message.sender === "user"
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted"
                          )}
                        >
                          <p className="text-sm">{message.content}</p>
                          <p className="text-xs opacity-70 mt-1">
                            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}

                  {isLoading && (
                    <div className="w-full flex justify-center px-4 md:px-8 lg:px-12">
                      <div className="flex gap-3 w-full max-w-4xl">
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center shrink-0">
                          <Bot className="h-4 w-4" />
                        </div>
                        <div className="rounded-lg px-4 py-3 bg-muted">
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                            <div className="w-2 h-2 bg-current rounded-full animate-pulse delay-100" />
                            <div className="w-2 h-2 bg-current rounded-full animate-pulse delay-200" />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </ChatMessageList>
              )}
            </div>
          ) : (
            <KnowledgeBase />
          )}
        </div>

        {/* Input - Only show for chat view */}
        {currentView === "chat" && (
          <div className="p-4 bg-background">
            <form
              onSubmit={handleSubmit}
              className="relative rounded-xl border border-border/50 bg-background focus-within:border-border focus-within:shadow-sm transition-all duration-200 max-w-4xl mx-auto"
            >
              <ChatInput
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Describe your mission or ask for help..."
                minRows={1}
                maxRows={6}
                className="resize-none rounded-xl bg-transparent border-0 p-4 shadow-none focus-visible:ring-0 focus-visible:outline-none"
              />
              <div className="flex items-center px-4 pb-3 justify-between">
                <div className="flex">
                  <Button
                    variant="ghost"
                    size="icon"
                    type="button"
                    className="h-8 w-8 hover:bg-muted hover:text-muted-foreground"
                  >
                    <Paperclip className="size-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="icon"
                    type="button"
                    className="h-8 w-8 hover:bg-muted hover:text-muted-foreground"
                  >
                    <Mic className="size-4" />
                  </Button>
                </div>
                <Button
                  type="submit"
                  size="icon"
                  className="ml-auto h-8 w-8 rounded-lg"
                  disabled={!input.trim() || isLoading}
                  title="Send Mission"
                >
                  <Send className="size-4" />
                </Button>
              </div>
            </form>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              <DialogTitle>Delete Mission</DialogTitle>
            </div>
            <DialogDescription>
              Are you sure you want to delete "{missionToDelete?.title}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={cancelDeleteMission}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteMission}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default MissionWorkspace 