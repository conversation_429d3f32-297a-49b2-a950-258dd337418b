"use client"

import * as React from "react"
import KnowledgeBase from "@/components/KnowledgeBase"
import { MissionSidebar } from "./mission/MissionSidebar"
import { TopNavigationBar } from "./mission/TopNavigationBar"
import { MissionProgressHeader } from "./mission/MissionProgressHeader"
import { ChatInterface } from "./mission/ChatInterface"
import { DeleteMissionDialog } from "./mission/DeleteMissionDialog"
import { MissionStateProvider, useMissionContext } from "./mission/MissionStateProvider"

/**
 * Internal component that uses the mission context
 */
function MissionWorkspaceContent() {
  // Only keep UI-specific state that doesn't need to be shared
  const [sidebarOpen, setSidebarOpen] = React.useState(true);

  // Get all state and operations from context
  const {
    // State
    missions,
    selectedMissionId,
    currentView,
    messages,
    input,
    missionDetails,
    loadingMissionDetails,
    searchQuery,
    editingMissionId,
    editingValue,
    deleteDialogOpen,
    missionToDelete,

    // Operations
    startNewMission,
    selectMission,
    selectKnowledgeBase,
    openDeleteDialog,
    confirmDeleteMission,
    cancelDeleteMission,
    clearSearch,
    startEditingMission,
    saveEditingMission,
    cancelEditingMission,
    getRealtimeMissionStatus,
    getRealtimeStatus,
    handleSubmit,
    handleKeyDown,
    handleMissionAction,
    setInput,
    setSearchQuery,
    setEditingValue,
  } = useMissionContext();

  // Global keyboard shortcuts
  React.useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Toggle sidebar on Ctrl+B or Cmd+B
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        setSidebarOpen(prev => !prev);
      }

      // New mission on Ctrl+N or Cmd+N
      if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        startNewMission();
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => document.removeEventListener('keydown', handleGlobalKeyDown);
  }, [startNewMission]);

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Sidebar */}
      <MissionSidebar
        isOpen={sidebarOpen}
        missions={missions}
        searchQuery={searchQuery}
        selectedMissionId={selectedMissionId}
        currentView={currentView}
        editingMissionId={editingMissionId}
        editingValue={editingValue}
        getRealtimeMissionStatus={getRealtimeMissionStatus}
        onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
        onStartNewMission={startNewMission}
        onSelectKnowledgeBase={selectKnowledgeBase}
        onSelectMission={selectMission}
        onSearchChange={setSearchQuery}
        onClearSearch={clearSearch}
        onStartEditingMission={startEditingMission}
        onEditValueChange={setEditingValue}
        onSaveEditingMission={saveEditingMission}
        onCancelEditingMission={cancelEditingMission}
        onDeleteMission={openDeleteDialog}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNavigationBar
          sidebarOpen={sidebarOpen}
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
        />

        {/* Main Content Area */}
        <div className="flex-1 overflow-hidden">
          {currentView === "chat" ? (
            <div className="flex flex-col h-full">
              {/* Mission Progress Header - Only show when mission is selected */}
              {selectedMissionId && missionDetails && (
                <MissionProgressHeader
                  missionDetails={missionDetails}
                  realtimeStatus={getRealtimeStatus()}
                  isLoading={loadingMissionDetails}
                  onMissionAction={handleMissionAction}
                />
              )}

              <ChatInterface
                messages={messages}
                input={input}
                isLoading={false}
                selectedMissionId={selectedMissionId}
                onInputChange={setInput}
                onSubmit={handleSubmit}
                onKeyDown={handleKeyDown}
              />
            </div>
          ) : (
            <KnowledgeBase />
          )}
        </div>
      </div>

      {/* Delete Mission Dialog */}
      <DeleteMissionDialog
        isOpen={deleteDialogOpen}
        mission={missionToDelete}
        onConfirm={confirmDeleteMission}
        onCancel={cancelDeleteMission}
      />
    </div>
  );
}

/**
 * Main MissionWorkspace component with context provider
 */
export default function MissionWorkspace() {
  return (
    <MissionStateProvider>
      <MissionWorkspaceContent />
    </MissionStateProvider>
  );
}