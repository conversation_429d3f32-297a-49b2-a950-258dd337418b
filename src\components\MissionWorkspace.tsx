"use client"

import * as React from "react"
import { useState, FormEvent, useEffect } from "react"
import { useMissionUpdates } from "@/hooks/useMissionUpdates"
import KnowledgeBase from "@/components/KnowledgeBase"
import { MissionDetails, Mission, Message, RealtimeStatus } from "./mission/types"
import { MissionSidebar } from "./mission/MissionSidebar"
import { TopNavigationBar } from "./mission/TopNavigationBar"
import { MissionProgressHeader } from "./mission/MissionProgressHeader"
import { ChatInterface } from "./mission/ChatInterface"
import { DeleteMissionDialog } from "./mission/DeleteMissionDialog"

// Main Mission Workspace Component
const MissionWorkspace: React.FC = () => {
  // Core state
  const [messages, setMessages] = useState<Message[]>([])
  const [missionDetails, setMissionDetails] = useState<MissionDetails | null>(null)
  const [loadingMissionDetails, setLoadingMissionDetails] = useState(false)
  const [missions, setMissions] = useState<Mission[]>([
    {
      id: 1,
      title: "Data Analysis Project",
      timestamp: new Date(Date.now() - 86400000),
      status: "completed",
    },
    {
      id: 2,
      title: "Market Research",
      timestamp: new Date(Date.now() - 172800000),
      status: "completed",
    },
    {
      id: 3,
      title: "Content Strategy",
      timestamp: new Date(Date.now() - 259200000),
      status: "in-progress",
    },
  ])

  // UI state
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [currentView, setCurrentView] = useState<"chat" | "knowledge">("chat")
  const [selectedMissionId, setSelectedMissionId] = useState<number | null>(null)
  
  // Dialog and editing state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [missionToDelete, setMissionToDelete] = useState<Mission | null>(null)
  const [editingMissionId, setEditingMissionId] = useState<number | null>(null)
  const [editingValue, setEditingValue] = useState("")
  const [searchQuery, setSearchQuery] = useState("")

  // WebSocket integration for real-time updates
  const {
    isConnected: wsConnected,
    connectionError,
    subscribeMission,
    unsubscribeMission,
    getMission: getWsMission,
    missions: wsMissions,
    clearMission
  } = useMissionUpdates({
    autoConnect: true,
    maxLogs: 50,
  })

  // Effects for mission management
  useEffect(() => {
    if (selectedMissionId) {
      fetchMissionDetails();
      
      // Subscribe to WebSocket updates for this mission
      if (wsConnected) {
        subscribeMission(selectedMissionId.toString());
        
        return () => {
          unsubscribeMission(selectedMissionId.toString());
        };
      }
    } else {
      setMissionDetails(null);
    }
  }, [selectedMissionId, wsConnected, subscribeMission, unsubscribeMission]);

  // Update details from WebSocket data when available
  useEffect(() => {
    if (selectedMissionId && missionDetails) {
      const wsMission = getWsMission(selectedMissionId.toString());
      if (wsMission) {
        // Update mission status and other real-time data
        setMissionDetails((prev: MissionDetails | null) => prev ? {
          ...prev,
          mission: {
            ...prev.mission,
            status: wsMission.status as 'pending' | 'planning' | 'executing' | 'completed' | 'failed',
            updatedAt: wsMission.lastUpdate
          },
          // Update logs with real-time data
          logs: wsMission.logs ? wsMission.logs.map((log: any) => ({
            id: log.id || Math.random().toString(),
            level: log.level,
            message: log.message,
            timestamp: log.timestamp,
            data: log.data
          })) : prev.logs
        } : prev);
      }
    }
  }, [wsMissions, selectedMissionId, getWsMission, missionDetails]);

  // Global keyboard shortcuts
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Toggle sidebar on Ctrl+B or Cmd+B
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault()
        setSidebarOpen(prev => !prev)
      }
      
      // New mission on Ctrl+N or Cmd+N
      if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault()
        startNewMission()
      }
    }

    document.addEventListener('keydown', handleGlobalKeyDown)
    return () => document.removeEventListener('keydown', handleGlobalKeyDown)
  }, [sidebarOpen])

  // Data fetching and actions
  const fetchMissionDetails = async () => {
    if (!selectedMissionId) return;
    
    try {
      setLoadingMissionDetails(true);
      const response = await fetch(`/api/missions/${selectedMissionId}`);
      const result = await response.json();
      if (result.success) {
        setMissionDetails(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch mission details:', error);
    } finally {
      setLoadingMissionDetails(false);
    }
  };

  const handleMissionAction = async (action: 'pause' | 'resume' | 'cancel') => {
    if (!selectedMissionId) return;
    
    try {
      const response = await fetch(`/api/missions/${selectedMissionId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action }),
      });

      if (response.ok) {
        await fetchMissionDetails();
      }
    } catch (error) {
      console.error(`Failed to ${action} mission:`, error);
    }
  };

  // Mission management handlers
  const startNewMission = () => {
    const newMission: Mission = {
      id: missions.length + 1,
      title: "New Mission",
      timestamp: new Date(),
      status: "in-progress",
    }
    setMissions((prev) => [newMission, ...prev])
    setSelectedMissionId(newMission.id)
    setCurrentView("chat")
    setMessages([]) // Start with empty messages to show welcome message
  }

  const selectMission = (missionId: number) => {
    setSelectedMissionId(missionId)
    setCurrentView("chat")
    // Load mission-specific messages here
    setMessages([
      {
        id: 1,
        content: `Loaded mission ${missionId}. How can I help you continue with this mission?`,
        sender: "ai",
        timestamp: new Date(),
      },
    ])
  }

  const selectKnowledgeBase = () => {
    setCurrentView("knowledge")
    setSelectedMissionId(null) // Clear mission selection when switching to knowledge base
  }

  const renameMission = (missionId: number, newTitle: string) => {
    setMissions(prev =>
      prev.map(mission =>
        mission.id === missionId ? { ...mission, title: newTitle } : mission
      )
    )
  }

  // Dialog handlers
  const openDeleteDialog = (mission: Mission) => {
    setMissionToDelete(mission)
    setDeleteDialogOpen(true)
  }

  const confirmDeleteMission = () => {
    if (missionToDelete) {
      setMissions(prev => prev.filter(mission => mission.id !== missionToDelete.id))
      if (selectedMissionId === missionToDelete.id) {
        setSelectedMissionId(null)
        setCurrentView("chat")
      }
      setDeleteDialogOpen(false)
      setMissionToDelete(null)
    }
  }

  const cancelDeleteMission = () => {
    setDeleteDialogOpen(false)
    setMissionToDelete(null)
  }

  // Editing handlers
  const startEditingMission = (mission: Mission) => {
    setEditingMissionId(mission.id)
    setEditingValue(mission.title)
  }

  const saveEditingMission = () => {
    if (editingMissionId) {
      const trimmedValue = editingValue.trim()
      
      // Validation
      if (!trimmedValue) {
        // Don't save empty names, just cancel
        cancelEditingMission()
        return
      }
      
      if (trimmedValue.length > 100) {
        // Show error feedback but don't save
        return
      }
      
      renameMission(editingMissionId, trimmedValue)
      setEditingMissionId(null)
      setEditingValue("")
    }
  }

  const cancelEditingMission = () => {
    setEditingMissionId(null)
    setEditingValue("")
  }

  // Chat handlers
  const handleSubmit = (e: FormEvent) => {
    e.preventDefault()
    if (!input.trim()) return

    const newMessage: Message = {
      id: messages.length + 1,
      content: input,
      sender: "user",
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, newMessage])
    setInput("")
    setIsLoading(true)

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = {
        id: messages.length + 2,
        content: "I understand your request. Let me help you with that mission. I'll analyze the requirements and provide you with a comprehensive solution.",
        sender: "ai",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, aiResponse])
      setIsLoading(false)
    }, 1500)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && e.ctrlKey) {
      e.preventDefault()
      handleSubmit(e as any)
    }
  }

  // Utility functions
  const getRealtimeMissionStatus = (missionId: number): RealtimeStatus | null => {
    const wsMission = getWsMission(missionId.toString())
    if (wsMission) {
      return {
        status: wsMission.status,
        isActive: wsMission.status === 'executing' || wsMission.status === 'planning',
        progress: wsMission.progress,
        lastUpdate: new Date(wsMission.lastUpdate),
        currentTask: wsMission.progress?.currentTask || null
      }
    }
    return null
  }

  const getRealtimeStatus = (): RealtimeStatus => {
    if (selectedMissionId) {
      const wsMission = getWsMission(selectedMissionId.toString());
      if (wsMission) {
        return {
          status: wsMission.status,
          isActive: wsMission.status === 'executing' || wsMission.status === 'planning',
          lastUpdate: new Date(wsMission.lastUpdate),
          currentTask: wsMission.progress?.currentTask || null,
          progress: wsMission.progress
        };
      }
    }
    
    return {
      status: missionDetails?.mission.status || 'pending',
      isActive: false,
      lastUpdate: null,
      currentTask: null
    };
  };

  // User profile handlers
  const handleCustomizeAssistant = () => {
    console.log("Customize Assistant clicked")
    // TODO: Implement assistant customization
  }

  const handleSettings = () => {
    console.log("Settings clicked")
    // TODO: Implement settings page
  }

  const handleHelp = () => {
    console.log("Help clicked")
    // TODO: Implement help documentation
  }

  const handleLogout = () => {
    console.log("Logout clicked")
    // TODO: Implement logout functionality
  }

  const clearSearch = () => {
    setSearchQuery("")
  }

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Sidebar */}
      <MissionSidebar
        isOpen={sidebarOpen}
        missions={missions}
        searchQuery={searchQuery}
        selectedMissionId={selectedMissionId}
        currentView={currentView}
        editingMissionId={editingMissionId}
        editingValue={editingValue}
        getRealtimeMissionStatus={getRealtimeMissionStatus}
        onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
        onStartNewMission={startNewMission}
        onSelectKnowledgeBase={selectKnowledgeBase}
        onSelectMission={selectMission}
        onSearchChange={setSearchQuery}
        onClearSearch={clearSearch}
        onStartEditingMission={startEditingMission}
        onEditValueChange={setEditingValue}
        onSaveEditingMission={saveEditingMission}
        onCancelEditingMission={cancelEditingMission}
        onDeleteMission={openDeleteDialog}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Navigation Bar */}
        <TopNavigationBar
          currentView={currentView}
          wsConnected={wsConnected}
          connectionError={connectionError}
          onCustomizeAssistant={handleCustomizeAssistant}
          onSettings={handleSettings}
          onHelp={handleHelp}
          onLogout={handleLogout}
        />

        {/* Main Content Area */}
        <div className="flex-1 overflow-hidden">
          {currentView === "chat" ? (
            <div className="flex flex-col h-full">
              {/* Mission Progress Header - Only show when mission is selected */}
              {selectedMissionId && missionDetails && (
                <MissionProgressHeader
                  missionDetails={missionDetails}
                  realtimeStatus={getRealtimeStatus()}
                  isLoading={loadingMissionDetails}
                  onMissionAction={handleMissionAction}
                />
              )}

              <ChatInterface
                messages={messages}
                input={input}
                isLoading={isLoading}
                selectedMissionId={selectedMissionId}
                onInputChange={setInput}
                onSubmit={handleSubmit}
                onKeyDown={handleKeyDown}
              />
            </div>
          ) : (
            <KnowledgeBase />
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteMissionDialog
        isOpen={deleteDialogOpen}
        mission={missionToDelete}
        onConfirm={confirmDeleteMission}
        onCancel={cancelDeleteMission}
      />
    </div>
  )
}

export default MissionWorkspace