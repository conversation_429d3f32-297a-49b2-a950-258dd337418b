import { NextRequest, NextResponse } from 'next/server';
import { cacheManager } from '@/lib/cache/cache-manager';

/**
 * GET /api/cache - Get cache statistics and health
 */
export async function GET() {
  try {
    const [statistics, hitRate, health] = await Promise.all([
      cacheManager.getStatistics(),
      cacheManager.getHitRate(),
      cacheManager.healthCheck(),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        statistics,
        hitRate,
        health,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Error getting cache information:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get cache information',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/cache - Cache management operations
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, cacheType, sessionId, key, data, ttl } = body;

    switch (action) {
      case 'clear': {
        if (!cacheType) {
          return NextResponse.json(
            {
              success: false,
              error: 'Cache type is required for clear operation',
              validTypes: ['embedding', 'search', 'session', 'api', 'document', 'completion', 'all'],
            },
            { status: 400 }
          );
        }

        cacheManager.clearCache(cacheType);
        
        return NextResponse.json({
          success: true,
          data: {
            action: 'clear',
            cacheType,
            message: `Cache ${cacheType} cleared successfully`,
          },
        });
      }

      case 'optimize': {
        const result = await cacheManager.optimizeCache();
        
        return NextResponse.json({
          success: true,
          data: {
            action: 'optimize',
            ...result,
            message: `Cache optimization completed: ${result.cleared} keys cleared`,
          },
        });
      }

      case 'setSession': {
        if (!sessionId || !key || data === undefined) {
          return NextResponse.json(
            {
              success: false,
              error: 'sessionId, key, and data are required for setSession operation',
            },
            { status: 400 }
          );
        }

        cacheManager.setSessionData(sessionId, key, data, ttl);
        
        return NextResponse.json({
          success: true,
          data: {
            action: 'setSession',
            sessionId,
            key,
            message: 'Session data cached successfully',
          },
        });
      }

      case 'getSession': {
        if (!sessionId || !key) {
          return NextResponse.json(
            {
              success: false,
              error: 'sessionId and key are required for getSession operation',
            },
            { status: 400 }
          );
        }

        const sessionData = cacheManager.getSessionData(sessionId, key);
        
        return NextResponse.json({
          success: true,
          data: {
            action: 'getSession',
            sessionId,
            key,
            data: sessionData,
            found: sessionData !== null,
          },
        });
      }

      case 'deleteSession': {
        if (!sessionId) {
          return NextResponse.json(
            {
              success: false,
              error: 'sessionId is required for deleteSession operation',
            },
            { status: 400 }
          );
        }

        cacheManager.deleteSessionData(sessionId, key);
        
        return NextResponse.json({
          success: true,
          data: {
            action: 'deleteSession',
            sessionId,
            key: key || 'all',
            message: key ? 'Session key deleted' : 'All session data deleted',
          },
        });
      }

      case 'warmup': {
        // Warm up cache with common operations
        try {
          // Initialize cache manager if not already done
          cacheManager.initialize();

          // Simulate some common cache operations to test performance
          const testOperations = [
            () => cacheManager.setApiResponse('/test', {}, { test: 'data' }),
            () => cacheManager.getApiResponse('/test', {}),
            () => cacheManager.setSessionData('warmup-session', 'test', { warm: true }),
            () => cacheManager.getSessionData('warmup-session', 'test'),
          ];

          for (const operation of testOperations) {
            operation();
          }

          // Clean up test data
          cacheManager.deleteSessionData('warmup-session');

          return NextResponse.json({
            success: true,
            data: {
              action: 'warmup',
              message: 'Cache warmup completed successfully',
              operations: testOperations.length,
            },
          });
        } catch (error) {
          return NextResponse.json(
            {
              success: false,
              error: 'Cache warmup failed',
              details: error instanceof Error ? error.message : 'Unknown error',
            },
            { status: 500 }
          );
        }
      }

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid action',
            validActions: ['clear', 'optimize', 'setSession', 'getSession', 'deleteSession', 'warmup'],
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in cache operation:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Cache operation failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/cache - Clear all caches
 */
export async function DELETE() {
  try {
    cacheManager.clearCache('all');
    
    return NextResponse.json({
      success: true,
      data: {
        action: 'clear_all',
        message: 'All caches cleared successfully',
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error('Error clearing all caches:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to clear all caches',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}