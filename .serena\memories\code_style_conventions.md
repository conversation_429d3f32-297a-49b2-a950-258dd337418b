# Code Style & Conventions

## TypeScript Configuration
- **Target**: ES2017
- **Module**: ESNext with bundler resolution
- **Strict Mode**: Enabled
- **Path Mapping**: `@/*` maps to `./src/*`

## Code Structure
- **Components**: Located in `src/components/`
- **API Routes**: Next.js App Router in `src/app/api/`
- **Agent Logic**: Core agent functionality in `src/lib/agent/`
- **Database**: Schema and operations in `src/lib/db/`
- **Utilities**: Helper functions in `src/lib/utils.ts`

## Component Structure
- React functional components with TypeScript
- Props interfaces defined with clear types
- Use of Radix UI primitives for accessible components
- Consistent use of `cn()` utility for class merging

## Database Patterns
- Drizzle ORM with TypeScript schema definitions
- Type-safe database operations
- Separate types for inserts vs selects (New* types)

## Agent Architecture
- Interface-based design with clear capability separation
- Dependency injection pattern for DeepSeek client
- Class-based implementations with clear method contracts

## Naming Conventions
- **Files**: kebab-case for components, camelCase for utilities
- **Components**: PascalCase
- **Functions/Variables**: camelCase
- **Constants**: UPPER_SNAKE_CASE for true constants
- **Types/Interfaces**: PascalCase