#!/usr/bin/env tsx

import { startWebSocketServer, getWebSocketServer } from '../src/lib/websocket/server';
import { ServiceManagementAgentImpl } from '../src/lib/agent/core';
import { DeepSeekClient } from '../src/lib/agent/deepseek';

const PORT = parseInt(process.env.WEBSOCKET_PORT || '8080');
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;

async function main() {
  console.log('🚀 Starting WebSocket server...');

  if (!DEEPSEEK_API_KEY) {
    console.warn('⚠️  Warning: DEEPSEEK_API_KEY not set. Some features may not work.');
  }

  try {
    // Start WebSocket server
    const wsServer = await startWebSocketServer(PORT);
    
    console.log(`✅ WebSocket server started on port ${PORT}`);
    console.log(`📊 Server stats:`, wsServer.getStats());

    // Handle graceful shutdown
    const gracefulShutdown = async (signal: string) => {
      console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
      
      try {
        await wsServer.stop();
        console.log('✅ WebSocket server stopped gracefully');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };

    // Register shutdown handlers
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Setup WebSocket server event handlers
    wsServer.on('client.disconnected', ({ clientId, client }) => {
      console.log(`👋 Client disconnected: ${clientId}`);
    });

    wsServer.on('mission.status.request', async ({ clientId, missionId }) => {
      console.log(`📋 Status request for mission ${missionId} from ${clientId}`);
      
      // You could implement mission status lookup here
      // For now, just acknowledge the request
      wsServer.broadcastMissionUpdate({
        missionId,
        status: 'pending',
        message: 'Status request received',
      });
    });

    wsServer.on('client.message', ({ clientId, message }) => {
      console.log(`💬 Message from ${clientId}:`, message.type);
    });

    // Keep the server running
    console.log('🔄 WebSocket server running. Press Ctrl+C to stop.');
    
    // Periodic stats logging
    setInterval(() => {
      const stats = wsServer.getStats();
      if (stats.connectedClients > 0) {
        console.log(`📊 Connected clients: ${stats.connectedClients}, Active missions: ${stats.activeMissions}`);
      }
    }, 30000); // Every 30 seconds

  } catch (error) {
    console.error('❌ Failed to start WebSocket server:', error);
    process.exit(1);
  }
}

// Run the server
main().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});