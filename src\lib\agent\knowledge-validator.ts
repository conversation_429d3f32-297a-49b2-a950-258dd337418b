import { Task, Plan, ExecutionContext, ToolResult } from './types';
import { AgentLogger } from './logger';
import { ERROR_CODES } from '../constants';

/**
 * Knowledge Base Validation - Ensures strict adherence to knowledge-base-first principle
 */
export class KnowledgeBaseValidator {
  private static readonly KNOWLEDGE_TOOLS = [
    'knowledge_base_query',
    'incident_query',
    'confluence_search',
    'document_search',
    'vector_search'
  ];

  private static readonly REASONING_TOOLS = [
    'deepseek_reasoning',
    'analysis_tool',
    'summary_generator',
    'report_generator'
  ];

  /**
   * Validate that a plan follows knowledge-base-first principle
   */
  static validatePlan(plan: Plan, context: ExecutionContext): ValidationResult {
    const logger = context.logger;
    const issues: ValidationIssue[] = [];

    // Check if plan has any tasks
    if (!plan.tasks || plan.tasks.length === 0) {
      issues.push({
        type: 'error',
        code: 'NO_TASKS',
        message: 'Plan has no tasks defined',
        taskId: null
      });
      return { isValid: false, issues };
    }

    // Check if first task is a knowledge retrieval task
    const firstTask = plan.tasks[0];
    if (!this.isKnowledgeRetrievalTask(firstTask)) {
      issues.push({
        type: 'error',
        code: 'MISSING_KNOWLEDGE_FIRST',
        message: 'First task must be a knowledge base query. No reasoning or responses should be generated without retrieving relevant information first.',
        taskId: firstTask.id
      });
    }

    // Check for reasoning tasks without prior knowledge retrieval
    let hasKnowledgeRetrieval = this.isKnowledgeRetrievalTask(firstTask);
    
    for (let i = 1; i < plan.tasks.length; i++) {
      const task = plan.tasks[i];
      
      if (this.isReasoningTask(task) && !hasKnowledgeRetrieval) {
        issues.push({
          type: 'error',
          code: 'REASONING_WITHOUT_KNOWLEDGE',
          message: `Task "${task.title}" performs reasoning without prior knowledge retrieval`,
          taskId: task.id
        });
      }

      if (this.isKnowledgeRetrievalTask(task)) {
        hasKnowledgeRetrieval = true;
      }
    }

    // Check for tasks that might generate responses without knowledge
    for (const task of plan.tasks) {
      if (this.isResponseGenerationTask(task) && !this.hasKnowledgeDependency(task, plan.tasks)) {
        issues.push({
          type: 'error',
          code: 'RESPONSE_WITHOUT_KNOWLEDGE',
          message: `Task "${task.title}" generates responses without depending on knowledge retrieval`,
          taskId: task.id
        });
      }
    }

    const isValid = issues.filter(issue => issue.type === 'error').length === 0;
    
    if (isValid) {
      logger.info('Plan validation passed: Knowledge-base-first principle enforced');
    } else {
      logger.error(`Plan validation failed: ${issues.length} issues found`);
    }

    return { isValid, issues };
  }

  /**
   * Validate task execution context has required knowledge
   */
  static validateTaskExecution(task: Task, context: ExecutionContext, previousResults: ToolResult[]): ValidationResult {
    const logger = context.logger;
    const issues: ValidationIssue[] = [];

    // If this is a reasoning or response generation task, ensure we have knowledge context
    if (this.isReasoningTask(task) || this.isResponseGenerationTask(task)) {
      const hasKnowledgeContext = this.hasKnowledgeInContext(previousResults);
      
      if (!hasKnowledgeContext) {
        issues.push({
          type: 'error',
          code: 'NO_KNOWLEDGE_CONTEXT',
          message: `Task "${task.title}" requires knowledge context but none found in previous results`,
          taskId: task.id
        });
      }
    }

    // Check if knowledge retrieval task returned meaningful data
    if (this.isKnowledgeRetrievalTask(task)) {
      // This will be validated after execution
    }

    const isValid = issues.filter(issue => issue.type === 'error').length === 0;
    return { isValid, issues };
  }

  /**
   * Validate knowledge retrieval result
   */
  static validateKnowledgeResult(result: ToolResult, context: ExecutionContext): ValidationResult {
    const logger = context.logger;
    const issues: ValidationIssue[] = [];

    if (!result.success) {
      issues.push({
        type: 'error',
        code: 'KNOWLEDGE_RETRIEVAL_FAILED',
        message: `Knowledge retrieval failed: ${result.error}`,
        taskId: null
      });
      return { isValid: false, issues };
    }

    // Check if we have meaningful data
    const hasData = result.data?.hasData === true || 
                   (result.data?.results && result.data.results.length > 0) ||
                   (result.data?.incidents && result.data.incidents.length > 0);

    if (!hasData) {
      issues.push({
        type: 'warning',
        code: 'NO_KNOWLEDGE_FOUND',
        message: 'No relevant information found in knowledge base. Cannot proceed with reasoning without supporting data.',
        taskId: null
      });
      
      // This is a critical issue - we should not proceed without knowledge
      issues[0].type = 'error';
    }

    const isValid = issues.filter(issue => issue.type === 'error').length === 0;
    
    if (!isValid) {
      logger.warn('Knowledge retrieval validation failed - no supporting data found');
    }

    return { isValid, issues };
  }

  /**
   * Generate knowledge-first task sequence
   */
  static generateKnowledgeFirstTasks(originalTasks: Partial<Task>[], missionDescription: string): Partial<Task>[] {
    const knowledgeFirstTasks: Partial<Task>[] = [];

    // Always start with knowledge base query
    knowledgeFirstTasks.push({
      title: 'Retrieve Relevant Information',
      description: `Query the knowledge base to find information relevant to: ${missionDescription}`,
      priority: 1,
      toolName: 'knowledge_base_query',
      toolParams: {
        query: missionDescription,
        limit: 10,
        threshold: 0.7,
        searchMode: 'hybrid'
      },
      dependencies: []
    });

    // Add specialized queries if needed
    if (this.requiresIncidentData(missionDescription)) {
      knowledgeFirstTasks.push({
        title: 'Query Incident Data',
        description: 'Retrieve specific incident information from the knowledge base',
        priority: 2,
        toolName: 'incident_query',
        toolParams: this.extractIncidentParams(missionDescription),
        dependencies: []
      });
    }

    // Add original tasks with updated dependencies and priorities
    let priority = knowledgeFirstTasks.length + 1;
    for (const task of originalTasks) {
      const updatedTask = { ...task };
      
      // Ensure all non-knowledge tasks depend on knowledge retrieval
      if (!this.isKnowledgeRetrievalTask(task as Task)) {
        updatedTask.dependencies = [
          ...(updatedTask.dependencies || []),
          'knowledge_retrieval' // Reference to first task
        ];
      }
      
      updatedTask.priority = priority++;
      knowledgeFirstTasks.push(updatedTask);
    }

    return knowledgeFirstTasks;
  }

  /**
   * Check if task is a knowledge retrieval task
   */
  private static isKnowledgeRetrievalTask(task: Task): boolean {
    return task.toolName ? this.KNOWLEDGE_TOOLS.includes(task.toolName) : false;
  }

  /**
   * Check if task is a reasoning task
   */
  private static isReasoningTask(task: Task): boolean {
    if (task.toolName && this.REASONING_TOOLS.includes(task.toolName)) {
      return true;
    }
    
    // Check task description for reasoning keywords
    const reasoningKeywords = ['analyze', 'reasoning', 'conclude', 'infer', 'deduce', 'synthesize'];
    const description = (task.description || '').toLowerCase();
    return reasoningKeywords.some(keyword => description.includes(keyword));
  }

  /**
   * Check if task generates responses
   */
  private static isResponseGenerationTask(task: Task): boolean {
    const responseKeywords = ['respond', 'answer', 'reply', 'generate response', 'provide answer'];
    const description = (task.description || '').toLowerCase();
    const title = (task.title || '').toLowerCase();
    
    return responseKeywords.some(keyword => 
      description.includes(keyword) || title.includes(keyword)
    );
  }

  /**
   * Check if task has knowledge dependency
   */
  private static hasKnowledgeDependency(task: Task, allTasks: Task[]): boolean {
    if (!task.dependencies || task.dependencies.length === 0) {
      return false;
    }

    // Check if any dependency is a knowledge retrieval task
    for (const depId of task.dependencies) {
      const depTask = allTasks.find(t => t.id === depId);
      if (depTask && this.isKnowledgeRetrievalTask(depTask)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Check if previous results contain knowledge
   */
  private static hasKnowledgeInContext(previousResults: ToolResult[]): boolean {
    return previousResults.some(result => 
      result.success && 
      result.metadata?.toolName && 
      this.KNOWLEDGE_TOOLS.includes(result.metadata.toolName) &&
      (result.data?.hasData === true || 
       (result.data?.results && result.data.results.length > 0))
    );
  }

  /**
   * Check if mission requires incident data
   */
  private static requiresIncidentData(description: string): boolean {
    const incidentKeywords = ['incident', 'outage', 'failure', 'error', 'issue', 'problem', 'downtime'];
    return incidentKeywords.some(keyword => description.toLowerCase().includes(keyword));
  }

  /**
   * Extract incident query parameters from mission description
   */
  private static extractIncidentParams(description: string): Record<string, any> {
    const params: Record<string, any> = {};
    
    // Extract service name
    const serviceMatch = description.match(/(?:for|of|in)\s+([a-zA-Z\s]+?)(?:\s+service|$)/i);
    if (serviceMatch) {
      params.service = serviceMatch[1].trim();
    }

    // Extract time range
    if (description.includes('last week')) {
      params.timeRange = 'last_week';
    } else if (description.includes('last month')) {
      params.timeRange = 'last_month';
    } else if (description.includes('last year')) {
      params.timeRange = 'last_year';
    }

    // Extract severity
    const severityMatch = description.match(/\b(critical|high|medium|low)\s+(?:severity|priority)/i);
    if (severityMatch) {
      params.severity = severityMatch[1].toLowerCase();
    }

    return params;
  }
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  issues: ValidationIssue[];
}

/**
 * Validation issue interface
 */
export interface ValidationIssue {
  type: 'error' | 'warning' | 'info';
  code: string;
  message: string;
  taskId: string | null;
}

/**
 * Knowledge base audit logger
 */
export class KnowledgeAuditLogger {
  private static logs: KnowledgeAuditLog[] = [];

  static logKnowledgeQuery(query: string, results: any, context: ExecutionContext): void {
    this.logs.push({
      timestamp: new Date(),
      type: 'knowledge_query',
      missionId: context.missionId,
      taskId: context.taskId,
      query,
      resultCount: results?.length || 0,
      hasData: results?.length > 0,
      details: { results }
    });

    context.logger.info(`Knowledge audit: Query "${query}" returned ${results?.length || 0} results`);
  }

  static logValidationFailure(validation: ValidationResult, context: ExecutionContext): void {
    this.logs.push({
      timestamp: new Date(),
      type: 'validation_failure',
      missionId: context.missionId,
      taskId: context.taskId,
      query: null,
      resultCount: 0,
      hasData: false,
      details: { validation }
    });

    context.logger.error(`Knowledge validation failed: ${validation.issues.length} issues`);
  }

  static getAuditTrail(missionId?: string): KnowledgeAuditLog[] {
    if (missionId) {
      return this.logs.filter(log => log.missionId === missionId);
    }
    return [...this.logs];
  }

  static clearAuditTrail(): void {
    this.logs = [];
  }
}

/**
 * Knowledge audit log interface
 */
export interface KnowledgeAuditLog {
  timestamp: Date;
  type: 'knowledge_query' | 'validation_failure' | 'reasoning_without_knowledge';
  missionId?: string;
  taskId?: string;
  query: string | null;
  resultCount: number;
  hasData: boolean;
  details: any;
}
