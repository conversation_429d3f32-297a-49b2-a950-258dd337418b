import { DeepSeekClient, createDeepSeekClient } from '../agent/deepseek';
import { cacheManager } from '@/lib/cache/cache-manager';
import { ChromaVectorStore } from './chroma';
import { EnhancedDocumentProcessor } from '@/lib/llamaindex/enhanced-processor';
import { DocumentProcessor } from '@/lib/llamaindex/index';
import { errorHandler } from '../error';

/**
 * Service manager for vector store and document processing
 * Provides a unified interface for all document operations
 */
export class VectorStoreServiceManager {
  private static instance: VectorStoreServiceManager | null = null;
  
  private deepseekClient: DeepSeekClient | null = null;
  private vectorStore: ChromaVectorStore | null = null;
  private enhancedProcessor: EnhancedDocumentProcessor | null = null;
  private fallbackProcessor: DocumentProcessor | null = null;
  private isInitialized = false;
  private chromaAvailable = false;

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): VectorStoreServiceManager {
    if (!VectorStoreServiceManager.instance) {
      VectorStoreServiceManager.instance = new VectorStoreServiceManager();
    }
    return VectorStoreServiceManager.instance;
  }

  /**
   * Initialize the service manager
   */
  async initialize(config: {
    chromaHost?: string;
    chromaPort?: number;
    deepseekApiKey?: string;
  } = {}): Promise<void> {
    try {
      console.log('Initializing Vector Store Service Manager...');

      // Initialize cache manager
      cacheManager.initialize();

      // Initialize DeepSeek client
      if (config.deepseekApiKey) {
        this.deepseekClient = createDeepSeekClient({
          apiKey: config.deepseekApiKey,
          baseURL: 'https://api.deepseek.com',
          maxRetries: 3,
          timeout: 30000,
        });
      }

      // Always create fallback processor
      this.fallbackProcessor = new DocumentProcessor(this.deepseekClient as DeepSeekClient);

      // Initialize ChromaDB vector store
      this.vectorStore = new ChromaVectorStore(
        config.chromaHost || 'localhost',
        config.chromaPort || 8000
      );

      // Test Chroma connection
      this.chromaAvailable = await this.vectorStore.healthCheck();
      
      if (this.chromaAvailable) {
        this.enhancedProcessor = new EnhancedDocumentProcessor(
          this.deepseekClient as DeepSeekClient,
          this.vectorStore
        );
        console.log('✅ Chroma vector store initialized successfully');
      } else {
        console.warn('⚠️ Chroma not available, using fallback processor');
      }

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize service manager:', error);
      throw error;
    }
  }

  /**
   * Get the appropriate document processor
   */
  getDocumentProcessor(): EnhancedDocumentProcessor | DocumentProcessor {
    if (!this.isInitialized) {
      throw new Error('Service manager not initialized. Call initialize() first.');
    }

    if (this.chromaAvailable && this.enhancedProcessor) {
      return this.enhancedProcessor;
    }

    if (this.fallbackProcessor) {
      return this.fallbackProcessor;
    }

    throw new Error('No document processor available');
  }

  /**
   * Get vector store (only available if Chroma is running)
   */
  getVectorStore(): ChromaVectorStore | null {
    return this.vectorStore;
  }

  /**
   * Check if enhanced features are available
   */
  isEnhanced(): boolean {
    return this.chromaAvailable && this.enhancedProcessor !== null;
  }

  /**
   * Get service status
   */
  getStatus(): {
    initialized: boolean;
    chromaAvailable: boolean;
    enhanced: boolean;
    version: string;
  } {
    return {
      initialized: this.isInitialized,
      chromaAvailable: this.chromaAvailable,
      enhanced: this.isEnhanced(),
      version: '1.0.0',
    };
  }

  /**
   * Process document with automatic fallback
   */
  async processDocument(
    content: string,
    metadata: {
      title: string;
      sourceUrl: string;
      sourceType: 'confluence' | 'file' | 'web';
      tags?: string[];
    }
  ) {
    const processor = this.getDocumentProcessor();
    
    const documentMetadata = {
      ...metadata,
      lastIndexed: new Date(),
    };

    if (this.isEnhanced()) {
      // Use enhanced processor
      return (processor as EnhancedDocumentProcessor).processDocument(
        content,
        documentMetadata
      );
    } else {
      // Use fallback processor
      return (processor as DocumentProcessor).processDocument(
        content,
        documentMetadata
      );
    }
  }

  /**
   * Search documents with automatic fallback
   */
  async searchDocuments(
    query: string,
    options: {
      limit?: number;
      threshold?: number;
      sourceType?: string;
      useChunks?: boolean;
    } = {}
  ) {
    const processor = this.getDocumentProcessor();

    if (this.isEnhanced()) {
      // Use enhanced search
      return (processor as EnhancedDocumentProcessor).searchDocuments(query, options);
    } else {
      // Use fallback search
      return (processor as DocumentProcessor).searchDocuments(query, options);
    }
  }

  /**
   * Get statistics about the vector store
   */
  async getStatistics() {
    if (this.isEnhanced()) {
      return (this.enhancedProcessor as EnhancedDocumentProcessor).getStatistics();
    }

    // Fallback statistics from database
    try {
      const db = await import('@/lib/db');
      const schema = await import('@/lib/db/schema');
      
      const docs = await db.default.select({
        count: schema.knowledgeBase.id
      }).from(schema.knowledgeBase);

      return {
        documents: docs.length,
        chunks: 0,
        collections: ['fallback'],
        isHealthy: true,
      };
    } catch (error) {
      return {
        documents: 0,
        chunks: 0,
        collections: [],
        isHealthy: false,
      };
    }
  }

  /**
   * Attempt to reconnect to Chroma
   */
  async reconnectChroma(chromaUrl?: string): Promise<boolean> {
    try {
      if (!this.vectorStore) {
        this.vectorStore = new ChromaVectorStore(
          this.deepseekClient as DeepSeekClient,
          chromaUrl || process.env.CHROMA_URL || 'http://localhost:8000'
        );
      }

      this.chromaAvailable = await this.vectorStore.healthCheck();
      
      if (this.chromaAvailable && !this.enhancedProcessor) {
        this.enhancedProcessor = new EnhancedDocumentProcessor(
          this.deepseekClient as DeepSeekClient,
          this.vectorStore
        );
      }

      console.log(`Chroma reconnection: ${this.chromaAvailable ? 'success' : 'failed'}`);
      return this.chromaAvailable;
    } catch (error) {
      console.error('Failed to reconnect to Chroma:', error);
      return false;
    }
  }

  /**
   * Migrate from legacy JSON storage to vector store
   */
  async migrateToVectorStore(): Promise<{
    success: boolean;
    migrated: number;
    errors: number;
    message: string;
  }> {
    if (!this.isEnhanced()) {
      return {
        success: false,
        migrated: 0,
        errors: 0,
        message: 'Vector store not available. Ensure Chroma is running.',
      };
    }

    try {
      console.log('Starting migration to vector store...');
      
      const result = await (this.enhancedProcessor as EnhancedDocumentProcessor).reindexDocuments();
      
      return {
        success: true,
        migrated: result.processed,
        errors: result.errors,
        message: `Migration completed: ${result.processed} documents migrated, ${result.errors} errors`,
      };
    } catch (error) {
      console.error('Migration failed:', error);
      return {
        success: false,
        migrated: 0,
        errors: 1,
        message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Health check for all services
   */
  async healthCheck(): Promise<{
    chroma: boolean;
    deepseek: boolean;
    overall: boolean;
  }> {
    const health = {
      chroma: false,
      deepseek: false,
      overall: false,
    };

    try {
      // Test ChromaDB connection
      if (this.vectorStore) {
        await this.vectorStore.listCollections();
        health.chroma = true;
      }
    } catch (error) {
      console.warn('ChromaDB health check failed:', error);
    }

    try {
      // Test DeepSeek API
      if (this.deepseekClient) {
        // Simple API test - you might want to implement a health check method
        health.deepseek = true;
      }
    } catch (error) {
      console.warn('DeepSeek health check failed:', error);
    }

    health.overall = health.chroma && health.deepseek;
    return health;
  }

  async reset(): Promise<void> {
    try {
      if (this.vectorStore) {
        await this.vectorStore.reset();
      }
      console.log('Vector store service manager reset complete');
    } catch (error) {
      console.error('Failed to reset vector store service manager:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const vectorStoreService = VectorStoreServiceManager.getInstance();