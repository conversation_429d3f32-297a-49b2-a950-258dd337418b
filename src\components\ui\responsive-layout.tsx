import * as React from 'react';
import { cn } from '@/lib/utils';
import { useIsMobile, useResponsiveSidebar, useSafeAreaInsets } from '@/lib/ui/responsive';
import { useFocusManagement, defaultSkipLinks } from '@/lib/ui/accessibility';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  className?: string;
  sidebarClassName?: string;
  mainClassName?: string;
  headerClassName?: string;
  footerClassName?: string;
  skipLinks?: Array<{ href: string; label: string }>;
  sidebarCollapsible?: boolean;
}

export function ResponsiveLayout({
  children,
  sidebar,
  header,
  footer,
  className,
  sidebarClassName,
  mainClassName,
  headerClassName,
  footerClassName,
  skipLinks = defaultSkipLinks,
  sidebarCollapsible = true
}: ResponsiveLayoutProps) {
  const isMobile = useIsMobile();
  const { isOpen, toggle, close, shouldShowOverlay } = useResponsiveSidebar();
  const { setFocus } = useFocusManagement();
  const safeAreaInsets = useSafeAreaInsets();
  
  // Close sidebar on escape key
  React.useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && isMobile) {
        close();
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, isMobile, close]);
  
  // Focus management for sidebar
  const sidebarRef = React.useRef<HTMLElement>(null);
  const mainRef = React.useRef<HTMLElement>(null);
  
  const handleSidebarToggle = () => {
    if (isMobile) {
      if (!isOpen) {
        toggle();
        // Focus first focusable element in sidebar
        setTimeout(() => {
          const firstFocusable = sidebarRef.current?.querySelector(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
          ) as HTMLElement;
          firstFocusable?.focus();
        }, 100);
      } else {
        close();
        // Return focus to toggle button or main content
        setTimeout(() => {
          const toggleButton = document.querySelector('[data-sidebar-toggle]') as HTMLElement;
          toggleButton?.focus() || mainRef.current?.focus();
        }, 100);
      }
    }
  };
  
  return (
    <div 
      className={cn('min-h-screen bg-background', className)}
      style={{
        paddingTop: safeAreaInsets.top,
        paddingLeft: safeAreaInsets.left,
        paddingRight: safeAreaInsets.right,
        paddingBottom: safeAreaInsets.bottom
      }}
    >
      {/* Skip Links for Accessibility */}
      <div className="sr-only focus-within:not-sr-only">
        <div className="fixed top-0 left-0 z-50 flex gap-2 p-2 bg-background border-b">
          {skipLinks.map((link) => (
            <a
              key={link.href}
              href={link.href}
              className="px-3 py-2 text-sm font-medium text-primary underline-offset-4 hover:underline focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-md"
            >
              {link.label}
            </a>
          ))}
        </div>
      </div>
      
      {/* Header */}
      {header && (
        <header 
          className={cn(
            'sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60',
            headerClassName
          )}
        >
          <div className="flex h-14 items-center px-4">
            {sidebar && sidebarCollapsible && isMobile && (
              <button
                onClick={handleSidebarToggle}
                data-sidebar-toggle
                className="mr-2 inline-flex items-center justify-center rounded-md p-2 text-muted-foreground hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 md:hidden"
                aria-label={isOpen ? 'Close sidebar' : 'Open sidebar'}
                aria-expanded={isOpen}
                aria-controls="sidebar"
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  {isOpen ? (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  ) : (
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  )}
                </svg>
              </button>
            )}
            {header}
          </div>
        </header>
      )}
      
      <div className="flex">
        {/* Sidebar */}
        {sidebar && (
          <>
            {/* Desktop Sidebar */}
            <aside
              ref={sidebarRef}
              id="sidebar"
              className={cn(
                'hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0',
                header && 'md:top-14',
                sidebarClassName
              )}
              aria-label="Sidebar navigation"
            >
              {sidebar}
            </aside>
            
            {/* Mobile Sidebar */}
            {isMobile && (
              <>
                {/* Overlay */}
                {shouldShowOverlay && (
                  <div
                    className="fixed inset-0 z-40 bg-black/50 transition-opacity"
                    onClick={close}
                    aria-hidden="true"
                  />
                )}
                
                {/* Sidebar Panel */}
                <aside
                  ref={sidebarRef}
                  id="mobile-sidebar"
                  className={cn(
                    'fixed inset-y-0 left-0 z-50 w-64 bg-background shadow-lg transform transition-transform md:hidden',
                    isOpen ? 'translate-x-0' : '-translate-x-full',
                    header && 'top-14',
                    sidebarClassName
                  )}
                  aria-label="Mobile sidebar navigation"
                  aria-hidden={!isOpen}
                >
                  {sidebar}
                </aside>
              </>
            )}
          </>
        )}
        
        {/* Main Content */}
        <main
          ref={mainRef}
          id="main-content"
          className={cn(
            'flex-1 min-w-0',
            sidebar && 'md:ml-64',
            mainClassName
          )}
          tabIndex={-1}
        >
          {children}
        </main>
      </div>
      
      {/* Footer */}
      {footer && (
        <footer 
          className={cn(
            'border-t bg-background',
            sidebar && 'md:ml-64',
            footerClassName
          )}
        >
          {footer}
        </footer>
      )}
    </div>
  );
}

// Responsive Grid Component
interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    base?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: number;
  className?: string;
}

export function ResponsiveGrid({ 
  children, 
  columns = { base: 1, md: 2, lg: 3 },
  gap = 4,
  className 
}: ResponsiveGridProps) {
  const gridClasses = [];
  
  if (columns.base) gridClasses.push(`grid-cols-${columns.base}`);
  if (columns.sm) gridClasses.push(`sm:grid-cols-${columns.sm}`);
  if (columns.md) gridClasses.push(`md:grid-cols-${columns.md}`);
  if (columns.lg) gridClasses.push(`lg:grid-cols-${columns.lg}`);
  if (columns.xl) gridClasses.push(`xl:grid-cols-${columns.xl}`);
  if (columns['2xl']) gridClasses.push(`2xl:grid-cols-${columns['2xl']}`);
  
  return (
    <div 
      className={cn(
        'grid',
        `gap-${gap}`,
        ...gridClasses,
        className
      )}
    >
      {children}
    </div>
  );
}

// Responsive Container
interface ResponsiveContainerProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  className?: string;
}

export function ResponsiveContainer({ 
  children, 
  size = 'lg',
  className 
}: ResponsiveContainerProps) {
  const sizeClasses = {
    sm: 'max-w-screen-sm',
    md: 'max-w-screen-md',
    lg: 'max-w-screen-lg',
    xl: 'max-w-screen-xl',
    '2xl': 'max-w-screen-2xl',
    full: 'max-w-full'
  };
  
  return (
    <div className={cn(
      'container mx-auto px-4',
      sizeClasses[size],
      className
    )}>
      {children}
    </div>
  );
}