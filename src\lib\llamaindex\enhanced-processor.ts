import { 
  Document,
  SimpleNodeParser,
} from 'llamaindex';
import { DeepSeekClient } from '@/lib/agent/deepseek';
import { cacheManager } from '@/lib/cache/cache-manager';
import { ChromaVectorStore, VectorDocument, VectorSearchResult } from '@/lib/vector-store/chroma';
import db from '@/lib/db';
import * as schema from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export interface DocumentMetadata {
  title: string;
  sourceUrl: string;
  sourceType: 'confluence' | 'file' | 'web';
  tags?: string[];
  lastIndexed: Date;
  summary?: string;
  concepts?: string[];
}

export interface ProcessedDocument {
  id: string;
  content: string;
  metadata: DocumentMetadata;
  chunks: DocumentChunk[];
}

export interface DocumentChunk {
  id: string;
  content: string;
  startIndex: number;
  endIndex: number;
  metadata: Record<string, any>;
}

export interface SearchResult {
  document: ProcessedDocument;
  chunk: DocumentChunk;
  score: number;
  snippet: string;
}

export interface SearchOptions {
  limit?: number;
  threshold?: number;
  sourceType?: string;
  useChunks?: boolean;
  collections?: string[];
}

/**
 * Enhanced document processor with Chroma vector store integration
 * Provides high-performance vector search and advanced document operations
 */
export class EnhancedDocumentProcessor {
  private deepseek: DeepSeekClient;
  private vectorStore: ChromaVectorStore;
  private nodeParser: SimpleNodeParser;
  
  // Collection names
  private static readonly DOCUMENTS_COLLECTION = 'knowledge_documents';
  private static readonly CHUNKS_COLLECTION = 'knowledge_chunks';

  constructor(deepseek: DeepSeekClient, vectorStore: ChromaVectorStore) {
    this.deepseek = deepseek;
    this.vectorStore = vectorStore;
    
    // Configure node parser for optimal chunking
    this.nodeParser = new SimpleNodeParser({
      chunkSize: 1024,
      chunkOverlap: 100,
    });
  }

  /**
   * Process a document with enhanced vector storage
   */
  async processDocument(
    content: string,
    metadata: DocumentMetadata
  ): Promise<ProcessedDocument> {
    try {
      console.log(`Processing document: ${metadata.title}`);
      
      // Create LlamaIndex document
      const document = new Document({
        text: content,
        metadata: {
          title: metadata.title,
          sourceUrl: metadata.sourceUrl,
          sourceType: metadata.sourceType,
          tags: metadata.tags?.join(',') || '',
        },
      });

      // Parse into nodes/chunks
      const nodes = this.nodeParser.getNodesFromDocuments([document]);
      
      // Generate enhanced metadata
      const [concepts, summary] = await Promise.all([
        this.extractConcepts(content),
        this.summarizeDocument(content)
      ]);

      const enhancedMetadata = {
        ...metadata,
        concepts,
        summary,
      };

      // Process chunks
      const chunks: DocumentChunk[] = [];
      const vectorChunks: VectorDocument[] = [];
      let startIndex = 0;

      for (let i = 0; i < nodes.length; i++) {
        const node = nodes[i];
        const chunkContent = node.getText();
        
        const chunkId = `${metadata.sourceUrl}-chunk-${i}`;
        const chunk: DocumentChunk = {
          id: chunkId,
          content: chunkContent,
          startIndex,
          endIndex: startIndex + chunkContent.length,
          metadata: {
            nodeId: node.id_,
            chunkIndex: i,
            totalChunks: nodes.length,
            documentId: metadata.sourceUrl,
            documentTitle: metadata.title,
            sourceType: metadata.sourceType,
            tags: metadata.tags || [],
          },
        };

        chunks.push(chunk);

        // Prepare vector document for chunk
        vectorChunks.push({
          id: chunkId,
          content: chunkContent,
          metadata: {
            ...chunk.metadata,
            type: 'chunk',
          },
        });

        startIndex += chunkContent.length;
      }

      const processedDoc: ProcessedDocument = {
        id: metadata.sourceUrl,
        content,
        metadata: enhancedMetadata,
        chunks,
      };

      // Store in vector database
      await Promise.all([
        // Store document-level vector
        this.vectorStore.addDocuments(EnhancedDocumentProcessor.DOCUMENTS_COLLECTION, [{
          id: metadata.sourceUrl,
          content: content.length > 8000 ? content.substring(0, 8000) : content,
          metadata: {
            ...enhancedMetadata,
            type: 'document',
            chunkCount: chunks.length,
          },
        }]),
        
        // Store chunk-level vectors
        this.vectorStore.addDocuments(EnhancedDocumentProcessor.CHUNKS_COLLECTION, vectorChunks)
      ]);

      // Store in relational database for metadata and backup
      await this.storeProcessedDocument(processedDoc);

      console.log(`Successfully processed document with ${chunks.length} chunks`);
      return processedDoc;
    } catch (error) {
      console.error('Error processing document:', error);
      throw new Error(`Failed to process document: ${error}`);
    }
  }

  /**
   * Enhanced search with multiple strategies
   */
  async searchDocuments(
    query: string,
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    const { 
      limit = 10, 
      threshold = 0.7, 
      useChunks = true,
      sourceType 
    } = options;

    try {
      // Check cache first
      const cachedResults = cacheManager.getSearchResults(query, options);
      if (cachedResults) {
        console.log('📦 Using cached search results');
        return cachedResults;
      }

      console.log(`🔍 Searching for: "${query}" (useChunks: ${useChunks})`);

      const results: SearchResult[] = [];

      if (useChunks) {
        // Search chunks for precise results
        const chunkResults = await this.vectorStore.search(
          EnhancedDocumentProcessor.CHUNKS_COLLECTION,
          query,
          { 
            limit: limit * 2, // Get more chunks to filter by document
            threshold,
            filter: sourceType ? { sourceType } : undefined
          }
        );

        // Group chunks by document and process
        const documentGroups = new Map<string, VectorSearchResult[]>();
        
        for (const result of chunkResults) {
          const docId = result.metadata.documentId;
          if (!documentGroups.has(docId)) {
            documentGroups.set(docId, []);
          }
          documentGroups.get(docId)!.push(result);
        }

        // Convert to SearchResult format
        for (const [docId, chunks] of documentGroups) {
          // Get document info from database
          const dbDoc = await db.select()
            .from(schema.knowledgeBase)
            .where(eq(schema.knowledgeBase.id, docId))
            .limit(1);

          if (dbDoc.length === 0) continue;

          const doc = dbDoc[0];
          const bestChunk = chunks[0]; // Highest scoring chunk

          results.push({
            document: {
              id: doc.id,
              content: doc.content || '',
              metadata: {
                title: doc.title,
                sourceUrl: doc.sourceUrl,
                sourceType: doc.sourceType as any,
                tags: doc.tags ? JSON.parse(doc.tags) : [],
                lastIndexed: doc.lastIndexed,
                summary: doc.summary || undefined,
                concepts: doc.concepts ? JSON.parse(doc.concepts) : undefined,
              },
              chunks: chunks.map(c => ({
                id: c.id,
                content: c.content,
                startIndex: c.metadata.startIndex || 0,
                endIndex: c.metadata.endIndex || c.content.length,
                metadata: c.metadata,
              })),
            },
            chunk: {
              id: bestChunk.id,
              content: bestChunk.content,
              startIndex: bestChunk.metadata.startIndex || 0,
              endIndex: bestChunk.metadata.endIndex || bestChunk.content.length,
              metadata: bestChunk.metadata,
            },
            score: bestChunk.score,
            snippet: this.generateSnippet(bestChunk.content, query),
          });
        }
      } else {
        // Search documents for broader results
        const docResults = await this.vectorStore.search(
          EnhancedDocumentProcessor.DOCUMENTS_COLLECTION,
          query,
          { 
            limit,
            threshold,
            filter: sourceType ? { sourceType } : undefined
          }
        );

        for (const result of docResults) {
          // Get full document data from database
          const dbDoc = await db.select()
            .from(schema.knowledgeBase)
            .where(eq(schema.knowledgeBase.id, result.id))
            .limit(1);

          if (dbDoc.length === 0) continue;

          const doc = dbDoc[0];

          results.push({
            document: {
              id: doc.id,
              content: doc.content || '',
              metadata: {
                title: doc.title,
                sourceUrl: doc.sourceUrl,
                sourceType: doc.sourceType as any,
                tags: doc.tags ? JSON.parse(doc.tags) : [],
                lastIndexed: doc.lastIndexed,
                summary: doc.summary || undefined,
                concepts: doc.concepts ? JSON.parse(doc.concepts) : undefined,
              },
              chunks: doc.chunks ? JSON.parse(doc.chunks) : [],
            },
            chunk: {
              id: `${doc.id}-full`,
              content: doc.content || '',
              startIndex: 0,
              endIndex: doc.content?.length || 0,
              metadata: { type: 'full_document' },
            },
            score: result.score,
            snippet: this.generateSnippet(doc.content || '', query),
          });
        }
      }

      // Sort by score and limit results
      const finalResults = results
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);

      // Cache the results for future use
      cacheManager.setSearchResults(query, options, finalResults);

      console.log(`Found ${finalResults.length} search results`);
      return finalResults;

    } catch (error) {
      console.error('Error searching documents:', error);
      
      // Fallback to database search if vector search fails
      console.log('Falling back to database search...');
      return this.fallbackSearch(query, options);
    }
  }

  /**
   * Semantic similarity search for related documents
   */
  async findSimilarDocuments(
    documentId: string,
    limit: number = 5
  ): Promise<SearchResult[]> {
    try {
      // Get the document from vector store
      const doc = await this.vectorStore.getDocument(
        EnhancedDocumentProcessor.DOCUMENTS_COLLECTION,
        documentId
      );

      if (!doc) {
        throw new Error('Document not found in vector store');
      }

      // Use document content for similarity search
      return this.searchDocuments(doc.content.substring(0, 500), {
        limit: limit + 1, // +1 to exclude the original document
        useChunks: false,
      }).then(results => 
        results.filter(r => r.document.id !== documentId)
      );
    } catch (error) {
      console.error('Error finding similar documents:', error);
      return [];
    }
  }

  /**
   * Concept-based search using extracted keywords
   */
  async searchByConcepts(
    concepts: string[],
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    try {
      // Combine concepts into a search query
      const conceptQuery = concepts.join(' ');
      
      // Search with broader threshold for concept matching
      return this.searchDocuments(conceptQuery, {
        ...options,
        threshold: 0.6, // Lower threshold for concept matching
      });
    } catch (error) {
      console.error('Error searching by concepts:', error);
      return [];
    }
  }

  /**
   * Reindex all documents in vector store
   */
  async reindexDocuments(): Promise<{ processed: number; errors: number }> {
    try {
      console.log('Starting document reindexing...');
      
      // Get all documents from database
      const documents = await db.select().from(schema.knowledgeBase);
      
      let processed = 0;
      let errors = 0;

      for (const doc of documents) {
        try {
          if (!doc.content) continue;

          await this.processDocument(doc.content, {
            title: doc.title,
            sourceUrl: doc.sourceUrl,
            sourceType: doc.sourceType as any,
            tags: doc.tags ? JSON.parse(doc.tags) : [],
            lastIndexed: new Date(),
          });

          processed++;
          
          if (processed % 10 === 0) {
            console.log(`Reindexed ${processed}/${documents.length} documents`);
          }
        } catch (error) {
          console.error(`Error reindexing document ${doc.id}:`, error);
          errors++;
        }
      }

      console.log(`Reindexing completed: ${processed} processed, ${errors} errors`);
      return { processed, errors };
    } catch (error) {
      console.error('Error during reindexing:', error);
      throw error;
    }
  }

  /**
   * Get vector store statistics
   */
  async getStatistics(): Promise<{
    documents: number;
    chunks: number;
    collections: string[];
    isHealthy: boolean;
  }> {
    try {
      const [docStats, chunkStats, collections, isHealthy] = await Promise.all([
        this.vectorStore.getCollectionStats(EnhancedDocumentProcessor.DOCUMENTS_COLLECTION),
        this.vectorStore.getCollectionStats(EnhancedDocumentProcessor.CHUNKS_COLLECTION),
        this.vectorStore.listCollections(),
        this.vectorStore.healthCheck(),
      ]);

      return {
        documents: docStats?.count || 0,
        chunks: chunkStats?.count || 0,
        collections,
        isHealthy,
      };
    } catch (error) {
      console.error('Error getting statistics:', error);
      return {
        documents: 0,
        chunks: 0,
        collections: [],
        isHealthy: false,
      };
    }
  }

  /**
   * Extract key concepts and entities from document content
   */
  private async extractConcepts(content: string): Promise<string[]> {
    try {
      const prompt = `Extract the key concepts, topics, and important terms from the following text. 
Return them as a comma-separated list of keywords and phrases (max 20 items):

${content.substring(0, 2000)}`;

      const response = await this.deepseek.generateCompletion([
        { role: 'user', content: prompt }
      ]);

      const concepts = response.content
        .split(',')
        .map(c => c.trim())
        .filter(c => c.length > 2 && c.length < 50)
        .slice(0, 20); // Limit to 20 concepts

      return concepts;
    } catch (error) {
      console.error('Error extracting concepts:', error);
      return [];
    }
  }

  /**
   * Generate a summary of document content
   */
  private async summarizeDocument(content: string, maxLength: number = 300): Promise<string> {
    try {
      const prompt = `Summarize the following content in ${maxLength} characters or less. 
Focus on the main points and key information:

${content.substring(0, 4000)}`;

      const response = await this.deepseek.generateCompletion([
        { role: 'user', content: prompt }
      ]);

      return response.content.substring(0, maxLength);
    } catch (error) {
      console.error('Error summarizing document:', error);
      return content.substring(0, maxLength) + '...';
    }
  }

  /**
   * Fallback search using database when vector search fails
   */
  private async fallbackSearch(query: string, options: SearchOptions): Promise<SearchResult[]> {
    try {
      console.log('Using fallback database search');
      
      const queryTerms = query.toLowerCase().split(/\\s+/);
      let dbQuery = db.select().from(schema.knowledgeBase);
      
      if (options.sourceType) {
        dbQuery = dbQuery.where(eq(schema.knowledgeBase.sourceType, options.sourceType));
      }

      const documents = await dbQuery;
      const results: SearchResult[] = [];

      for (const doc of documents) {
        if (!doc.content) continue;

        // Simple text matching for fallback
        const contentLower = doc.content.toLowerCase();
        const titleLower = doc.title.toLowerCase();
        
        let matches = 0;
        for (const term of queryTerms) {
          if (contentLower.includes(term) || titleLower.includes(term)) {
            matches++;
          }
        }

        if (matches > 0) {
          const score = matches / queryTerms.length;
          
          results.push({
            document: {
              id: doc.id,
              content: doc.content,
              metadata: {
                title: doc.title,
                sourceUrl: doc.sourceUrl,
                sourceType: doc.sourceType as any,
                tags: doc.tags ? JSON.parse(doc.tags) : [],
                lastIndexed: doc.lastIndexed,
                summary: doc.summary || undefined,
                concepts: doc.concepts ? JSON.parse(doc.concepts) : undefined,
              },
              chunks: doc.chunks ? JSON.parse(doc.chunks) : [],
            },
            chunk: {
              id: `${doc.id}-fallback`,
              content: doc.content,
              startIndex: 0,
              endIndex: doc.content.length,
              metadata: { type: 'fallback' },
            },
            score,
            snippet: this.generateSnippet(doc.content, query),
          });
        }
      }

      return results
        .sort((a, b) => b.score - a.score)
        .slice(0, options.limit || 10);
    } catch (error) {
      console.error('Fallback search failed:', error);
      return [];
    }
  }

  /**
   * Store processed document in database
   */
  private async storeProcessedDocument(doc: ProcessedDocument): Promise<void> {
    try {
      await db.insert(schema.knowledgeBase).values({
        id: doc.id,
        title: doc.metadata.title,
        content: doc.content,
        summary: doc.metadata.summary || null,
        sourceUrl: doc.metadata.sourceUrl,
        sourceType: doc.metadata.sourceType,
        tags: JSON.stringify(doc.metadata.tags || []),
        concepts: JSON.stringify(doc.metadata.concepts || []),
        chunks: JSON.stringify(doc.chunks),
        lastIndexed: doc.metadata.lastIndexed,
      }).onConflictDoUpdate({
        target: schema.knowledgeBase.id,
        set: {
          title: doc.metadata.title,
          content: doc.content,
          summary: doc.metadata.summary || null,
          tags: JSON.stringify(doc.metadata.tags || []),
          concepts: JSON.stringify(doc.metadata.concepts || []),
          chunks: JSON.stringify(doc.chunks),
          lastIndexed: doc.metadata.lastIndexed,
        },
      });
    } catch (error) {
      console.error('Error storing document:', error);
      throw error;
    }
  }

  /**
   * Generate a context snippet around query matches
   */
  private generateSnippet(content: string, query: string, contextLength: number = 200): string {
    const queryWords = query.toLowerCase().split(/\\s+/);
    const contentLower = content.toLowerCase();
    
    // Find the best match position
    let bestMatch = -1;
    let maxMatches = 0;
    
    for (let i = 0; i < content.length - contextLength; i++) {
      const snippet = contentLower.substring(i, i + contextLength);
      const matches = queryWords.filter(word => snippet.includes(word)).length;
      
      if (matches > maxMatches) {
        maxMatches = matches;
        bestMatch = i;
      }
    }
    
    if (bestMatch === -1) {
      return content.substring(0, contextLength) + '...';
    }
    
    const start = Math.max(0, bestMatch);
    const end = Math.min(content.length, bestMatch + contextLength);
    
    return (start > 0 ? '...' : '') + 
           content.substring(start, end) + 
           (end < content.length ? '...' : '');
  }
}

// Export factory function
export function createEnhancedDocumentProcessor(
  deepseek: DeepSeekClient,
  vectorStore: ChromaVectorStore
): EnhancedDocumentProcessor {
  return new EnhancedDocumentProcessor(deepseek, vectorStore);
}