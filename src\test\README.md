# Testing Framework Documentation

This document describes the comprehensive testing framework implemented for the AI Agent system.

## Overview

The testing framework provides:
- **Unit Tests**: Component and function-level testing
- **Integration Tests**: Full system workflow testing  
- **Performance Tests**: Load and response time testing
- **Accessibility Tests**: WCAG compliance verification
- **API Tests**: Backend endpoint testing

## Test Structure

```
src/test/
├── README.md              # This documentation
├── setup.ts               # Jest configuration and global setup
├── utils.tsx              # Testing utilities and mock data
├── performance.test.ts    # Performance benchmarks
└── integration/           # Integration test suites
    └── mission-lifecycle.test.ts

src/components/__tests__/   # Component unit tests
src/lib/agent/__tests__/   # Agent logic tests  
src/app/api/__tests__/     # API route tests
```

## Running Tests

### All Tests
```bash
npm run test:all           # Run all test suites
npm run test               # Run tests once  
npm run test:watch         # Run tests in watch mode
npm run test:coverage      # Generate coverage report
```

### Specific Test Types
```bash
npm run test:unit          # Component and unit tests
npm run test:integration   # Integration tests
npm run test:performance   # Performance benchmarks
npm run test:e2e          # End-to-end tests (Playwright)
npm run test:ci           # CI/CD optimized run
```

## Test Utilities

### Core Testing Utils (`src/test/utils.tsx`)

**Mock Data**
- `mockMission`: Sample mission object
- `mockPlan`: Sample plan object  
- `mockTask`: Sample task object
- `mockUser`: Sample user object

**Render Utilities**
```typescript
import { render, screen, user } from '@/test/utils';

// Enhanced render with providers
const { container } = render(<MyComponent />);

// User interactions
await user.click(screen.getByRole('button'));
await user.type(screen.getByLabelText('Search'), 'query');
```

**Accessibility Testing**
```typescript
import { TestUtils } from '@/test/utils';

// Check accessibility compliance
await TestUtils.checkAccessibility(element);

// Test keyboard navigation
await TestUtils.navigateWithKeyboard(['Tab', 'Enter']);
```

**Responsive Testing**
```typescript
// Test different breakpoints
await TestUtils.testResponsive({
  mobile: 375,
  tablet: 768,
  desktop: 1024
});
```

### Agent Testing (`AgentTestUtils`)

**Mock Services**
```typescript
import { AgentTestUtils } from '@/test/utils';

// Mock DeepSeek client
const mockClient = AgentTestUtils.createMockDeepSeekClient();

// Mock tool registry
const mockRegistry = AgentTestUtils.createMockToolRegistry();

// Mock lifecycle manager
const mockLifecycle = AgentTestUtils.createMockLifecycleManager();
```

### Database Testing (`DbTestUtils`)

**Mock Database**
```typescript
import { DbTestUtils } from '@/test/utils';

const mockDb = DbTestUtils.createMockDb();
await DbTestUtils.resetDatabase(mockDb);
```

### Performance Testing (`PerformanceTestUtils`)

**Render Performance**
```typescript
import { PerformanceTestUtils } from '@/test/utils';

// Measure component render time
const renderTime = await PerformanceTestUtils.measureRenderTime(<Component />);
expect(renderTime).toBeLessThan(16); // 60fps budget

// Test memory leaks
await PerformanceTestUtils.testMemoryLeaks(<Component />, 100);
```

## Writing Tests

### Component Tests

```typescript
import { render, screen, user } from '@/test/utils';
import { MyComponent } from '../MyComponent';

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  it('handles user interactions', async () => {
    const handleClick = jest.fn();
    render(<MyComponent onClick={handleClick} />);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('is accessible', async () => {
    render(<MyComponent />);
    const button = screen.getByRole('button');
    await TestUtils.checkAccessibility(button);
  });
});
```

### API Tests

```typescript
import { NextRequest } from 'next/server';
import { GET, POST } from '../route';
import { TestUtils, DbTestUtils } from '@/test/utils';

describe('/api/missions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns missions list', async () => {
    const request = new NextRequest('http://localhost/api/missions');
    const response = await GET(request);
    
    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.missions).toBeDefined();
  });
});
```

### Integration Tests

```typescript
import { TestUtils, AgentTestUtils, MockWebSocket } from '@/test/utils';

describe('Mission Lifecycle Integration', () => {
  let mockWs: MockWebSocket;
  
  beforeEach(() => {
    mockWs = new MockWebSocket('ws://localhost:3001');
  });

  it('completes full mission workflow', async () => {
    // Test complete workflow from creation to completion
    const mission = await createMission(missionData);
    const plan = await generatePlan(mission.id);
    await executeMission(mission.id);
    
    // Verify WebSocket updates
    mockWs.simulateMessage({
      type: 'mission_completed',
      missionId: mission.id
    });
    
    const finalState = await getMissionStatus(mission.id);
    expect(finalState.status).toBe('completed');
  });
});
```

## Test Coverage

The framework tracks coverage for:
- **Statements**: 70% minimum
- **Branches**: 70% minimum  
- **Functions**: 70% minimum
- **Lines**: 70% minimum

### Coverage Reports

```bash
npm run test:coverage
```

Reports are generated in:
- `coverage/lcov-report/index.html` - HTML report
- `coverage/lcov.info` - LCOV format
- `coverage/coverage-final.json` - JSON format

## Accessibility Testing

### WCAG Compliance

The framework tests for:
- **Keyboard Navigation**: Tab order, focus management
- **Screen Reader Support**: ARIA labels, roles, live regions
- **Color Contrast**: Minimum contrast ratios
- **Motion Preferences**: Respect for `prefers-reduced-motion`

### Custom Matchers

```typescript
// Custom Jest matcher for accessibility
expect(element).toBeAccessible();
```

## Performance Benchmarks

### Render Performance
- Components must render under 16ms (60fps)
- Large lists must virtualize effectively
- Animations must maintain 60fps

### Memory Management
- No memory leaks after unmounting
- Efficient cleanup of event listeners
- Proper disposal of WebSocket connections

### Network Performance
- API responses under 200ms for local calls
- Proper request debouncing (300ms)
- Timeout handling under 5 seconds

## Mocking Strategy

### External Services
- **WebSocket**: `MockWebSocket` class
- **Database**: Mock Drizzle ORM methods
- **APIs**: `fetch` and `axios` mocks
- **File System**: Mock file operations

### Browser APIs
- **localStorage/sessionStorage**: Mocked storage
- **ResizeObserver/IntersectionObserver**: Mocked observers
- **matchMedia**: Responsive behavior mocks

## CI/CD Integration

### GitHub Actions

```yaml
- name: Run Tests
  run: npm run test:ci

- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### Test Configuration

Tests are optimized for CI with:
- No watch mode
- Coverage reporting enabled
- Fail fast on errors
- Parallel execution

## Best Practices

### Test Organization
1. **Arrange-Act-Assert**: Clear test structure
2. **Descriptive Names**: Self-documenting test names
3. **Single Responsibility**: One concept per test
4. **Setup/Teardown**: Proper cleanup between tests

### Performance
1. **Mock Heavy Operations**: Database, network calls
2. **Shallow Rendering**: When deep rendering not needed
3. **Selective Testing**: Focus on critical paths
4. **Parallel Execution**: Utilize available cores

### Accessibility
1. **Real User Scenarios**: Test how users actually interact
2. **Assistive Technology**: Screen reader announcements
3. **Keyboard Only**: Navigate without mouse
4. **High Contrast**: Test in high contrast mode

## Troubleshooting

### Common Issues

**Tests timing out**
```typescript
// Increase timeout for slow operations
jest.setTimeout(10000);
```

**Memory leaks**
```typescript
// Always cleanup in afterEach
afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});
```

**WebSocket tests failing**
```typescript
// Wait for WebSocket connection
await waitFor(() => {
  expect(mockWs.readyState).toBe(WebSocket.OPEN);
});
```

### Debug Mode

```bash
# Run tests with debug output
DEBUG=true npm run test

# Run specific test file
npm test -- MyComponent.test.tsx

# Run with verbose output
npm test -- --verbose
```

## Contributing

When adding new features:

1. **Write tests first** (TDD approach)
2. **Cover edge cases** and error conditions
3. **Test accessibility** for new UI components
4. **Update this documentation** if adding new utilities
5. **Maintain coverage** above minimum thresholds

### Test Review Checklist

- [ ] Tests are deterministic (no flaky tests)
- [ ] Edge cases are covered
- [ ] Error conditions are tested
- [ ] Accessibility is verified
- [ ] Performance is within budget
- [ ] Mocks are realistic
- [ ] Tests are maintainable

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [Testing Library](https://testing-library.com/docs/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Playwright Testing](https://playwright.dev/)