"use client"

import { useRef, useEffect } from "react"
import { MoreVertical, SquarePen, Trash2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { Mission, RealtimeStatus } from "./types"
import { MissionStatusIndicator } from "./MissionStatusIndicator"

interface MissionItemProps {
  mission: Mission;
  isSelected: boolean;
  isEditing: boolean;
  editingValue: string;
  realtimeStatus?: RealtimeStatus | null;
  onSelect: (missionId: number) => void;
  onStartEdit: (mission: Mission) => void;
  onEditValueChange: (value: string) => void;
  onSaveEdit: () => void;
  onCancelEdit: () => void;
  onDelete: (mission: Mission) => void;
}

export function MissionItem({
  mission,
  isSelected,
  isEditing,
  editingValue,
  realtimeStatus,
  onSelect,
  onStartEdit,
  onEditValueChange,
  onSaveEdit,
  onCancelEdit,
  onDelete
}: MissionItemProps) {
  const editInputRef = useRef<HTMLInputElement>(null);

  // Auto-focus on mission name input when editing starts
  useEffect(() => {
    if (isEditing && editInputRef.current) {
      editInputRef.current.focus();
      editInputRef.current.select();
    }
  }, [isEditing]);

  const handleEditKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      onSaveEdit();
    } else if (e.key === "Escape") {
      onCancelEdit();
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    // Don't trigger selection if we're editing this mission
    if (isEditing) return;
    
    // Don't trigger selection if clicking on dropdown trigger or input elements
    const target = e.target as HTMLElement;
    if (target.closest('button[role="combobox"]') || 
        target.closest('[role="menuitem"]') || 
        target.tagName === 'INPUT') {
      return;
    }
    
    onSelect(mission.id);
  };

  const status = realtimeStatus?.status || mission.status;
  const isActive = realtimeStatus?.isActive || false;
  const progress = realtimeStatus?.progress?.percentage;

  return (
    <div
      className={cn(
        "p-3 rounded-lg border transition-colors group",
        isEditing 
          ? "bg-muted/50 border-primary/50 ring-2 ring-primary/20"
          : cn(
              "cursor-pointer hover:bg-accent/50 hover:border-accent",
              isSelected
                ? "bg-primary/10 border-primary/30"
                : "bg-background border-border/50"
            )
      )}
      onClick={handleClick}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0 mr-2">
          {isEditing ? (
            <div>
              <input
                ref={editInputRef}
                type="text"
                value={editingValue}
                onChange={(e) => onEditValueChange(e.target.value)}
                onKeyDown={handleEditKeyDown}
                onBlur={onSaveEdit}
                className={cn(
                  "w-full text-sm font-medium bg-background border rounded px-2 py-1 focus:outline-none focus:ring-2 transition-colors",
                  editingValue.trim().length === 0 
                    ? "border-destructive/50 focus:border-destructive focus:ring-destructive/20"
                    : editingValue.length > 100
                    ? "border-yellow-500/50 focus:border-yellow-500 focus:ring-yellow-500/20"
                    : "border-border focus:border-primary focus:ring-primary/20"
                )}
                autoFocus
                maxLength={150} // Allow typing beyond 100 to show validation
                placeholder="Enter mission name..."
              />
              {editingValue.length > 100 && (
                <p className="text-xs text-yellow-600 mt-1">
                  {editingValue.length}/100 characters (max length exceeded)
                </p>
              )}
            </div>
          ) : (
            <h4 
              className="text-sm font-medium truncate group-hover:text-primary transition-colors"
              title="Click mission to view details"
            >
              {mission.title}
            </h4>
          )}
          <p className="text-xs text-muted-foreground mt-1">
            {mission.timestamp.toLocaleDateString([], { 
              month: 'numeric', 
              day: 'numeric', 
              year: '2-digit' 
            })}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <MissionStatusIndicator 
            status={status}
            isActive={isActive}
            progress={progress}
          />
          {!isEditing && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-muted hover:text-muted-foreground"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onStartEdit(mission);
                  }}
                >
                  <SquarePen className="h-4 w-4 mr-2" />
                  Rename
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(mission);
                  }}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </div>
  );
}