import React from 'react';
import { render, screen, user, TestUtils } from '@/test/utils';
import { Button } from '@/components/ui/enhanced-button';

describe('EnhancedButton', () => {
  it('renders with default props', () => {
    render(<Button>Click me</Button>);
    
    const button = screen.getByRole('button', { name: /click me/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('bg-primary');
  });

  it('handles different variants', () => {
    const { rerender } = render(<Button variant="destructive">Delete</Button>);
    
    let button = screen.getByRole('button');
    expect(button).toHaveClass('bg-destructive');
    
    rerender(<Button variant="outline">Cancel</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('border-input');
  });

  it('handles different sizes', () => {
    const { rerender } = render(<Button size="sm">Small</Button>);
    
    let button = screen.getByRole('button');
    expect(button).toHaveClass('h-9');
    
    rerender(<Button size="lg">Large</Button>);
    button = screen.getByRole('button');
    expect(button).toHaveClass('h-11');
  });

  it('shows loading state correctly', () => {
    render(
      <Button loading loadingText="Processing...">
        Submit
      </Button>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-busy', 'true');
    expect(button).toBeDisabled();
    expect(screen.getByText('Processing...')).toBeInTheDocument();
    
    // Should show loading spinner
    const spinner = button.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    const button = screen.getByRole('button');
    await user.click(button);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('prevents clicks when disabled', async () => {
    const handleClick = jest.fn();
    render(
      <Button onClick={handleClick} disabled>
        Disabled
      </Button>
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    
    await user.click(button);
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('prevents clicks when loading', async () => {
    const handleClick = jest.fn();
    render(
      <Button onClick={handleClick} loading>
        Loading
      </Button>
    );
    
    const button = screen.getByRole('button');
    await user.click(button);
    
    expect(handleClick).not.toHaveBeenCalled();
  });

  it('announces clicks to screen readers', async () => {
    const announceToScreenReader = jest.fn();
    jest.doMock('@/lib/ui/accessibility', () => ({
      announceToScreenReader,
      useReducedMotion: () => false
    }));

    render(
      <Button announceOnClick="Button was clicked">
        Announce me
      </Button>
    );
    
    const button = screen.getByRole('button');
    await user.click(button);
    
    // Note: In real tests, you'd need to properly mock the accessibility module
  });

  it('is accessible', async () => {
    render(<Button>Accessible button</Button>);
    
    const button = screen.getByRole('button');
    await TestUtils.checkAccessibility(button);
  });

  it('supports keyboard navigation', async () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Keyboard accessible</Button>);
    
    const button = screen.getByRole('button');
    button.focus();
    
    expect(document.activeElement).toBe(button);
    
    await user.keyboard('[Enter]');
    expect(handleClick).toHaveBeenCalledTimes(1);
    
    await user.keyboard('[Space]');
    expect(handleClick).toHaveBeenCalledTimes(2);
  });

  it('shows tooltip when provided', () => {
    render(<Button tooltipText="This is a tooltip">Hover me</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('title', 'This is a tooltip');
  });

  it('respects reduced motion preferences', () => {
    // Mock useReducedMotion to return true
    jest.doMock('@/lib/ui/accessibility', () => ({
      useReducedMotion: () => true,
      announceToScreenReader: jest.fn()
    }));

    render(<Button loading>Loading</Button>);
    
    const button = screen.getByRole('button');
    const spinner = button.querySelector('.animate-spin');
    
    // Should not have animation classes when reduced motion is preferred
    expect(spinner).toHaveClass('animate-none');
  });

  it('works as child component', () => {
    render(
      <Button asChild>
        <a href="/test">Link button</a>
      </Button>
    );
    
    const link = screen.getByRole('link');
    expect(link).toHaveAttribute('href', '/test');
    expect(link).toHaveClass('bg-primary'); // Should have button styles
  });

  it('handles icon buttons correctly', () => {
    render(
      <Button size="icon" aria-label="Close">
        <span>×</span>
      </Button>
    );
    
    const button = screen.getByRole('button', { name: /close/i });
    expect(button).toHaveClass('h-10', 'w-10');
  });
});