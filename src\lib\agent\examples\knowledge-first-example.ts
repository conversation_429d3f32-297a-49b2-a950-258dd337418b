/**
 * Knowledge-Base-First RAG/ReAct Example Implementation
 * 
 * This file demonstrates the correct implementation of the knowledge-base-first principle
 * for a typical user query: "How many incidents for payment services last week?"
 */

import { ServiceManagementAgentImpl } from '../core';
import { createMissionLogger } from '../logger';
import { KnowledgeBaseAuditSystem } from '../audit/knowledge-audit';

/**
 * Example: Knowledge-Base-First Mission Execution
 * 
 * User Query: "How many incidents for payment services last week?"
 * 
 * ENFORCED FLOW:
 * 1. Query Phase: Search vector database for payment service incidents
 * 2. Retrieval Phase: Extract incident data from knowledge base
 * 3. Reasoning Phase: Analyze retrieved data using LLM
 * 4. Response Phase: Generate response based solely on retrieved data
 */
export async function demonstrateKnowledgeFirstFlow() {
  console.log('🚀 Starting Knowledge-Base-First RAG/ReAct Demonstration');
  console.log('=' * 60);

  // Initialize agent with knowledge-first enforcement
  const agent = new ServiceManagementAgentImpl({
    deepseekApiKey: process.env.DEEPSEEK_API_KEY || '',
    deepseekBaseUrl: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com',
  });

  const auditSystem = KnowledgeBaseAuditSystem.getInstance();
  
  // Example user query
  const userQuery = "How many incidents for payment services last week?";
  console.log(`📝 User Query: "${userQuery}"`);

  try {
    // Step 1: Create Mission (this will automatically enforce knowledge-first planning)
    console.log('\n🎯 Step 1: Creating Knowledge-First Mission Plan');
    
    const mission = {
      id: 'demo-mission-' + Date.now(),
      title: 'Payment Service Incident Analysis',
      description: userQuery,
      priority: 'medium' as const,
      status: 'pending' as const,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const context = {
      missionId: mission.id,
      logger: createMissionLogger(mission.id)
    };

    // The planning engine will automatically create a knowledge-first plan
    const planningResult = await agent.planning.createPlan(mission, context);
    
    console.log('✅ Plan Created with Knowledge-First Enforcement:');
    planningResult.plan.tasks.forEach((task, index) => {
      console.log(`   ${index + 1}. ${task.title} (${task.toolName})`);
    });

    // Step 2: Execute the Plan (knowledge-base-first validation enforced)
    console.log('\n🔄 Step 2: Executing Knowledge-First Plan');
    
    const executionResult = await agent.autonomous.executePlan(planningResult.plan, context);
    
    console.log('✅ Plan Execution Results:');
    console.log(`   Success: ${executionResult.success}`);
    console.log(`   Completed Tasks: ${executionResult.completedTasks?.length || 0}`);
    console.log(`   Failed Tasks: ${executionResult.failedTasks?.length || 0}`);

    // Step 3: Generate Compliance Report
    console.log('\n📊 Step 3: Knowledge-Base-First Compliance Report');
    
    const complianceReport = await auditSystem.generateComplianceReport(mission.id);
    
    console.log('✅ Compliance Analysis:');
    console.log(`   Compliant: ${complianceReport.isCompliant ? '✅ YES' : '❌ NO'}`);
    console.log(`   Knowledge Queries: ${complianceReport.summary.totalKnowledgeQueries}`);
    console.log(`   Query Success Rate: ${complianceReport.summary.querySuccessRate.toFixed(1)}%`);
    console.log(`   Validation Failures: ${complianceReport.summary.validationFailures}`);
    console.log(`   Reasoning Violations: ${complianceReport.summary.reasoningViolations}`);

    // Step 4: Show Audit Trail
    console.log('\n📋 Step 4: Knowledge Base Audit Trail');
    
    const auditTrail = auditSystem.getAuditTrail(mission.id);
    auditTrail.forEach((entry, index) => {
      console.log(`   ${index + 1}. [${entry.type}] ${entry.toolName}`);
      if (entry.query) {
        console.log(`      Query: "${entry.query}"`);
        console.log(`      Results: ${entry.resultCount} (hasData: ${entry.hasData})`);
      }
    });

    // Step 5: Demonstrate Violation Detection
    console.log('\n🚨 Step 5: Violation Detection Example');
    
    try {
      // This should fail due to knowledge-first validation
      await demonstrateViolation(agent, context);
    } catch (error) {
      console.log('✅ Violation correctly detected and blocked:');
      console.log(`   Error: ${(error as Error).message}`);
    }

    console.log('\n🎉 Knowledge-Base-First Demonstration Complete!');
    console.log('=' * 60);

    return {
      mission,
      planningResult,
      executionResult,
      complianceReport,
      auditTrail
    };

  } catch (error) {
    console.error('❌ Demonstration failed:', error);
    throw error;
  }
}

/**
 * Demonstrate what happens when knowledge-first principle is violated
 */
async function demonstrateViolation(agent: ServiceManagementAgentImpl, context: any) {
  console.log('   Attempting to execute reasoning without knowledge retrieval...');
  
  // Create a task that tries to reason without knowledge
  const violatingTask = {
    id: 'violating-task',
    planId: 'demo-plan',
    title: 'Generate Response Without Knowledge',
    description: 'Try to answer user question without retrieving knowledge',
    status: 'pending' as const,
    priority: 1,
    toolName: 'knowledge_based_reasoning', // This should fail validation
    toolParams: {
      query: 'How many incidents?',
      knowledgeContext: [] // Empty knowledge context - should trigger violation
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // This should throw an error due to knowledge-first validation
  await agent.autonomous.executeTask(violatingTask, context);
}

/**
 * Example of correct knowledge-first task sequence
 */
export function getKnowledgeFirstTaskExample() {
  return {
    userQuery: "How many incidents for payment services last week?",
    correctTaskSequence: [
      {
        step: 1,
        title: "Retrieve Relevant Information",
        toolName: "knowledge_base_query",
        description: "Query the knowledge base for payment service incidents from last week",
        parameters: {
          query: "payment services incidents last week",
          limit: 20,
          threshold: 0.6,
          timeRange: "last_week",
          sourceType: "incident"
        },
        reasoning: "MUST start with knowledge retrieval - no reasoning without data"
      },
      {
        step: 2,
        title: "Query Incident Data",
        toolName: "incident_query",
        description: "Get specific incident data for payment services",
        parameters: {
          service: "payment services",
          timeRange: "last_week",
          limit: 50
        },
        reasoning: "Specialized incident query for more detailed data"
      },
      {
        step: 3,
        title: "Analyze Retrieved Data",
        toolName: "knowledge_based_reasoning",
        description: "Analyze incident data to count and categorize incidents",
        parameters: {
          query: "How many incidents for payment services last week?",
          knowledgeContext: "{{results_from_step_1_and_2}}",
          analysisType: "incident_analysis"
        },
        reasoning: "Can only reason AFTER knowledge retrieval"
      },
      {
        step: 4,
        title: "Generate Final Response",
        toolName: "response_generation",
        description: "Generate user response based on analysis",
        parameters: {
          originalQuery: "How many incidents for payment services last week?",
          reasoningResult: "{{results_from_step_3}}",
          responseFormat: "detailed"
        },
        reasoning: "Final response based solely on retrieved and analyzed data"
      }
    ],
    expectedResponse: {
      withData: "Based on the knowledge base analysis, there were 12 incidents for payment services last week. This includes 2 critical incidents, 4 high severity, 5 medium severity, and 1 low severity incident. The critical incidents were related to payment processing delays and transaction failures.",
      withoutData: "I don't have enough information in the knowledge base to answer your question about payment service incidents from last week. No relevant incident data was found for that time period."
    }
  };
}

/**
 * Validation checklist for knowledge-base-first compliance
 */
export const KNOWLEDGE_FIRST_CHECKLIST = {
  planning: [
    "✅ First task must be a knowledge base query",
    "✅ No reasoning tasks without knowledge dependencies", 
    "✅ All response generation depends on knowledge retrieval",
    "✅ Plan validation passes knowledge-first checks"
  ],
  execution: [
    "✅ Knowledge retrieval executes before any reasoning",
    "✅ Reasoning tools validate knowledge context exists",
    "✅ No responses generated without supporting data",
    "✅ Audit trail shows knowledge-first compliance"
  ],
  responses: [
    "✅ All responses cite knowledge base sources",
    "✅ 'Don't know' responses when no data found",
    "✅ No assumptions or external knowledge used",
    "✅ Clear indication when knowledge is insufficient"
  ]
};

// Export for testing and demonstration
export {
  demonstrateKnowledgeFirstFlow as default,
  getKnowledgeFirstTaskExample,
  KNOWLEDGE_FIRST_CHECKLIST
};
