// Shared types for mission components

export interface MissionDetails {
  mission: {
    id: string;
    title: string;
    description: string;
    status: 'pending' | 'planning' | 'executing' | 'completed' | 'failed';
    priority: 'low' | 'medium' | 'high' | 'urgent';
    createdAt: string;
    updatedAt: string;
    completedAt?: string;
  };
  plans: Array<{
    id: string;
    title: string;
    description: string;
    status: string;
    estimatedDuration: number;
    version: number;
    createdAt: string;
    updatedAt: string;
  }>;
  tasks: Array<{
    id: string;
    title: string;
    description: string;
    status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
    priority: number;
    toolName?: string;
    estimatedDuration?: number;
    actualDuration?: number;
    startedAt?: string;
    completedAt?: string;
    createdAt: string;
    updatedAt: string;
  }>;
  logs: Array<{
    id: string;
    level: 'debug' | 'info' | 'warn' | 'error';
    message: string;
    timestamp: string;
    data?: any;
  }>;
  reflections: Array<{
    id: string;
    type: 'progress_assessment' | 'plan_optimization' | 'error_analysis' | 'success_analysis';
    content: string;
    insights?: string[];
    recommendations?: string[];
    confidence?: number;
    createdAt: string;
  }>;
}

export interface Mission {
  id: number;
  title: string;
  timestamp: Date;
  status: string;
}

export interface Message {
  id: number;
  content: string;
  sender: "user" | "ai";
  timestamp: Date;
}

export interface RealtimeStatus {
  status: string;
  isActive: boolean;
  lastUpdate: Date | null;
  currentTask: string | null;
  progress?: {
    percentage: number;
    currentTask?: string;
  };
}