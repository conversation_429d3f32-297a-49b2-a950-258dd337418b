'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Target, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CreateMissionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: { title: string; description: string; priority: string }) => void;
}

export function CreateMissionDialog({ open, onOpenChange, onSubmit }: CreateMissionDialogProps) {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState('medium');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !description.trim()) return;

    setLoading(true);
    try {
      await onSubmit({ title: title.trim(), description: description.trim(), priority });
      setTitle('');
      setDescription('');
      setPriority('medium');
      onOpenChange(false);
    } finally {
      setLoading(false);
    }
  };

  const priorities = [
    { value: 'low', label: 'Low', variant: 'secondary' as const, color: 'bg-slate-100 text-slate-700 hover:bg-slate-200' },
    { value: 'medium', label: 'Medium', variant: 'default' as const, color: 'bg-blue-100 text-blue-700 hover:bg-blue-200' },
    { value: 'high', label: 'High', variant: 'outline' as const, color: 'bg-orange-100 text-orange-700 hover:bg-orange-200' },
    { value: 'urgent', label: 'Urgent', variant: 'destructive' as const, color: 'bg-red-100 text-red-700 hover:bg-red-200' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-6">
          <div className="flex items-center gap-2 mb-2">
            <Target className="h-6 w-6 text-primary" />
            <DialogTitle className="text-xl font-semibold">Create New Mission</DialogTitle>
          </div>
          <DialogDescription className="text-muted-foreground text-base">
            Define a new mission for your AI agent to execute autonomously
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Mission Title */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <label htmlFor="title" className="block text-base font-medium text-foreground">
                What should this mission be called?
              </label>
              <HelpCircle className="h-4 w-4 text-muted-foreground" />
            </div>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Mission name"
              required
              className="h-12 text-base border-border focus:border-primary focus:ring-primary/20 transition-all"
            />
          </div>

          {/* Mission Description */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <label htmlFor="description" className="block text-base font-medium text-foreground">
                What should the AI agent accomplish?
              </label>
              <HelpCircle className="h-4 w-4 text-muted-foreground" />
            </div>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe the mission objectives, expected outcomes, and any specific requirements..."
              rows={6}
              required
              className="text-base border-border focus:border-primary focus:ring-primary/20 transition-all resize-none"
            />
          </div>

          {/* Priority Selection */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <label className="block text-base font-medium text-foreground">
                What priority level should this mission have?
              </label>
              <HelpCircle className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
              {priorities.map((p) => (
                <button
                  key={p.value}
                  type="button"
                  onClick={() => setPriority(p.value)}
                  className={cn(
                    "relative flex items-center justify-center px-4 py-3 rounded-lg border-2 transition-all duration-200 text-sm font-medium",
                    priority === p.value
                      ? "border-primary bg-primary/5 text-primary shadow-sm"
                      : "border-border bg-background text-muted-foreground hover:border-muted-foreground hover:bg-muted/30"
                  )}
                >
                  <span className={cn(
                    "w-2 h-2 rounded-full mr-2",
                    p.value === 'low' && "bg-slate-400",
                    p.value === 'medium' && "bg-blue-400", 
                    p.value === 'high' && "bg-orange-400",
                    p.value === 'urgent' && "bg-red-400"
                  )} />
                  {p.label}
                  {priority === p.value && (
                    <div className="absolute inset-0 rounded-lg ring-2 ring-primary/30" />
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Additional Context Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <label htmlFor="context" className="block text-base font-medium text-foreground">
                Anything else the AI should know about this mission?
              </label>
              <HelpCircle className="h-4 w-4 text-muted-foreground" />
            </div>
            <Textarea
              id="context"
              placeholder="Additional context, constraints, or preferences..."
              rows={4}
              className="text-base border-border focus:border-primary focus:ring-primary/20 transition-all resize-none"
            />
          </div>

          <DialogFooter className="gap-3 pt-6 border-t border-border">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="px-6 py-2 h-10"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!title.trim() || !description.trim() || loading}
              className="px-6 py-2 h-10 bg-primary hover:bg-primary/90"
            >
              {loading ? 'Creating Mission...' : 'Save Mission'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 