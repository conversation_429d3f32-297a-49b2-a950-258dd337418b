import { EventEmitter } from 'events';
import { 
  ErrorSeverity, 
  ErrorCategory, 
  ServiceError, 
  ErrorContext, 
  RecoveryAction, 
  RetryOptions,
  CircuitBreakerState,
  ErrorMetrics,
  HealthCheckResult
} from './types';

/**
 * Comprehensive error handling system with recovery mechanisms
 */
export class ErrorHandler extends EventEmitter {
  private static instance: ErrorHandler;
  private errorMetrics: Map<string, ErrorMetrics> = new Map();
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();
  private readonly defaultRetryOptions: RetryOptions = {
    maxAttempts: 3,
    initialDelay: 1000,
    maxDelay: 10000,
    backoffMultiplier: 2,
    jitter: true,
    retryCondition: (error: Error) => this.isRetryableError(error)
  };

  private constructor() {
    super();
    this.setupErrorMetricsCollection();
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Create a standardized service error
   */
  createError(
    message: string,
    category: ErrorCategory,
    severity: ErrorSeverity,
    code: string,
    context: Partial<ErrorContext> = {},
    originalError?: Error
  ): ServiceError {
    const serviceError: ServiceError = Object.assign(new Error(message), {
      category,
      severity,
      code,
      context: {
        timestamp: new Date(),
        ...context
      },
      originalError,
      recoveryActions: this.determineRecoveryActions(category, severity),
      retryable: this.isRetryableCategory(category),
      userMessage: this.getUserFriendlyMessage(category, code),
      technicalMessage: message
    });

    // Record error metrics
    this.recordError(serviceError);

    // Emit error event for monitoring
    this.emit('error', serviceError);

    return serviceError;
  }

  /**
   * Execute operation with retry logic and circuit breaker
   */
  async withRetry<T>(
    operation: () => Promise<T>,
    serviceName: string,
    options: Partial<RetryOptions> = {}
  ): Promise<T> {
    const retryOptions = { ...this.defaultRetryOptions, ...options };
    const circuitBreaker = this.getCircuitBreaker(serviceName);

    // Check circuit breaker state
    if (circuitBreaker.state === 'open') {
      if (this.shouldAttemptHalfOpen(circuitBreaker)) {
        circuitBreaker.state = 'half-open';
      } else {
        throw this.createError(
          `Service ${serviceName} is currently unavailable (circuit breaker open)`,
          ErrorCategory.SYSTEM,
          ErrorSeverity.HIGH,
          'CIRCUIT_BREAKER_OPEN',
          { serviceName }
        );
      }
    }

    let lastError: Error;
    let attempt = 0;

    while (attempt < retryOptions.maxAttempts) {
      try {
        const result = await operation();
        
        // Operation succeeded - update circuit breaker
        this.recordSuccess(serviceName);
        
        return result;
      } catch (error) {
        lastError = error as Error;
        attempt++;

        // Record failure
        this.recordFailure(serviceName, lastError);

        // Check if we should retry
        if (attempt >= retryOptions.maxAttempts || !retryOptions.retryCondition(lastError)) {
          break;
        }

        // Wait before retry
        const delay = this.calculateDelay(attempt, retryOptions);
        await this.sleep(delay);
      }
    }

    // All retries failed
    const serviceError = this.createError(
      `Operation failed after ${attempt} attempts: ${lastError.message}`,
      this.categorizeError(lastError),
      ErrorSeverity.HIGH,
      'OPERATION_FAILED',
      { serviceName, attempts: attempt },
      lastError
    );

    throw serviceError;
  }

  /**
   * Execute operation with fallback mechanisms
   */
  async withFallback<T>(
    primaryOperation: () => Promise<T>,
    fallbackOperation: () => Promise<T>,
    serviceName: string
  ): Promise<T> {
    try {
      return await this.withRetry(primaryOperation, serviceName);
    } catch (primaryError) {
      console.warn(`Primary operation failed for ${serviceName}, attempting fallback:`, primaryError.message);
      
      try {
        const result = await fallbackOperation();
        
        // Log successful fallback
        this.emit('fallback-success', {
          serviceName,
          primaryError: primaryError.message,
          timestamp: new Date()
        });
        
        return result;
      } catch (fallbackError) {
        // Both primary and fallback failed
        const combinedError = this.createError(
          `Both primary and fallback operations failed for ${serviceName}`,
          ErrorCategory.SYSTEM,
          ErrorSeverity.CRITICAL,
          'FALLBACK_FAILED',
          { serviceName },
          fallbackError
        );

        this.emit('fallback-failed', {
          serviceName,
          primaryError: primaryError.message,
          fallbackError: fallbackError.message,
          timestamp: new Date()
        });

        throw combinedError;
      }
    }
  }

  /**
   * Health check for services
   */
  async healthCheck(serviceName: string, checkFunction: () => Promise<any>): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      await checkFunction();
      const responseTime = Date.now() - startTime;
      
      return {
        service: serviceName,
        status: 'healthy',
        responseTime,
        timestamp: new Date()
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const err = error as Error;
      
      return {
        service: serviceName,
        status: 'unhealthy',
        responseTime,
        error: err.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Get error metrics for monitoring
   */
  getErrorMetrics(): Map<string, ErrorMetrics> {
    return new Map(this.errorMetrics);
  }

  /**
   * Get circuit breaker states
   */
  getCircuitBreakerStates(): Map<string, CircuitBreakerState> {
    return new Map(this.circuitBreakers);
  }

  /**
   * Reset circuit breaker for a service
   */
  resetCircuitBreaker(serviceName: string): void {
    const circuitBreaker = this.getCircuitBreaker(serviceName);
    circuitBreaker.state = 'closed';
    circuitBreaker.failureCount = 0;
    circuitBreaker.successCount = 0;
    circuitBreaker.lastFailureTime = undefined;
    circuitBreaker.nextAttemptTime = undefined;
  }

  /**
   * Clear error metrics
   */
  clearErrorMetrics(): void {
    this.errorMetrics.clear();
  }

  // Private methods

  private setupErrorMetricsCollection(): void {
    // Set up periodic cleanup of old metrics
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 60000 * 15); // Every 15 minutes
  }

  private cleanupOldMetrics(): void {
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
    
    for (const [key, metrics] of this.errorMetrics.entries()) {
      if (metrics.lastOccurrence < cutoffTime) {
        this.errorMetrics.delete(key);
      }
    }
  }

  private recordError(error: ServiceError): void {
    const key = `${error.category}-${error.code}`;
    const existing = this.errorMetrics.get(key);
    
    if (existing) {
      existing.count++;
      existing.lastOccurrence = error.context.timestamp;
    } else {
      this.errorMetrics.set(key, {
        category: error.category,
        severity: error.severity,
        count: 1,
        lastOccurrence: error.context.timestamp
      });
    }
  }

  private getCircuitBreaker(serviceName: string): CircuitBreakerState {
    let circuitBreaker = this.circuitBreakers.get(serviceName);
    
    if (!circuitBreaker) {
      circuitBreaker = {
        state: 'closed',
        failureCount: 0,
        successCount: 0
      };
      this.circuitBreakers.set(serviceName, circuitBreaker);
    }
    
    return circuitBreaker;
  }

  private recordSuccess(serviceName: string): void {
    const circuitBreaker = this.getCircuitBreaker(serviceName);
    circuitBreaker.successCount++;
    
    if (circuitBreaker.state === 'half-open' && circuitBreaker.successCount >= 3) {
      circuitBreaker.state = 'closed';
      circuitBreaker.failureCount = 0;
    }
  }

  private recordFailure(serviceName: string, error: Error): void {
    const circuitBreaker = this.getCircuitBreaker(serviceName);
    circuitBreaker.failureCount++;
    circuitBreaker.lastFailureTime = new Date();
    
    // Open circuit breaker after 5 consecutive failures
    if (circuitBreaker.failureCount >= 5) {
      circuitBreaker.state = 'open';
      circuitBreaker.nextAttemptTime = new Date(Date.now() + 60000); // Try again in 1 minute
    }
  }

  private shouldAttemptHalfOpen(circuitBreaker: CircuitBreakerState): boolean {
    return circuitBreaker.nextAttemptTime ? 
      new Date() >= circuitBreaker.nextAttemptTime : 
      false;
  }

  private calculateDelay(attempt: number, options: RetryOptions): number {
    let delay = options.initialDelay * Math.pow(options.backoffMultiplier, attempt - 1);
    delay = Math.min(delay, options.maxDelay);
    
    if (options.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5); // Add jitter between 50-100% of calculated delay
    }
    
    return delay;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isRetryableError(error: Error): boolean {
    if (error.message.includes('timeout')) return true;
    if (error.message.includes('network')) return true;
    if (error.message.includes('connection')) return true;
    if (error.message.includes('503')) return true;
    if (error.message.includes('502')) return true;
    if (error.message.includes('504')) return true;
    
    return false;
  }

  private isRetryableCategory(category: ErrorCategory): boolean {
    return [
      ErrorCategory.NETWORK,
      ErrorCategory.TIMEOUT,
      ErrorCategory.RATE_LIMIT
    ].includes(category);
  }

  private categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('connection')) {
      return ErrorCategory.NETWORK;
    }
    if (message.includes('timeout')) {
      return ErrorCategory.TIMEOUT;
    }
    if (message.includes('rate') || message.includes('limit')) {
      return ErrorCategory.RATE_LIMIT;
    }
    if (message.includes('auth') || message.includes('unauthorized')) {
      return ErrorCategory.AUTHENTICATION;
    }
    if (message.includes('permission') || message.includes('forbidden')) {
      return ErrorCategory.PERMISSION;
    }
    if (message.includes('database') || message.includes('sql')) {
      return ErrorCategory.DATABASE;
    }
    if (message.includes('api')) {
      return ErrorCategory.API;
    }
    
    return ErrorCategory.UNKNOWN;
  }

  private determineRecoveryActions(category: ErrorCategory, severity: ErrorSeverity): RecoveryAction[] {
    const actions: RecoveryAction[] = [];
    
    switch (category) {
      case ErrorCategory.NETWORK:
      case ErrorCategory.TIMEOUT:
        actions.push({ type: 'retry' });
        if (severity !== ErrorSeverity.CRITICAL) {
          actions.push({ type: 'fallback' });
        }
        break;
        
      case ErrorCategory.RATE_LIMIT:
        actions.push({ type: 'retry', params: { delay: 5000 } });
        break;
        
      case ErrorCategory.API:
        if (severity === ErrorSeverity.CRITICAL) {
          actions.push({ type: 'circuit_break' });
        } else {
          actions.push({ type: 'retry' });
        }
        break;
        
      case ErrorCategory.DATABASE:
        actions.push({ type: 'retry' });
        actions.push({ type: 'escalate' });
        break;
        
      case ErrorCategory.VALIDATION:
        actions.push({ type: 'ignore' });
        break;
        
      default:
        if (severity === ErrorSeverity.CRITICAL) {
          actions.push({ type: 'escalate' });
        } else {
          actions.push({ type: 'retry' });
        }
    }
    
    return actions;
  }

  private getUserFriendlyMessage(category: ErrorCategory, code: string): string {
    const messages: Record<string, string> = {
      [ErrorCategory.NETWORK]: 'Connection issue - please check your internet connection and try again.',
      [ErrorCategory.TIMEOUT]: 'The operation took too long to complete. Please try again.',
      [ErrorCategory.RATE_LIMIT]: 'Too many requests. Please wait a moment and try again.',
      [ErrorCategory.AUTHENTICATION]: 'Authentication failed. Please log in again.',
      [ErrorCategory.PERMISSION]: 'You do not have permission to perform this action.',
      [ErrorCategory.VALIDATION]: 'Invalid input provided. Please check your data and try again.',
      [ErrorCategory.DATABASE]: 'Data storage issue. Please try again later.',
      [ErrorCategory.API]: 'Service temporarily unavailable. Please try again later.',
      [ErrorCategory.SYSTEM]: 'System error occurred. Please try again later.',
      [ErrorCategory.BUSINESS_LOGIC]: 'Operation could not be completed due to business rules.',
      [ErrorCategory.UNKNOWN]: 'An unexpected error occurred. Please try again later.'
    };
    
    return messages[category] || 'An error occurred. Please try again later.';
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();