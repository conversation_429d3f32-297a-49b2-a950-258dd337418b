import { NextRequest, NextResponse } from 'next/server';
import { vectorStoreService } from '@/lib/vector-store/service-manager';

/**
 * GET /api/vector-store - Get vector store status and statistics
 */
export async function GET() {
  try {
    const [status, statistics, health] = await Promise.all([
      vectorStoreService.getStatus(),
      vectorStoreService.getStatistics(),
      vectorStoreService.healthCheck(),
    ]);

    return NextResponse.json({
      success: true,
      data: {
        status,
        statistics,
        health,
      },
    });
  } catch (error) {
    console.error('Error getting vector store status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get vector store status',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vector-store - Initialize or reconnect vector store
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, chromaUrl } = body;

    switch (action) {
      case 'initialize': {
        const result = await vectorStoreService.initialize(chromaUrl);
        return NextResponse.json({
          success: true,
          data: result,
        });
      }

      case 'reconnect': {
        const success = await vectorStoreService.reconnectChroma(chromaUrl);
        return NextResponse.json({
          success: true,
          data: {
            reconnected: success,
            chromaAvailable: success,
          },
        });
      }

      case 'migrate': {
        const result = await vectorStoreService.migrateToVectorStore();
        return NextResponse.json({
          success: result.success,
          data: result,
        });
      }

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid action',
            validActions: ['initialize', 'reconnect', 'migrate'],
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in vector store operation:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Vector store operation failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}