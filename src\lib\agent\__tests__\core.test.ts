import { jest } from '@jest/globals';
import { ServiceManagementAgentImpl } from '../core';
import { AgentTestUtils, mockMission, mockPlan, mockTask } from '@/test/utils';

// Mock dependencies
const mockDeepSeekClient = AgentTestUtils.createMockDeepSeekClient();
const mockToolRegistry = AgentTestUtils.createMockToolRegistry();
const mockLifecycleManager = AgentTestUtils.createMockLifecycleManager();

jest.mock('../deepseek', () => ({
  DeepSeekClient: jest.fn().mockImplementation(() => mockDeepSeekClient)
}));

jest.mock('../tools/registry', () => ({
  ToolRegistry: jest.fn().mockImplementation(() => mockToolRegistry)
}));

jest.mock('../lifecycle', () => ({
  MissionLifecycleManager: {
    getInstance: jest.fn().mockReturnValue(mockLifecycleManager)
  }
}));

describe('ServiceManagementAgentImpl', () => {
  let agent: ServiceManagementAgentImpl;
  
  beforeEach(() => {
    jest.clearAllMocks();
    agent = new ServiceManagementAgentImpl({
      deepseekApiKey: 'test-key',
      deepseekBaseUrl: 'https://test.api.com'
    });
  });

  describe('createMission', () => {
    it('creates a new mission successfully', async () => {
      const missionRequest = {
        title: 'Test Mission',
        description: 'Test description'
      };

      const result = await agent.createMission(missionRequest);

      expect(result).toEqual(mockMission);
      expect(mockLifecycleManager.createMission).toHaveBeenCalledWith(
        expect.objectContaining(missionRequest)
      );
    });

    it('handles mission creation errors', async () => {
      const error = new Error('Mission creation failed');
      mockLifecycleManager.createMission.mockRejectedValueOnce(error);

      await expect(agent.createMission({
        title: 'Test Mission',
        description: 'Test description'
      })).rejects.toThrow('Mission creation failed');
    });
  });

  describe('generatePlan', () => {
    it('generates a plan for a mission', async () => {
      const planRequest = {
        missionId: 'test-mission-1',
        requirements: ['Requirement 1', 'Requirement 2'],
        constraints: ['Constraint 1']
      };

      mockDeepSeekClient.generatePlan.mockResolvedValueOnce(JSON.stringify({
        title: 'Generated Plan',
        description: 'Generated plan description',
        tasks: [mockTask]
      }));

      const result = await agent.generatePlan(planRequest);

      expect(result).toHaveProperty('title', 'Generated Plan');
      expect(result).toHaveProperty('tasks');
      expect(mockDeepSeekClient.generatePlan).toHaveBeenCalledWith(
        expect.objectContaining(planRequest)
      );
    });

    it('handles plan generation errors', async () => {
      mockDeepSeekClient.generatePlan.mockRejectedValueOnce(
        new Error('Plan generation failed')
      );

      await expect(agent.generatePlan({
        missionId: 'test-mission-1',
        requirements: ['Requirement 1']
      })).rejects.toThrow('Plan generation failed');
    });

    it('validates generated plan structure', async () => {
      // Mock invalid plan response
      mockDeepSeekClient.generatePlan.mockResolvedValueOnce(
        JSON.stringify({ invalid: 'plan' })
      );

      await expect(agent.generatePlan({
        missionId: 'test-mission-1',
        requirements: ['Requirement 1']
      })).rejects.toThrow(/Invalid plan structure/);
    });
  });

  describe('executeMission', () => {
    it('executes a mission successfully', async () => {
      const missionId = 'test-mission-1';
      
      // Mock mission state
      mockLifecycleManager.getMissionState.mockReturnValue({
        mission: mockMission,
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0,
        isRunning: false,
        isPaused: false,
        cancellationRequested: false
      });

      await agent.executeMission(missionId);

      expect(mockLifecycleManager.updateMissionStatus).toHaveBeenCalledWith(
        missionId,
        'running'
      );
    });

    it('handles mission execution with task failures', async () => {
      const missionId = 'test-mission-1';
      
      // Mock tool execution failure
      const mockTool = {
        name: 'test-tool',
        description: 'Test tool',
        execute: jest.fn().mockRejectedValue(new Error('Tool execution failed'))
      };
      
      mockToolRegistry.get.mockReturnValue(mockTool);
      
      mockLifecycleManager.getMissionState.mockReturnValue({
        mission: mockMission,
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0,
        isRunning: false,
        isPaused: false,
        cancellationRequested: false
      });

      await agent.executeMission(missionId);

      // Should handle tool failure gracefully
      expect(mockLifecycleManager.updateMissionStatus).toHaveBeenCalledWith(
        missionId,
        'running'
      );
    });

    it('respects cancellation requests', async () => {
      const missionId = 'test-mission-1';
      
      mockLifecycleManager.getMissionState.mockReturnValue({
        mission: mockMission,
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0,
        isRunning: true,
        isPaused: false,
        cancellationRequested: true
      });

      await agent.executeMission(missionId);

      expect(mockLifecycleManager.updateMissionStatus).toHaveBeenCalledWith(
        missionId,
        'cancelled'
      );
    });
  });

  describe('reflect', () => {
    it('performs reflection on mission progress', async () => {
      const reflectionRequest = {
        missionId: 'test-mission-1',
        context: 'Test context'
      };

      mockDeepSeekClient.reflect.mockResolvedValueOnce(JSON.stringify({
        insights: ['Insight 1', 'Insight 2'],
        recommendations: ['Recommendation 1'],
        confidence: 0.8
      }));

      const result = await agent.reflect(reflectionRequest);

      expect(result).toHaveProperty('insights');
      expect(result).toHaveProperty('recommendations');
      expect(result.confidence).toBeGreaterThan(0);
      expect(mockDeepSeekClient.reflect).toHaveBeenCalledWith(
        expect.objectContaining(reflectionRequest)
      );
    });

    it('handles reflection errors gracefully', async () => {
      mockDeepSeekClient.reflect.mockRejectedValueOnce(
        new Error('Reflection failed')
      );

      await expect(agent.reflect({
        missionId: 'test-mission-1',
        context: 'Test context'
      })).rejects.toThrow('Reflection failed');
    });
  });

  describe('selectTool', () => {
    it('selects appropriate tool for task', async () => {
      const selectionRequest = {
        taskDescription: 'Test task',
        availableTools: ['tool1', 'tool2'],
        context: 'Test context'
      };

      mockDeepSeekClient.selectTool.mockResolvedValueOnce(JSON.stringify({
        selectedTool: 'tool1',
        reasoning: 'Tool1 is best for this task',
        confidence: 0.9
      }));

      const result = await agent.selectTool(selectionRequest);

      expect(result.selectedTool).toBe('tool1');
      expect(result.confidence).toBeGreaterThan(0);
      expect(mockDeepSeekClient.selectTool).toHaveBeenCalledWith(
        expect.objectContaining(selectionRequest)
      );
    });

    it('handles invalid tool selection', async () => {
      mockDeepSeekClient.selectTool.mockResolvedValueOnce(
        JSON.stringify({ invalid: 'response' })
      );

      await expect(agent.selectTool({
        taskDescription: 'Test task',
        availableTools: ['tool1'],
        context: 'Test context'
      })).rejects.toThrow(/Invalid tool selection/);
    });
  });

  describe('assessCompletion', () => {
    it('assesses task completion correctly', async () => {
      const assessmentRequest = {
        taskId: 'test-task-1',
        expectedOutcome: 'Expected result',
        actualResult: 'Actual result'
      };

      mockDeepSeekClient.assessCompletion.mockResolvedValueOnce(JSON.stringify({
        completed: true,
        completionPercentage: 1.0,
        reasoning: 'Task completed successfully'
      }));

      const result = await agent.assessCompletion(assessmentRequest);

      expect(result.completed).toBe(true);
      expect(result.completionPercentage).toBe(1.0);
      expect(mockDeepSeekClient.assessCompletion).toHaveBeenCalledWith(
        expect.objectContaining(assessmentRequest)
      );
    });

    it('handles partial completion assessment', async () => {
      mockDeepSeekClient.assessCompletion.mockResolvedValueOnce(JSON.stringify({
        completed: false,
        completionPercentage: 0.6,
        reasoning: 'Task partially completed'
      }));

      const result = await agent.assessCompletion({
        taskId: 'test-task-1',
        expectedOutcome: 'Expected result',
        actualResult: 'Partial result'
      });

      expect(result.completed).toBe(false);
      expect(result.completionPercentage).toBe(0.6);
    });
  });

  describe('pauseMission', () => {
    it('pauses a running mission', async () => {
      const missionId = 'test-mission-1';
      
      await agent.pauseMission(missionId);

      expect(mockLifecycleManager.requestPause).toHaveBeenCalledWith(missionId);
    });
  });

  describe('resumeMission', () => {
    it('resumes a paused mission', async () => {
      const missionId = 'test-mission-1';
      
      await agent.resumeMission(missionId);

      expect(mockLifecycleManager.requestResume).toHaveBeenCalledWith(missionId);
    });
  });

  describe('cancelMission', () => {
    it('cancels a mission', async () => {
      const missionId = 'test-mission-1';
      
      await agent.cancelMission(missionId);

      expect(mockLifecycleManager.requestCancellation).toHaveBeenCalledWith(missionId);
    });
  });

  describe('getMissionStatus', () => {
    it('returns current mission status', async () => {
      const missionId = 'test-mission-1';
      
      mockLifecycleManager.getMissionState.mockReturnValue({
        mission: mockMission,
        plan: mockPlan,
        tasks: [mockTask],
        progress: 0.5,
        isRunning: true,
        isPaused: false,
        cancellationRequested: false
      });

      const result = await agent.getMissionStatus(missionId);

      expect(result).toHaveProperty('mission');
      expect(result).toHaveProperty('progress', 0.5);
      expect(result.isRunning).toBe(true);
    });

    it('throws error for non-existent mission', async () => {
      const missionId = 'non-existent';
      
      mockLifecycleManager.getMissionState.mockReturnValue(null);

      await expect(agent.getMissionStatus(missionId)).rejects.toThrow(
        /Mission not found/
      );
    });
  });
});