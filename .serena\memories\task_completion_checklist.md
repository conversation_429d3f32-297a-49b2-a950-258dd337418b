# Task Completion Checklist

When completing development tasks on this project, ensure the following:

## Code Quality
- [ ] Run `npm run lint` and fix any ESLint errors
- [ ] Ensure TypeScript compilation passes (no `tsc` errors)
- [ ] Follow established code style conventions
- [ ] Add appropriate TypeScript types for new code

## Database Changes
- [ ] If schema changes: run `npm run db:generate` for new migration
- [ ] Test migrations with `npm run db:migrate`
- [ ] Update type definitions if schema changed
- [ ] Verify database operations work correctly

## Testing
- [ ] Test the feature manually in development (`npm run dev`)
- [ ] Verify API endpoints work with appropriate test data
- [ ] Check both happy path and error scenarios
- [ ] Test UI components for accessibility and responsiveness

## Agent Integration
- [ ] Ensure new tools are registered in the ToolRegistry
- [ ] Test agent capabilities work as expected
- [ ] Verify DeepSeek API integration functions correctly
- [ ] Check that reflective learning and planning work

## Documentation
- [ ] Update relevant memory files if architecture changes
- [ ] Add JSDoc comments for complex functions
- [ ] Update README if user-facing changes

## Deployment Readiness
- [ ] Build succeeds: `npm run build`
- [ ] No console errors in production build
- [ ] Environment variables properly configured
- [ ] Database initialization works on fresh setup

## Final Steps
- [ ] Commit changes with descriptive message
- [ ] Push to repository
- [ ] Verify deployment if applicable