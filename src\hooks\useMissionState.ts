import { useState, useCallback, useEffect } from 'react';
import { Mission, Message, MissionDetails, RealtimeStatus } from '@/components/mission/types';
import { useMissionUpdates } from './useMissionUpdates';
import { MISSION_STATUS, UI_CONFIG } from '@/lib/constants';

/**
 * Custom hook to manage mission state and operations
 */
export function useMissionState() {
  // Core state
  const [missions, setMissions] = useState<Mission[]>([
    {
      id: 1,
      title: "Sample Mission",
      timestamp: new Date(),
      status: "completed",
    },
  ]);

  const [selectedMissionId, setSelectedMissionId] = useState<number | null>(null);
  const [currentView, setCurrentView] = useState<"chat" | "knowledge">("chat");
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Mission details state
  const [missionDetails, setMissionDetails] = useState<MissionDetails | null>(null);
  const [loadingMissionDetails, setLoadingMissionDetails] = useState(false);

  // Search state
  const [searchQuery, setSearchQuery] = useState("");

  // Editing state
  const [editingMissionId, setEditingMissionId] = useState<number | null>(null);
  const [editingValue, setEditingValue] = useState("");

  // Dialog state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [missionToDelete, setMissionToDelete] = useState<Mission | null>(null);

  // WebSocket integration
  const {
    isConnected: wsConnected,
    connectionError,
    subscribeMission,
    unsubscribeMission,
    getMission: getWsMission,
    missions: wsMissions,
    clearMission
  } = useMissionUpdates({
    autoConnect: true,
    maxLogs: UI_CONFIG.MAX_LOGS_DISPLAY,
  });

  // Mission operations
  const startNewMission = useCallback(() => {
    const newMission: Mission = {
      id: missions.length + 1,
      title: "New Mission",
      timestamp: new Date(),
      status: "in-progress",
    };
    setMissions((prev) => [newMission, ...prev]);
    setSelectedMissionId(newMission.id);
    setCurrentView("chat");
    setMessages([]);
  }, [missions.length]);

  const selectMission = useCallback((missionId: number) => {
    setSelectedMissionId(missionId);
    setCurrentView("chat");
    setMessages([
      {
        id: 1,
        content: `Loaded mission ${missionId}. How can I help you continue with this mission?`,
        sender: "ai",
        timestamp: new Date(),
      },
    ]);
  }, []);

  const selectKnowledgeBase = useCallback(() => {
    setCurrentView("knowledge");
    setSelectedMissionId(null);
  }, []);

  const renameMission = useCallback((missionId: number, newTitle: string) => {
    setMissions(prev =>
      prev.map(mission =>
        mission.id === missionId ? { ...mission, title: newTitle } : mission
      )
    );
  }, []);

  // Dialog operations
  const openDeleteDialog = useCallback((mission: Mission) => {
    setMissionToDelete(mission);
    setDeleteDialogOpen(true);
  }, []);

  const confirmDeleteMission = useCallback(() => {
    if (missionToDelete) {
      setMissions(prev => prev.filter(mission => mission.id !== missionToDelete.id));
      if (selectedMissionId === missionToDelete.id) {
        setSelectedMissionId(null);
        setCurrentView("chat");
      }
      setDeleteDialogOpen(false);
      setMissionToDelete(null);
    }
  }, [missionToDelete, selectedMissionId]);

  const cancelDeleteMission = useCallback(() => {
    setDeleteDialogOpen(false);
    setMissionToDelete(null);
  }, []);

  // Search operations
  const clearSearch = useCallback(() => {
    setSearchQuery("");
  }, []);

  // Editing operations
  const startEditingMission = useCallback((missionId: number, currentTitle: string) => {
    setEditingMissionId(missionId);
    setEditingValue(currentTitle);
  }, []);

  const saveEditingMission = useCallback(() => {
    if (editingMissionId && editingValue.trim()) {
      renameMission(editingMissionId, editingValue.trim());
      setEditingMissionId(null);
      setEditingValue("");
    }
  }, [editingMissionId, editingValue, renameMission]);

  const cancelEditingMission = useCallback(() => {
    setEditingMissionId(null);
    setEditingValue("");
  }, []);

  // Fetch mission details
  const fetchMissionDetails = useCallback(async () => {
    if (!selectedMissionId) return;

    setLoadingMissionDetails(true);
    try {
      const response = await fetch(`/api/missions/${selectedMissionId}`);
      if (response.ok) {
        const details = await response.json();
        setMissionDetails(details);
      }
    } catch (error) {
      console.error('Failed to fetch mission details:', error);
    } finally {
      setLoadingMissionDetails(false);
    }
  }, [selectedMissionId]);

  // Real-time status helpers
  const getRealtimeMissionStatus = useCallback((missionId: number): RealtimeStatus | null => {
    const wsMission = getWsMission(missionId.toString());
    if (wsMission) {
      return {
        status: wsMission.status,
        isActive: wsMission.status === MISSION_STATUS.EXECUTING || wsMission.status === MISSION_STATUS.PLANNING,
        progress: wsMission.progress,
        lastUpdate: new Date(wsMission.lastUpdate),
        currentTask: wsMission.progress?.currentTask || null
      };
    }
    return null;
  }, [getWsMission]);

  const getRealtimeStatus = useCallback((): RealtimeStatus => {
    if (selectedMissionId) {
      const wsMission = getWsMission(selectedMissionId.toString());
      if (wsMission) {
        return {
          status: wsMission.status,
          isActive: wsMission.status === MISSION_STATUS.EXECUTING || wsMission.status === MISSION_STATUS.PLANNING,
          lastUpdate: new Date(wsMission.lastUpdate),
          currentTask: wsMission.progress?.currentTask || null,
          progress: wsMission.progress
        };
      }
    }
    
    return {
      status: missionDetails?.mission.status || MISSION_STATUS.PENDING,
      isActive: false,
      lastUpdate: null,
      currentTask: null
    };
  }, [selectedMissionId, getWsMission, missionDetails]);

  // Effects
  useEffect(() => {
    if (selectedMissionId) {
      fetchMissionDetails();
      
      if (wsConnected) {
        subscribeMission(selectedMissionId.toString());
        
        return () => {
          unsubscribeMission(selectedMissionId.toString());
        };
      }
    } else {
      setMissionDetails(null);
    }
  }, [selectedMissionId, wsConnected, subscribeMission, unsubscribeMission, fetchMissionDetails]);

  // Update details from WebSocket data
  useEffect(() => {
    if (selectedMissionId && missionDetails) {
      const wsMission = getWsMission(selectedMissionId.toString());
      if (wsMission) {
        setMissionDetails((prev: MissionDetails | null) => prev ? {
          ...prev,
          mission: {
            ...prev.mission,
            status: wsMission.status as any,
            updatedAt: wsMission.lastUpdate
          },
          logs: wsMission.logs ? wsMission.logs.map((log: any) => ({
            id: log.id || Math.random().toString(),
            level: log.level,
            message: log.message,
            timestamp: log.timestamp,
            data: log.data
          })) : prev.logs
        } : prev);
      }
    }
  }, [selectedMissionId, missionDetails, getWsMission]);

  return {
    // State
    missions,
    selectedMissionId,
    currentView,
    messages,
    input,
    isLoading,
    missionDetails,
    loadingMissionDetails,
    searchQuery,
    editingMissionId,
    editingValue,
    deleteDialogOpen,
    missionToDelete,
    wsConnected,
    connectionError,

    // Setters
    setMessages,
    setInput,
    setIsLoading,
    setSearchQuery,
    setEditingValue,

    // Operations
    startNewMission,
    selectMission,
    selectKnowledgeBase,
    renameMission,
    openDeleteDialog,
    confirmDeleteMission,
    cancelDeleteMission,
    clearSearch,
    startEditingMission,
    saveEditingMission,
    cancelEditingMission,
    fetchMissionDetails,
    getRealtimeMissionStatus,
    getRealtimeStatus,
  };
}
