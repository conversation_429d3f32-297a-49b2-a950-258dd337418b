"use client"

import { Alert<PERSON>riangle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog"
import { Mission } from "./types"

interface DeleteMissionDialogProps {
  isOpen: boolean;
  mission: Mission | null;
  onConfirm: () => void;
  onCancel: () => void;
}

export function DeleteMissionDialog({ 
  isOpen, 
  mission, 
  onConfirm, 
  onCancel 
}: DeleteMissionDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onCancel()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <DialogTitle>Delete Mission</DialogTitle>
          </div>
          <DialogDescription>
            Are you sure you want to delete "{mission?.title}"? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={onConfirm}>
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}