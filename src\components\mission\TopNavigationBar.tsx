"use client"

import { History } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { UserProfileMenu } from "./UserProfileMenu"

interface TopNavigationBarProps {
  currentView: "chat" | "knowledge";
  wsConnected: boolean;
  connectionError?: string | null;
  onCustomizeAssistant: () => void;
  onSettings: () => void;
  onHelp: () => void;
  onLogout: () => void;
}

export function TopNavigationBar({
  currentView,
  wsConnected,
  connectionError,
  onCustomizeAssistant,
  onSettings,
  onHelp,
  onLogout
}: TopNavigationBarProps) {
  return (
    <div className="p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h1 className="text-lg font-semibold">
            {currentView === "knowledge" ? "Service Management Knowledge Base" : "Service Management Assistant"}
          </h1>
          
          {/* WebSocket Connection Status */}
          <div className="flex items-center gap-2 text-xs">
            <div className={cn(
              "w-2 h-2 rounded-full",
              wsConnected ? "bg-green-500" : "bg-red-500"
            )} />
            <span className="text-muted-foreground">
              {wsConnected ? "Real-time updates active" : connectionError || "Connecting..."}
            </span>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="icon" 
            title="History"
            className="hover:bg-muted hover:text-muted-foreground"
          >
            <History className="h-4 w-4" />
          </Button>
          
          <UserProfileMenu
            onCustomizeAssistant={onCustomizeAssistant}
            onSettings={onSettings}
            onHelp={onHelp}
            onLogout={onLogout}
          />
        </div>
      </div>
    </div>
  );
}