import { jest } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET, POST } from '../missions/route';
// Remove PUT and DELETE as they don't exist in the route

// Mock database
const mockDb = {
  select: jest.fn().mockReturnThis(),
  from: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  offset: jest.fn().mockReturnThis(),
  insert: jest.fn().mockReturnThis(),
  values: jest.fn().mockReturnThis(),
  returning: jest.fn().mockReturnThis(),
  all: jest.fn(),
};

jest.mock('@/lib/db', () => ({
  default: mockDb
}));

jest.mock('@/lib/db/schema', () => ({
  missions: {
    createdAt: 'createdAt',
    status: 'status',
    title: 'title'
  }
}));

// Mock agent
const mockAgent = {
  executeMission: jest.fn(),
};

jest.mock('@/lib/agent/core', () => ({
  ServiceManagementAgentImpl: jest.fn().mockImplementation(() => mockAgent)
}));

const mockMission = {
  id: 'test-mission-1',
  title: 'Test Mission',
  description: 'Test description',
  status: 'pending',
  priority: 'medium',
  createdAt: new Date(),
  updatedAt: new Date(),
  completedAt: null,
  metadata: {}
};

describe('/api/missions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/missions', () => {
    it('returns list of missions', async () => {
      const missions = [mockMission];
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.orderBy.mockResolvedValue(missions);

      const response = await GET();
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(1);
      expect(mockDb.select).toHaveBeenCalled();
    });

    it('handles database errors', async () => {
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.orderBy.mockRejectedValue(new Error('Database error'));

      const response = await GET();

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to fetch missions');
    });
  });

  describe('POST /api/missions', () => {
    it('creates a new mission', async () => {
      const missionData = {
        title: 'New Mission',
        description: 'Mission description',
        priority: 'high'
      };

      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockResolvedValue([mockMission]);

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'POST',
        body: JSON.stringify(missionData)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      expect(mockDb.insert).toHaveBeenCalled();
    });

    it('validates required fields', async () => {
      const invalidData = {
        description: 'Missing title'
      };

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Title and description are required');
    });

    it('handles mission creation errors', async () => {
      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockReturnValue(mockDb);
      mockDb.returning.mockRejectedValue(new Error('Mission creation failed'));

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'POST',
        body: JSON.stringify({
          title: 'Test Mission',
          description: 'Test description'
        })
      });

      const response = await POST(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to create mission');
    });
  });
});