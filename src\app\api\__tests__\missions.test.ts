import { jest } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET, POST, PUT, DELETE } from '../missions/route';
import { TestUtils, DbTestUtils, mockMission } from '@/test/utils';

// Mock database
const mockDb = DbTestUtils.createMockDb();

jest.mock('@/lib/db', () => ({
  default: mockDb
}));

// Mock agent
const mockAgent = {
  createMission: jest.fn(),
  executeMission: jest.fn(),
  pauseMission: jest.fn(),
  resumeMission: jest.fn(),
  cancelMission: jest.fn(),
  getMissionStatus: jest.fn()
};

jest.mock('@/lib/agent', () => ({
  agent: mockAgent
}));

describe('/api/missions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/missions', () => {
    it('returns list of missions', async () => {
      const missions = [mockMission];
      mockDb.all.mockResolvedValueOnce(missions);

      const request = new NextRequest('http://localhost:3000/api/missions');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.missions).toEqual(missions);
      expect(mockDb.select).toHaveBeenCalled();
    });

    it('filters missions by status', async () => {
      const request = new NextRequest(
        'http://localhost:3000/api/missions?status=pending'
      );
      
      await GET(request);

      expect(mockDb.where).toHaveBeenCalledWith(
        expect.objectContaining({ status: 'pending' })
      );
    });

    it('searches missions by title', async () => {
      const request = new NextRequest(
        'http://localhost:3000/api/missions?search=test'
      );
      
      await GET(request);

      expect(mockDb.where).toHaveBeenCalledWith(
        expect.objectContaining({ title: expect.stringContaining('test') })
      );
    });

    it('paginates results', async () => {
      const request = new NextRequest(
        'http://localhost:3000/api/missions?page=2&limit=10'
      );
      
      await GET(request);

      expect(mockDb.limit).toHaveBeenCalledWith(10);
      expect(mockDb.offset).toHaveBeenCalledWith(10); // (page - 1) * limit
    });

    it('handles database errors', async () => {
      mockDb.all.mockRejectedValueOnce(new Error('Database error'));

      const request = new NextRequest('http://localhost:3000/api/missions');
      const response = await GET(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.error).toBe('Failed to fetch missions');
    });
  });

  describe('POST /api/missions', () => {
    it('creates a new mission', async () => {
      const missionData = {
        title: 'New Mission',
        description: 'Mission description',
        priority: 'high'
      };

      mockAgent.createMission.mockResolvedValueOnce(mockMission);

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'POST',
        body: JSON.stringify(missionData)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.mission).toEqual(mockMission);
      expect(mockAgent.createMission).toHaveBeenCalledWith(
        expect.objectContaining(missionData)
      );
    });

    it('validates required fields', async () => {
      const invalidData = {
        description: 'Missing title'
      };

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'POST',
        body: JSON.stringify(invalidData)
      });

      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toContain('title is required');
    });

    it('handles mission creation errors', async () => {
      mockAgent.createMission.mockRejectedValueOnce(
        new Error('Mission creation failed')
      );

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'POST',
        body: JSON.stringify({
          title: 'Test Mission',
          description: 'Test description'
        })
      });

      const response = await POST(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.error).toBe('Failed to create mission');
    });

    it('sanitizes input data', async () => {
      const maliciousData = {
        title: '<script>alert("xss")</script>',
        description: 'Normal description'
      };

      mockAgent.createMission.mockResolvedValueOnce(mockMission);

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'POST',
        body: JSON.stringify(maliciousData)
      });

      await POST(request);

      expect(mockAgent.createMission).toHaveBeenCalledWith(
        expect.objectContaining({
          title: expect.not.stringContaining('<script>')
        })
      );
    });
  });

  describe('PUT /api/missions', () => {
    it('updates mission status', async () => {
      const missionId = 'test-mission-1';
      const updateData = {
        id: missionId,
        action: 'execute'
      };

      mockAgent.executeMission.mockResolvedValueOnce(undefined);

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      });

      const response = await PUT(request);

      expect(response.status).toBe(200);
      expect(mockAgent.executeMission).toHaveBeenCalledWith(missionId);
    });

    it('pauses a mission', async () => {
      const missionId = 'test-mission-1';
      const updateData = {
        id: missionId,
        action: 'pause'
      };

      mockAgent.pauseMission.mockResolvedValueOnce(undefined);

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      });

      const response = await PUT(request);

      expect(response.status).toBe(200);
      expect(mockAgent.pauseMission).toHaveBeenCalledWith(missionId);
    });

    it('resumes a mission', async () => {
      const missionId = 'test-mission-1';
      const updateData = {
        id: missionId,
        action: 'resume'
      };

      mockAgent.resumeMission.mockResolvedValueOnce(undefined);

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      });

      const response = await PUT(request);

      expect(response.status).toBe(200);
      expect(mockAgent.resumeMission).toHaveBeenCalledWith(missionId);
    });

    it('cancels a mission', async () => {
      const missionId = 'test-mission-1';
      const updateData = {
        id: missionId,
        action: 'cancel'
      };

      mockAgent.cancelMission.mockResolvedValueOnce(undefined);

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      });

      const response = await PUT(request);

      expect(response.status).toBe(200);
      expect(mockAgent.cancelMission).toHaveBeenCalledWith(missionId);
    });

    it('validates action parameter', async () => {
      const updateData = {
        id: 'test-mission-1',
        action: 'invalid-action'
      };

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      });

      const response = await PUT(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toContain('Invalid action');
    });

    it('requires mission ID', async () => {
      const updateData = {
        action: 'execute'
      };

      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'PUT',
        body: JSON.stringify(updateData)
      });

      const response = await PUT(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toContain('Mission ID is required');
    });
  });

  describe('DELETE /api/missions', () => {
    it('deletes a mission', async () => {
      const missionId = 'test-mission-1';
      
      mockDb.run.mockResolvedValueOnce({ changes: 1 });

      const request = new NextRequest(
        `http://localhost:3000/api/missions?id=${missionId}`,
        { method: 'DELETE' }
      );

      const response = await DELETE(request);

      expect(response.status).toBe(200);
      expect(mockDb.delete).toHaveBeenCalled();
    });

    it('handles non-existent mission deletion', async () => {
      const missionId = 'non-existent';
      
      mockDb.run.mockResolvedValueOnce({ changes: 0 });

      const request = new NextRequest(
        `http://localhost:3000/api/missions?id=${missionId}`,
        { method: 'DELETE' }
      );

      const response = await DELETE(request);

      expect(response.status).toBe(404);
      const data = await response.json();
      expect(data.error).toBe('Mission not found');
    });

    it('requires mission ID for deletion', async () => {
      const request = new NextRequest(
        'http://localhost:3000/api/missions',
        { method: 'DELETE' }
      );

      const response = await DELETE(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toContain('Mission ID is required');
    });

    it('handles database errors during deletion', async () => {
      const missionId = 'test-mission-1';
      
      mockDb.run.mockRejectedValueOnce(new Error('Database error'));

      const request = new NextRequest(
        `http://localhost:3000/api/missions?id=${missionId}`,
        { method: 'DELETE' }
      );

      const response = await DELETE(request);

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.error).toBe('Failed to delete mission');
    });
  });

  describe('Rate limiting', () => {
    it('implements rate limiting for mission creation', async () => {
      // Mock multiple rapid requests
      const requests = Array.from({ length: 10 }, () =>
        new NextRequest('http://localhost:3000/api/missions', {
          method: 'POST',
          body: JSON.stringify({
            title: 'Test Mission',
            description: 'Test description'
          })
        })
      );

      const responses = await Promise.all(
        requests.map(request => POST(request))
      );

      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Authentication', () => {
    it('requires authentication for mission operations', async () => {
      // Mock unauthenticated request
      const request = new NextRequest('http://localhost:3000/api/missions', {
        method: 'POST',
        body: JSON.stringify({
          title: 'Test Mission',
          description: 'Test description'
        })
        // No authentication headers
      });

      const response = await POST(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Authentication required');
    });
  });
});