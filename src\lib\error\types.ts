// Error types for comprehensive error handling system

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  NETWORK = 'network',
  API = 'api',
  DATABASE = 'database',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  PERMISSION = 'permission',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system',
  TIMEOUT = 'timeout',
  RATE_LIMIT = 'rate_limit',
  UNKNOWN = 'unknown'
}

export interface ErrorContext {
  userId?: string;
  missionId?: string;
  taskId?: string;
  operationId?: string;
  requestId?: string;
  timestamp: Date;
  userAgent?: string;
  ip?: string;
  // Service-specific context
  serviceName?: string;
  service?: string;
  operation?: string;
  endpoint?: string;
  // Database/collection context
  collection?: string;
  collectionName?: string;
  // Additional flexible data
  [key: string]: any;
}

export interface RecoveryAction {
  type: 'retry' | 'fallback' | 'escalate' | 'ignore' | 'circuit_break';
  params?: Record<string, any>;
}

export interface ServiceError extends Error {
  category: ErrorCategory;
  severity: ErrorSeverity;
  code: string;
  context: ErrorContext;
  originalError?: Error;
  recoveryActions: RecoveryAction[];
  retryable: boolean;
  userMessage: string;
  technicalMessage: string;
}

export interface RetryOptions {
  maxAttempts: number;
  initialDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
  retryCondition: (error: Error) => boolean;
}

export interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failureCount: number;
  lastFailureTime?: Date;
  nextAttemptTime?: Date;
  successCount: number;
}

export interface ErrorMetrics {
  category: ErrorCategory;
  severity: ErrorSeverity;
  count: number;
  lastOccurrence: Date;
  averageResponseTime?: number;
  successRate?: number;
}

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  error?: string;
  timestamp: Date;
  dependencies?: HealthCheckResult[];
}