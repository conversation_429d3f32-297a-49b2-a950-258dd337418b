import { NextRequest, NextResponse } from 'next/server';
import { errorMonitor, defaultAlertRules } from '@/lib/error/monitoring';
import { errorHandler } from '@/lib/error/error-handler';

// Initialize monitoring with default alert rules
defaultAlertRules.forEach(rule => errorMonitor.addAlertRule(rule));

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const type = url.searchParams.get('type') || 'status';

    switch (type) {
      case 'status':
        const healthStatus = errorMonitor.getSystemHealth();
        return NextResponse.json(healthStatus);

      case 'metrics':
        const errorMetrics = errorHandler.getErrorMetrics();
        const circuitBreakers = errorHandler.getCircuitBreakerStates();
        
        return NextResponse.json({
          errorMetrics: Array.from(errorMetrics.entries()).map(([key, value]) => ({
            key,
            ...value
          })),
          circuitBreakers: Array.from(circuitBreakers.entries()).map(([service, state]) => ({
            service,
            ...state
          }))
        });

      case 'trends':
        const errorTrends = errorMonitor.getErrorTrends();
        return NextResponse.json({ trends: errorTrends });

      case 'report':
        const start = url.searchParams.get('start');
        const end = url.searchParams.get('end');
        
        const timeRange = {
          start: start ? new Date(start) : new Date(Date.now() - 24 * 60 * 60 * 1000), // 24 hours ago
          end: end ? new Date(end) : new Date()
        };
        
        const report = errorMonitor.generateErrorReport(timeRange);
        return NextResponse.json(report);

      default:
        return NextResponse.json({ error: 'Invalid type parameter' }, { status: 400 });
    }
  } catch (error) {
    console.error('Health check endpoint error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, service } = body;

    switch (action) {
      case 'reset-circuit-breaker':
        if (!service) {
          return NextResponse.json({ error: 'Service name required' }, { status: 400 });
        }
        
        errorHandler.resetCircuitBreaker(service);
        return NextResponse.json({ message: `Circuit breaker reset for ${service}` });

      case 'clear-metrics':
        errorHandler.clearErrorMetrics();
        return NextResponse.json({ message: 'Error metrics cleared' });

      case 'test-error':
        // For testing error handling system
        const testError = errorHandler.createError(
          'Test error for monitoring system',
          body.category || 'api',
          body.severity || 'medium',
          'TEST_ERROR',
          { test: true, timestamp: new Date() }
        );
        
        // Don't throw, just log for testing
        console.log('Test error created:', testError.message);
        return NextResponse.json({ message: 'Test error logged', error: testError });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Health action endpoint error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}